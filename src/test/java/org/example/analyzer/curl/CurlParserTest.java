package org.example.analyzer.curl;

/**
 * cURL解析器测试类
 * 用于验证cURL命令解析功能
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
public class CurlParserTest {
    
    public static void main(String[] args) {
        testCurlParsing();
    }
    
    public static void testCurlParsing() {
        System.out.println("=== cURL解析器测试 ===");
        
        CurlParser parser = new CurlParser();
        
        // 测试用例1: 简单GET请求
        String curlCommand1 = "curl 'https://api.example.com/data' -H 'Authorization: Bearer token123'";
        testParseSingle(parser, curlCommand1, "test-get");
        
        // 测试用例2: POST请求带数据
        String curlCommand2 = "curl 'https://api.example.com/export' \\\n" +
                "  -X POST \\\n" +
                "  -H 'Accept: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' \\\n" +
                "  -H 'Authorization: Bearer token123' \\\n" +
                "  -H 'Content-Type: application/json' \\\n" +
                "  --data-raw '{\"startTime\":\"2025-07-24\",\"endTime\":\"2025-07-25\"}'";
        testParseSingle(parser, curlCommand2, "test-post");
        
        // 测试用例3: 带引号的URL
        String curlCommand3 = "curl \"https://monitoring.example.com/api/export\" -H \"User-Agent: Mozilla/5.0\"";
        testParseSingle(parser, curlCommand3, "test-quotes");
        
        System.out.println("=== 测试完成 ===");
    }
    
    private static void testParseSingle(CurlParser parser, String curlCommand, String testName) {
        System.out.println("\n--- 测试: " + testName + " ---");
        System.out.println("输入cURL: " + curlCommand.replaceAll("\\s+", " "));
        
        try {
            CurlCommand command = parser.parse(curlCommand, testName);
            
            System.out.println("解析结果:");
            System.out.println("  URL: " + command.getUrl());
            System.out.println("  方法: " + command.getMethod());
            System.out.println("  请求头数量: " + command.getHeaders().size());
            
            if (!command.getHeaders().isEmpty()) {
                System.out.println("  请求头详情:");
                for (String key : command.getHeaders().keySet()) {
                    String value = command.getHeaders().get(key);
                    // 隐藏敏感信息
                    if (key.toLowerCase().contains("authorization")) {
                        value = value.substring(0, Math.min(20, value.length())) + "...";
                    }
                    System.out.println("    " + key + ": " + value);
                }
            }
            
            if (command.getBody() != null && !command.getBody().isEmpty()) {
                System.out.println("  请求体: " + command.getBody());
            }
            
            System.out.println("  有效性: " + (command.isValid() ? "✓ 有效" : "✗ 无效"));
            
        } catch (CurlProcessException e) {
            System.out.println("解析失败: " + e.getDetailedMessage());
        }
    }
}
