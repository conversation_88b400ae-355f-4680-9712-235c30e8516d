# 里程碑设计计划明细层级计算器

## 概述

这个Java实现完全替代了原有的递归CTE SQL功能，用于计算`pam_ctc.milepost_design_plan_detail`表的层级深度和最终数量。

## 核心功能

- ✅ 递归计算层级深度（level_depth）
- ✅ 递归计算最终数量（final_number）
- ✅ 批量更新数据库记录
- ✅ 事务控制确保数据一致性
- ✅ 完整的错误处理和日志记录
- ✅ 支持同步和异步计算
- ✅ 支持批量项目计算

## 类结构

### 1. MilepostDesignPlanDetailHierarchyCalculator
**核心计算器类** - 实现具体的层级计算逻辑

```java
// 基本用法
MilepostDesignPlanDetailHierarchyCalculator calculator = 
    new MilepostDesignPlanDetailHierarchyCalculator();

CalculationResult result = calculator.calculateHierarchyForProject(projectId);
calculator.close();
```

### 2. MilepostDesignPlanDetailHierarchyService
**服务层类** - 提供高级服务功能，支持异步和批量操作

```java
// 服务层用法
MilepostDesignPlanDetailHierarchyService service = 
    new MilepostDesignPlanDetailHierarchyService();

// 同步计算
CalculationResult syncResult = service.calculateHierarchy(projectId);

// 异步计算
CompletableFuture<CalculationResult> asyncResult = 
    service.calculateHierarchyAsync(projectId);

// 批量计算
CompletableFuture<BatchCalculationResult> batchResult = 
    service.calculateHierarchyBatch(projectId1, projectId2, projectId3);

service.shutdown();
```

### 3. MilepostDesignPlanDetailHierarchyUtils
**工具类** - 提供静态方法，最简单的使用方式

```java
// 工具类用法（推荐）
CalculationResult result = 
    MilepostDesignPlanDetailHierarchyUtils.calculateHierarchy(projectId);

// 批量计算
BatchCalculationSummary summary = 
    MilepostDesignPlanDetailHierarchyUtils.calculateHierarchyBatch(
        projectId1, projectId2, projectId3);

// 数据验证
boolean hasData = 
    MilepostDesignPlanDetailHierarchyUtils.hasProjectData(projectId);
```

## 算法逻辑

### 原SQL逻辑
```sql
-- 根节点：level_depth = 1, final_number = number（排除零值）
-- 子节点：level_depth = 父级level_depth + 1, final_number = 父级final_number * 当前有效number
```

### Java实现逻辑
1. **查询数据**: 获取项目的所有相关记录
2. **构建树**: 根据parent_id构建层级树结构
3. **递归计算**: 
   - level_depth = 父级level_depth + 1
   - final_number = 父级final_number * 当前number（如果number为null或0则使用父级final_number）
4. **批量更新**: 将计算结果更新到数据库

## 使用示例

### 示例1: 简单计算（推荐）
```java
public class Example1 {
    public static void main(String[] args) {
        try {
            Long projectId = 1026509072716791808L;
            
            // 使用工具类进行计算
            CalculationResult result = 
                MilepostDesignPlanDetailHierarchyUtils.calculateHierarchy(projectId);
            
            System.out.println("计算完成:");
            System.out.println("- 计算记录数: " + result.getCalculatedCount());
            System.out.println("- 更新记录数: " + result.getUpdatedCount());
            System.out.println("- 计算时间: " + result.getCalculationTime());
            
        } catch (Exception e) {
            System.err.println("计算失败: " + e.getMessage());
        }
    }
}
```

### 示例2: 批量计算
```java
public class Example2 {
    public static void main(String[] args) {
        try {
            Long[] projectIds = {
                1026509072716791808L,
                1026509072716791809L,
                1026509072716791810L
            };
            
            // 批量计算
            BatchCalculationSummary summary = 
                MilepostDesignPlanDetailHierarchyUtils.calculateHierarchyBatch(projectIds);
            
            // 打印详细报告
            summary.printDetailedReport();
            
        } catch (Exception e) {
            System.err.println("批量计算失败: " + e.getMessage());
        }
    }
}
```

### 示例3: 异步计算
```java
public class Example3 {
    public static void main(String[] args) {
        MilepostDesignPlanDetailHierarchyService service = 
            new MilepostDesignPlanDetailHierarchyService();
        
        try {
            Long projectId = 1026509072716791808L;
            
            // 异步计算
            CompletableFuture<CalculationResult> future = 
                service.calculateHierarchyAsync(projectId);
            
            // 可以在这里做其他事情...
            System.out.println("计算进行中...");
            
            // 获取结果
            CalculationResult result = future.get();
            System.out.println("异步计算完成: " + result);
            
        } catch (Exception e) {
            System.err.println("异步计算失败: " + e.getMessage());
        } finally {
            service.shutdown();
        }
    }
}
```

## 配置说明

### 数据库连接配置
在`MilepostDesignPlanDetailHierarchyCalculator`类中修改以下常量：

```java
private static final String JDBC_URL = "******************************************************************************************************************************************";
private static final String DB_USER = "pam_sit";
private static final String DB_PASSWORD = "tqVhr1zM5";
```

### 连接池配置
```java
config.setMaximumPoolSize(10);      // 最大连接数
config.setMinimumIdle(2);           // 最小空闲连接数
config.setIdleTimeout(30000);       // 空闲超时时间
config.setConnectionTimeout(30000); // 连接超时时间
config.setMaxLifetime(1800000);     // 连接最大生存时间
```

## 性能特性

- **批量更新**: 每100条记录执行一次批量更新，提高性能
- **连接池**: 使用HikariCP连接池，高效管理数据库连接
- **事务控制**: 自动事务管理，确保数据一致性
- **内存优化**: 逐步处理数据，避免内存溢出
- **异步支持**: 支持异步计算，不阻塞主线程

## 错误处理

- **数据库连接错误**: 自动重试和连接池管理
- **数据完整性错误**: 事务回滚确保数据一致性
- **计算错误**: 详细的错误日志和异常信息
- **资源泄漏**: 自动资源清理和连接关闭

## 日志配置

使用SLF4J进行日志记录，支持以下日志级别：

- `INFO`: 计算进度和结果信息
- `DEBUG`: 详细的计算过程信息
- `WARN`: 警告信息（如数据异常）
- `ERROR`: 错误信息和异常堆栈

## 注意事项

1. **数据备份**: 执行计算前请备份相关数据
2. **并发控制**: 避免同时对同一项目进行多次计算
3. **资源管理**: 使用完毕后记得关闭Calculator或Service
4. **事务大小**: 大量数据时建议分批处理
5. **数据验证**: 计算前可使用`hasProjectData`验证数据存在性

## 扩展功能

如需扩展功能，可以：

1. 修改`calculateHierarchyRecursive`方法调整计算逻辑
2. 在`ValidationResult`中添加更多数据验证规则
3. 扩展`BatchCalculationResult`添加更多统计信息
4. 添加缓存机制提高重复计算的性能

## 技术依赖

- Java 8+
- HikariCP 4.0.3（数据库连接池）
- MySQL Connector 5.1.49
- SLF4J 1.7.36（日志框架）

## 测试建议

建议创建单元测试验证以下场景：

1. 正常的层级计算
2. 空数据项目的处理
3. 循环引用的检测
4. 大数据量的性能测试
5. 并发计算的安全性测试
