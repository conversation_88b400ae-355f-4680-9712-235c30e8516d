package org.pam.pamCtc;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * 里程碑设计计划明细层级计算工具类
 * 提供静态方法便于直接调用
 */
public class MilepostDesignPlanDetailHierarchyUtils {

    private static final Logger logger = LoggerFactory.getLogger(MilepostDesignPlanDetailHierarchyUtils.class);

    // 私有构造函数，防止实例化
    private MilepostDesignPlanDetailHierarchyUtils() {
        throw new UnsupportedOperationException("工具类不允许实例化");
    }

    /**
     * 计算指定项目的层级结构和最终数量
     * 这是最常用的静态方法，等价于原SQL的功能
     * 
     * @param projectId 项目ID
     * @return 计算结果统计信息
     * @throws RuntimeException 如果计算失败
     */
    public static MilepostDesignPlanDetailHierarchyCalculator.CalculationResult calculateHierarchy(Long projectId) {
        logger.info("使用工具类计算项目 {} 的层级结构", projectId);
        
        MilepostDesignPlanDetailHierarchyCalculator calculator = null;
        try {
            calculator = new MilepostDesignPlanDetailHierarchyCalculator();
            MilepostDesignPlanDetailHierarchyCalculator.CalculationResult result = 
                calculator.calculateHierarchyForProject(projectId);
            
            logger.info("项目 {} 层级计算完成: 计算记录数={}, 更新记录数={}", 
                       projectId, result.getCalculatedCount(), result.getUpdatedCount());
            
            return result;
            
        } catch (Exception e) {
            logger.error("项目 {} 层级计算失败", projectId, e);
            throw new RuntimeException("层级计算失败: " + e.getMessage(), e);
        } finally {
            if (calculator != null) {
                calculator.close();
            }
        }
    }

    /**
     * 批量计算多个项目的层级结构
     * 
     * @param projectIds 项目ID数组
     * @return 批量计算结果
     */
    public static BatchCalculationSummary calculateHierarchyBatch(Long... projectIds) {
        logger.info("使用工具类批量计算 {} 个项目的层级结构", projectIds.length);
        
        BatchCalculationSummary summary = new BatchCalculationSummary();
        MilepostDesignPlanDetailHierarchyCalculator calculator = null;
        
        try {
            calculator = new MilepostDesignPlanDetailHierarchyCalculator();
            
            for (Long projectId : projectIds) {
                try {
                    logger.info("正在计算项目 {} 的层级结构", projectId);
                    MilepostDesignPlanDetailHierarchyCalculator.CalculationResult result = 
                        calculator.calculateHierarchyForProject(projectId);
                    
                    summary.addSuccess(projectId, result);
                    logger.info("项目 {} 计算成功", projectId);
                    
                } catch (Exception e) {
                    logger.error("项目 {} 计算失败", projectId, e);
                    summary.addFailure(projectId, e.getMessage());
                }
            }
            
            logger.info("批量计算完成: 成功={}, 失败={}, 总计={}", 
                       summary.getSuccessCount(), summary.getFailureCount(), summary.getTotalCount());
            
            return summary;
            
        } finally {
            if (calculator != null) {
                calculator.close();
            }
        }
    }

    /**
     * 验证项目是否存在数据
     * 
     * @param projectId 项目ID
     * @return 是否存在数据
     */
    public static boolean hasProjectData(Long projectId) {
        logger.info("验证项目 {} 是否存在数据", projectId);
        
        MilepostDesignPlanDetailHierarchyCalculator calculator = null;
        try {
            calculator = new MilepostDesignPlanDetailHierarchyCalculator();
            MilepostDesignPlanDetailHierarchyCalculator.CalculationResult result = 
                calculator.calculateHierarchyForProject(projectId);
            
            boolean hasData = result.getCalculatedCount() > 0;
            logger.info("项目 {} 数据验证结果: {}", projectId, hasData ? "存在数据" : "无数据");
            
            return hasData;
            
        } catch (Exception e) {
            logger.warn("验证项目 {} 数据时发生错误: {}", projectId, e.getMessage());
            return false;
        } finally {
            if (calculator != null) {
                calculator.close();
            }
        }
    }

    /**
     * 批量计算结果摘要
     */
    public static class BatchCalculationSummary {
        private final java.util.Map<Long, MilepostDesignPlanDetailHierarchyCalculator.CalculationResult> successResults;
        private final java.util.Map<Long, String> failureResults;
        private int totalCalculatedRecords = 0;
        private int totalUpdatedRecords = 0;

        public BatchCalculationSummary() {
            this.successResults = new java.util.HashMap<>();
            this.failureResults = new java.util.HashMap<>();
        }

        public void addSuccess(Long projectId, MilepostDesignPlanDetailHierarchyCalculator.CalculationResult result) {
            successResults.put(projectId, result);
            totalCalculatedRecords += result.getCalculatedCount();
            totalUpdatedRecords += result.getUpdatedCount();
        }

        public void addFailure(Long projectId, String errorMessage) {
            failureResults.put(projectId, errorMessage);
        }

        public java.util.Map<Long, MilepostDesignPlanDetailHierarchyCalculator.CalculationResult> getSuccessResults() {
            return successResults;
        }

        public java.util.Map<Long, String> getFailureResults() {
            return failureResults;
        }

        public int getSuccessCount() {
            return successResults.size();
        }

        public int getFailureCount() {
            return failureResults.size();
        }

        public int getTotalCount() {
            return getSuccessCount() + getFailureCount();
        }

        public int getTotalCalculatedRecords() {
            return totalCalculatedRecords;
        }

        public int getTotalUpdatedRecords() {
            return totalUpdatedRecords;
        }

        /**
         * 获取成功率
         */
        public double getSuccessRate() {
            if (getTotalCount() == 0) return 0.0;
            return (double) getSuccessCount() / getTotalCount() * 100;
        }

        /**
         * 打印详细报告
         */
        public void printDetailedReport() {
            System.out.println("=== 批量计算详细报告 ===");
            System.out.println("总项目数: " + getTotalCount());
            System.out.println("成功项目数: " + getSuccessCount());
            System.out.println("失败项目数: " + getFailureCount());
            System.out.println("成功率: " + String.format("%.2f%%", getSuccessRate()));
            System.out.println("总计算记录数: " + getTotalCalculatedRecords());
            System.out.println("总更新记录数: " + getTotalUpdatedRecords());
            
            if (!successResults.isEmpty()) {
                System.out.println("\n--- 成功项目详情 ---");
                for (java.util.Map.Entry<Long, MilepostDesignPlanDetailHierarchyCalculator.CalculationResult> entry : successResults.entrySet()) {
                    System.out.println("项目 " + entry.getKey() + ": " + entry.getValue());
                }
            }
            
            if (!failureResults.isEmpty()) {
                System.out.println("\n--- 失败项目详情 ---");
                for (java.util.Map.Entry<Long, String> entry : failureResults.entrySet()) {
                    System.out.println("项目 " + entry.getKey() + ": " + entry.getValue());
                }
            }
            
            System.out.println("========================");
        }

        @Override
        public String toString() {
            return "BatchCalculationSummary{" +
                    "successCount=" + getSuccessCount() +
                    ", failureCount=" + getFailureCount() +
                    ", totalCount=" + getTotalCount() +
                    ", successRate=" + String.format("%.2f%%", getSuccessRate()) +
                    ", totalCalculatedRecords=" + totalCalculatedRecords +
                    ", totalUpdatedRecords=" + totalUpdatedRecords +
                    '}';
        }
    }

    /**
     * 主方法 - 演示工具类的使用
     */
    public static void main(String[] args) {
        try {
            // 演示单个项目计算
            Long projectId = 1026509072716791808L;
            
            System.out.println("=== 单个项目计算演示 ===");
            MilepostDesignPlanDetailHierarchyCalculator.CalculationResult result = 
                calculateHierarchy(projectId);
            System.out.println("计算结果: " + result);
            
            // 演示数据验证
            System.out.println("\n=== 数据验证演示 ===");
            boolean hasData = hasProjectData(projectId);
            System.out.println("项目 " + projectId + " 是否有数据: " + hasData);
            
            // 演示批量计算
            System.out.println("\n=== 批量计算演示 ===");
            BatchCalculationSummary summary = calculateHierarchyBatch(projectId);
            summary.printDetailedReport();
            
        } catch (Exception e) {
            logger.error("演示执行失败", e);
            System.err.println("演示失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
