package org.pam.pamCtc;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * 里程碑设计计划明细层级计算服务
 * 提供异步和同步的层级计算服务
 */
public class MilepostDesignPlanDetailHierarchyService {

    private static final Logger logger = LoggerFactory.getLogger(MilepostDesignPlanDetailHierarchyService.class);

    private final MilepostDesignPlanDetailHierarchyCalculator calculator;
    private final ExecutorService executorService;

    public MilepostDesignPlanDetailHierarchyService() {
        this.calculator = new MilepostDesignPlanDetailHierarchyCalculator();
        this.executorService = Executors.newFixedThreadPool(5);
    }

    /**
     * 同步计算指定项目的层级结构
     * 
     * @param projectId 项目ID
     * @return 计算结果
     */
    public MilepostDesignPlanDetailHierarchyCalculator.CalculationResult calculateHierarchy(Long projectId) {
        logger.info("开始同步计算项目 {} 的层级结构", projectId);
        
        try {
            MilepostDesignPlanDetailHierarchyCalculator.CalculationResult result = 
                calculator.calculateHierarchyForProject(projectId);
            
            logger.info("项目 {} 层级计算完成: {}", projectId, result);
            return result;
            
        } catch (Exception e) {
            logger.error("项目 {} 层级计算失败", projectId, e);
            throw new RuntimeException("层级计算失败: " + e.getMessage(), e);
        }
    }

    /**
     * 异步计算指定项目的层级结构
     * 
     * @param projectId 项目ID
     * @return CompletableFuture包装的计算结果
     */
    public CompletableFuture<MilepostDesignPlanDetailHierarchyCalculator.CalculationResult> calculateHierarchyAsync(Long projectId) {
        logger.info("开始异步计算项目 {} 的层级结构", projectId);
        
        return CompletableFuture.supplyAsync(() -> {
            try {
                return calculator.calculateHierarchyForProject(projectId);
            } catch (Exception e) {
                logger.error("异步计算项目 {} 层级结构失败", projectId, e);
                throw new RuntimeException("异步层级计算失败: " + e.getMessage(), e);
            }
        }, executorService);
    }

    /**
     * 批量计算多个项目的层级结构
     * 
     * @param projectIds 项目ID数组
     * @return 每个项目的计算结果
     */
    public CompletableFuture<BatchCalculationResult> calculateHierarchyBatch(Long... projectIds) {
        logger.info("开始批量计算 {} 个项目的层级结构", projectIds.length);
        
        return CompletableFuture.supplyAsync(() -> {
            BatchCalculationResult batchResult = new BatchCalculationResult();
            
            for (Long projectId : projectIds) {
                try {
                    logger.info("正在计算项目 {} 的层级结构", projectId);
                    MilepostDesignPlanDetailHierarchyCalculator.CalculationResult result = 
                        calculator.calculateHierarchyForProject(projectId);
                    
                    batchResult.addSuccessResult(projectId, result);
                    logger.info("项目 {} 计算成功", projectId);
                    
                } catch (Exception e) {
                    logger.error("项目 {} 计算失败", projectId, e);
                    batchResult.addFailureResult(projectId, e.getMessage());
                }
            }
            
            logger.info("批量计算完成，成功: {}, 失败: {}", 
                       batchResult.getSuccessCount(), batchResult.getFailureCount());
            
            return batchResult;
        }, executorService);
    }

    /**
     * 批量计算结果
     */
    public static class BatchCalculationResult {
        private final java.util.Map<Long, MilepostDesignPlanDetailHierarchyCalculator.CalculationResult> successResults;
        private final java.util.Map<Long, String> failureResults;

        public BatchCalculationResult() {
            this.successResults = new java.util.HashMap<>();
            this.failureResults = new java.util.HashMap<>();
        }

        public void addSuccessResult(Long projectId, MilepostDesignPlanDetailHierarchyCalculator.CalculationResult result) {
            successResults.put(projectId, result);
        }

        public void addFailureResult(Long projectId, String errorMessage) {
            failureResults.put(projectId, errorMessage);
        }

        public java.util.Map<Long, MilepostDesignPlanDetailHierarchyCalculator.CalculationResult> getSuccessResults() {
            return successResults;
        }

        public java.util.Map<Long, String> getFailureResults() {
            return failureResults;
        }

        public int getSuccessCount() {
            return successResults.size();
        }

        public int getFailureCount() {
            return failureResults.size();
        }

        public int getTotalCount() {
            return getSuccessCount() + getFailureCount();
        }

        @Override
        public String toString() {
            return "BatchCalculationResult{" +
                    "successCount=" + getSuccessCount() +
                    ", failureCount=" + getFailureCount() +
                    ", totalCount=" + getTotalCount() +
                    '}';
        }
    }

    /**
     * 验证项目数据完整性
     * 
     * @param projectId 项目ID
     * @return 验证结果
     */
    public ValidationResult validateProjectData(Long projectId) {
        logger.info("开始验证项目 {} 的数据完整性", projectId);
        
        // 这里可以添加数据验证逻辑
        // 例如：检查是否存在循环引用、孤立节点等
        
        return new ValidationResult(projectId, true, "数据验证通过");
    }

    /**
     * 数据验证结果
     */
    public static class ValidationResult {
        private final Long projectId;
        private final boolean isValid;
        private final String message;

        public ValidationResult(Long projectId, boolean isValid, String message) {
            this.projectId = projectId;
            this.isValid = isValid;
            this.message = message;
        }

        public Long getProjectId() { return projectId; }
        public boolean isValid() { return isValid; }
        public String getMessage() { return message; }

        @Override
        public String toString() {
            return "ValidationResult{" +
                    "projectId=" + projectId +
                    ", isValid=" + isValid +
                    ", message='" + message + '\'' +
                    '}';
        }
    }

    /**
     * 关闭服务，释放资源
     */
    public void shutdown() {
        logger.info("正在关闭层级计算服务...");
        
        if (executorService != null && !executorService.isShutdown()) {
            executorService.shutdown();
            try {
                if (!executorService.awaitTermination(60, java.util.concurrent.TimeUnit.SECONDS)) {
                    executorService.shutdownNow();
                }
            } catch (InterruptedException e) {
                executorService.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        
        if (calculator != null) {
            calculator.close();
        }
        
        logger.info("层级计算服务已关闭");
    }

    /**
     * 主方法 - 用于测试服务
     */
    public static void main(String[] args) {
        MilepostDesignPlanDetailHierarchyService service = new MilepostDesignPlanDetailHierarchyService();
        
        try {
            // 测试单个项目计算
            Long projectId = 1026509072716791808L;
            
            logger.info("测试同步计算...");
            MilepostDesignPlanDetailHierarchyCalculator.CalculationResult syncResult = 
                service.calculateHierarchy(projectId);
            System.out.println("同步计算结果: " + syncResult);
            
            // 测试异步计算
            logger.info("测试异步计算...");
            CompletableFuture<MilepostDesignPlanDetailHierarchyCalculator.CalculationResult> asyncFuture = 
                service.calculateHierarchyAsync(projectId);
            
            MilepostDesignPlanDetailHierarchyCalculator.CalculationResult asyncResult = asyncFuture.get();
            System.out.println("异步计算结果: " + asyncResult);
            
            // 测试批量计算
            logger.info("测试批量计算...");
            CompletableFuture<BatchCalculationResult> batchFuture = 
                service.calculateHierarchyBatch(projectId);
            
            BatchCalculationResult batchResult = batchFuture.get();
            System.out.println("批量计算结果: " + batchResult);
            
        } catch (Exception e) {
            logger.error("测试执行失败", e);
            System.err.println("测试失败: " + e.getMessage());
        } finally {
            service.shutdown();
        }
    }
}
