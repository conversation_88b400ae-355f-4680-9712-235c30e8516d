package org.example.analyzer;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 接口优先级分析器 - 主程序类
 * <p>
 * 使用方法：
 * 1. 修改 INPUT_DIRECTORY 为您的Excel文件目录路径
 * 2. 修改 OUTPUT_FILE_PATH 为输出Excel文件路径
 * 3. 运行 main 方法
 */
public class InterfacePriorityAnalyzer {

    // ==================== 配置参数 ====================
    /**
     * 输入Excel文件目录路径
     */
    private static final String INPUT_DIRECTORY = "D:\\Users\\ex_wuyh42.CN\\Desktop\\生产环境-慢接口\\auto-export";

    /**
     * 输出Excel报告文件路径
     */
    private static final String OUTPUT_FILE_PATH = "D:\\Users\\ex_wuyh42.CN\\Desktop\\生产环境-慢接口\\分析报告\\接口优先级分析报告_" +
            LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";

    public static void main(String[] args) {
        System.out.println("=================== 接口优先级分析器 ===================");
        System.out.println("开始时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println("输入目录: " + INPUT_DIRECTORY);
        System.out.println("输出文件: " + OUTPUT_FILE_PATH);
        System.out.println("========================================================");

        try {
            // 1. 读取Excel文件数据
            System.out.println("\n[步骤1] 读取Excel文件数据...");
            ExcelMetricsReader reader = new ExcelMetricsReader();
            List<InterfaceMetrics> allMetrics = reader.readFromDirectory(INPUT_DIRECTORY);

            if (allMetrics.isEmpty()) {
                System.err.println("错误: 没有读取到任何接口数据，请检查输入目录和文件格式");
                return;
            }

            System.out.println("成功读取 " + allMetrics.size() + " 个接口的数据");

            // 2. 计算优先级得分并排序
            System.out.println("\n[步骤2] 计算优先级得分并排序...");
            PriorityCalculator calculator = new PriorityCalculator();
            List<InterfaceMetrics> sortedMetrics = calculator.calculateAndSortPriorities(allMetrics);

            // 3. 获取全局TOP N接口
            List<InterfaceMetrics> topNMetrics = calculator.getTopNInterfaces(sortedMetrics, PriorityConfig.TOP_N_COUNT);

            System.out.println("全局优先级计算完成，TOP " + PriorityConfig.TOP_N_COUNT + " 接口已确定");

            // 4. 计算每个服务的TOP N接口
            System.out.println("\n[步骤3] 计算各服务TOP " + PriorityConfig.TOP_N_COUNT + " 接口...");
            Map<String, List<InterfaceMetrics>> serviceTopNMap = calculator.calculateServiceTopN(reader.getServiceMetricsMap(), PriorityConfig.TOP_N_COUNT);

            System.out.println("各服务TOP " + PriorityConfig.TOP_N_COUNT + " 接口计算完成");

            // 5. 输出控制台报告
            System.out.println("\n[步骤4] 生成控制台报告...");
            printConsoleReport(sortedMetrics, topNMetrics, serviceTopNMap);

            // 6. 生成Excel报告
            System.out.println("\n[步骤5] 生成Excel报告...");
            ExcelReportGenerator reportGenerator = new ExcelReportGenerator();
            reportGenerator.generateReport(sortedMetrics, topNMetrics, serviceTopNMap, OUTPUT_FILE_PATH);

            // 7. 生成Markdown报告
            System.out.println("\n[步骤6] 生成Markdown报告...");
            MarkdownReportGenerator markdownGenerator = new MarkdownReportGenerator();
            markdownGenerator.generateReport(sortedMetrics, topNMetrics, serviceTopNMap, OUTPUT_FILE_PATH);

            // 8. 输出汇总信息
            System.out.println("\n=================== 分析完成 ===================");
            System.out.println("结束时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            System.out.println("Excel报告已保存至: " + OUTPUT_FILE_PATH);
            System.out.println("Markdown报告已保存至: " + OUTPUT_FILE_PATH.replace(".xlsx", ".md"));
            System.out.println("===============================================");

        } catch (Exception e) {
            System.err.println("分析过程中发生错误: " + e.getMessage());
            System.err.println("详细错误信息: " + e);
        }
    }

    /**
     * 打印控制台报告
     */
    private static void printConsoleReport(List<InterfaceMetrics> allMetrics, List<InterfaceMetrics> topNMetrics,
                                          Map<String, List<InterfaceMetrics>> serviceTopNMap) {
        System.out.println("\n=================== 接口优先级分析报告 ===================");
        System.out.println("分析时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println("总接口数: " + allMetrics.size() + " 个");
        System.out.println("分析周期: 15天");

        // 统计优先级分布
        long highPriorityCount = 0;
        long mediumPriorityCount = 0;
        for (InterfaceMetrics metric : allMetrics) {
            if (metric.getFinalPriorityScore() >= PriorityConfig.HIGH_PRIORITY_THRESHOLD) {
                highPriorityCount++;
            } else if (metric.getFinalPriorityScore() >= PriorityConfig.MEDIUM_PRIORITY_THRESHOLD) {
                mediumPriorityCount++;
            }
        }
        long lowPriorityCount = allMetrics.size() - highPriorityCount - mediumPriorityCount;

        // 统计被关键字过滤的接口数量
        long filteredCount = 0;
        for (InterfaceMetrics metric : allMetrics) {
            if (metric.getFinalPriorityScore() == PriorityConfig.LOW_PRIORITY_SCORE) {
                filteredCount++;
            }
        }

        System.out.println("\n优先级分布:");
        System.out.println("  高优先级(≥8.0分): " + highPriorityCount + " 个");
        System.out.println("  中优先级(6.0-7.9分): " + mediumPriorityCount + " 个");
        System.out.println("  低优先级(<6.0分): " + lowPriorityCount + " 个");
        if (filteredCount > 0) {
            System.out.println("  关键字过滤: " + filteredCount + " 个接口被降权");
        }

        System.out.println("\n=================== TOP " + PriorityConfig.TOP_N_COUNT + " 优先修复接口 ===================");
        System.out.printf("%-4s %-10s %-40s %-6s %-8s %-10s %-8s %-12s%n",
                "排名", "服务模块", "接口地址", "综合得分", "调用次数", "平均RT(ms)", "错误率(%)", "主要问题");
        System.out.println(String.join("", Collections.nCopies(120, "─")));
        for (InterfaceMetrics metrics : topNMetrics) {
            String url = metrics.getUrl();
            if (url.length() > 38) {
                url = url.substring(0, 35) + "...";
            }

            String mainIssue = generateMainIssue(metrics);
            if (mainIssue.length() > 10) {
                mainIssue = mainIssue.substring(0, 10) + "...";
            }

            System.out.printf("%-4d %-10s %-40s %-6.2f %-8d %-10.0f %-8.2f %-12s%n",
                    metrics.getPriorityRank(),
                    metrics.getServiceName(),
                    url,
                    metrics.getFinalPriorityScore(),
                    metrics.getCallCount(),
                    metrics.getAvgResponseTime(),
                    metrics.getErrorRate() * 100,
                    mainIssue);
        }

        System.out.println(String.join("", Collections.nCopies(120, "─")));

        // 输出详细的TOP 3接口信息
        System.out.println("\n=================== TOP 3 接口详细信息 ===================");
        for (int i = 0; i < Math.min(3, topNMetrics.size()); i++) {
            InterfaceMetrics metrics = topNMetrics.get(i);
            System.out.println("\n【第" + (i + 1) + "名】" + metrics.getServiceName() + " - " + metrics.getUrl());
            System.out.println("  综合得分: " + String.format("%.2f", metrics.getFinalPriorityScore()) + " 分");
            System.out.println("  调用统计: " + metrics.getCallCount() + " 次调用，" + metrics.getErrorCount() + " 次错误");
            System.out.println("  性能指标: 平均RT " + String.format("%.0f", metrics.getAvgResponseTime()) + "ms，最慢 " + String.format("%.0f", metrics.getMaxResponseTime()) + "ms");
            System.out.println("  时间分布: 10s以上 " + metrics.getTimeOver10s() + " 次 (" + String.format("%.1f", metrics.getOver10sRatio() * 100) + "%)");
            System.out.println("  得分详情: 业务影响 " + String.format("%.2f", metrics.getBusinessImpactScore()) +
                    ", 性能严重度 " + String.format("%.2f", metrics.getPerformanceSeverityScore()) +
                    ", 稳定性风险 " + String.format("%.2f", metrics.getStabilityRiskScore()) +
                    ", 资源消耗 " + String.format("%.2f", metrics.getResourceConsumptionScore()));
            if (metrics.hasRecentError()) {
                String error = metrics.getRecentError();
                if (error.length() > 100) {
                    error = error.substring(0, 100) + "...";
                }
                System.out.println("  最近错误: " + error);
            }
        }

        // 输出各服务TOP N接口信息
        if (serviceTopNMap != null && !serviceTopNMap.isEmpty()) {
            System.out.println("\n=================== 各服务TOP " + PriorityConfig.TOP_N_COUNT + " 接口汇总 ===================");

            for (Map.Entry<String, List<InterfaceMetrics>> entry : serviceTopNMap.entrySet()) {
                String serviceName = entry.getKey();
                List<InterfaceMetrics> serviceTopN = entry.getValue();

                System.out.println("\n【" + serviceName + " 服务】TOP " + PriorityConfig.TOP_N_COUNT + " 接口:");
                System.out.printf("%-4s %-40s %-6s %-8s %-10s %-8s%n",
                        "排名", "接口地址", "综合得分", "调用次数", "平均RT(ms)", "错误率(%)");
                System.out.println(String.join("", Collections.nCopies(80, "─")));

                for (InterfaceMetrics metrics : serviceTopN) {
                    String url = metrics.getUrl();
                    if (url.length() > 38) {
                        url = url.substring(0, 35) + "...";
                    }

                    System.out.printf("%-4d %-40s %-6.2f %-8d %-10.0f %-8.2f%n",
                            metrics.getPriorityRank(),
                            url,
                            metrics.getFinalPriorityScore(),
                            metrics.getCallCount(),
                            metrics.getAvgResponseTime(),
                            metrics.getErrorRate() * 100);
                }
            }
        }

        System.out.println("\n========================================================");
    }

    /**
     * 生成主要问题描述
     */
    private static String generateMainIssue(InterfaceMetrics metrics) {
        StringBuilder issues = new StringBuilder();

        if (metrics.getAvgResponseTime() > 30000) {
            issues.append("极高RT ");
        } else if (metrics.getAvgResponseTime() > 10000) {
            issues.append("高RT ");
        } else if (metrics.getAvgResponseTime() > 5000) {
            issues.append("慢RT ");
        }

        if (metrics.getErrorRate() > 0.05) {
            issues.append("高错误率 ");
        } else if (metrics.getErrorRate() > 0.01) {
            issues.append("错误率高 ");
        }

        if (metrics.hasRecentError()) {
            String error = metrics.getRecentError().toLowerCase();
            if (error.contains("timeout") || error.contains("timed out")) {
                issues.append("超时 ");
            } else if (error.contains("connection")) {
                issues.append("连接异常 ");
            } else {
                issues.append("异常 ");
            }
        }

        if (metrics.getOver10sRatio() > 0.5) {
            issues.append("慢请求多 ");
        } else if (metrics.getOver10sRatio() > 0.2) {
            issues.append("有慢请求 ");
        }

        if (metrics.getCallCount() > 5000) {
            issues.append("高频 ");
        } else if (metrics.getCallCount() > 1000) {
            issues.append("中频 ");
        }

        return issues.length() > 0 ? issues.toString().trim() : "性能问题";
    }
}