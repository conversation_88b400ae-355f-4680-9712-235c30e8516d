package org.example.analyzer;

/**
 * 接口性能指标数据模型
 */
public class InterfaceMetrics {
    private String url;                    // 接口地址
    private String method;                 // HTTP方法
    private int callCount;                 // 调用次数
    private int errorCount;                // 错误数
    private double avgResponseTime;        // 平均响应时间(ms)
    private double totalTime;              // 总时间(ms)
    private int maxConcurrency;            // 最大并发
    private double maxResponseTime;        // 最慢响应时间(ms)
    private String recentError;            // 最近错误
    private int time0To10ms;               // 0-10ms响应时间次数
    private int time10To100ms;             // 10-100ms响应时间次数
    private int time100To500ms;            // 100-500ms响应时间次数
    private int time500To1000ms;           // 500-1000ms响应时间次数
    private int time1To10s;                // 1-10s响应时间次数
    private int timeOver10s;               // 10s以上响应时间次数
    private long maxResponseSize;          // 最大响应体大小(Bytes)
    private String tp99;                   // TP99
    private String tp95;                   // TP95
    private String tp90;                   // TP90
    private String callSource;             // 调用源
    private int businessExceptionCount;    // 业务异常次数
    private String serviceName;            // 服务名称
    
    // 计算得出的指标
    private double businessImpactScore;    // 业务影响得分
    private double performanceSeverityScore; // 性能严重度得分
    private double stabilityRiskScore;     // 稳定性风险得分
    private double resourceConsumptionScore; // 资源消耗得分
    private double finalPriorityScore;     // 最终优先级得分
    private int priorityRank;              // 优先级排名

    // 构造函数
    public InterfaceMetrics() {}

    // Getter和Setter方法
    public String getUrl() { return url; }
    public void setUrl(String url) { this.url = url; }

    public String getMethod() { return method; }
    public void setMethod(String method) { this.method = method; }

    public int getCallCount() { return callCount; }
    public void setCallCount(int callCount) { this.callCount = callCount; }

    public int getErrorCount() { return errorCount; }
    public void setErrorCount(int errorCount) { this.errorCount = errorCount; }

    public double getAvgResponseTime() { return avgResponseTime; }
    public void setAvgResponseTime(double avgResponseTime) { this.avgResponseTime = avgResponseTime; }

    public double getTotalTime() { return totalTime; }
    public void setTotalTime(double totalTime) { this.totalTime = totalTime; }

    public int getMaxConcurrency() { return maxConcurrency; }
    public void setMaxConcurrency(int maxConcurrency) { this.maxConcurrency = maxConcurrency; }

    public double getMaxResponseTime() { return maxResponseTime; }
    public void setMaxResponseTime(double maxResponseTime) { this.maxResponseTime = maxResponseTime; }

    public String getRecentError() { return recentError; }
    public void setRecentError(String recentError) { this.recentError = recentError; }

    public int getTime0To10ms() { return time0To10ms; }
    public void setTime0To10ms(int time0To10ms) { this.time0To10ms = time0To10ms; }

    public int getTime10To100ms() { return time10To100ms; }
    public void setTime10To100ms(int time10To100ms) { this.time10To100ms = time10To100ms; }

    public int getTime100To500ms() { return time100To500ms; }
    public void setTime100To500ms(int time100To500ms) { this.time100To500ms = time100To500ms; }

    public int getTime500To1000ms() { return time500To1000ms; }
    public void setTime500To1000ms(int time500To1000ms) { this.time500To1000ms = time500To1000ms; }

    public int getTime1To10s() { return time1To10s; }
    public void setTime1To10s(int time1To10s) { this.time1To10s = time1To10s; }

    public int getTimeOver10s() { return timeOver10s; }
    public void setTimeOver10s(int timeOver10s) { this.timeOver10s = timeOver10s; }

    public long getMaxResponseSize() { return maxResponseSize; }
    public void setMaxResponseSize(long maxResponseSize) { this.maxResponseSize = maxResponseSize; }

    public String getTp99() { return tp99; }
    public void setTp99(String tp99) { this.tp99 = tp99; }

    public String getTp95() { return tp95; }
    public void setTp95(String tp95) { this.tp95 = tp95; }

    public String getTp90() { return tp90; }
    public void setTp90(String tp90) { this.tp90 = tp90; }

    public String getCallSource() { return callSource; }
    public void setCallSource(String callSource) { this.callSource = callSource; }

    public int getBusinessExceptionCount() { return businessExceptionCount; }
    public void setBusinessExceptionCount(int businessExceptionCount) { this.businessExceptionCount = businessExceptionCount; }

    public String getServiceName() { return serviceName; }
    public void setServiceName(String serviceName) { this.serviceName = serviceName; }

    public double getBusinessImpactScore() { return businessImpactScore; }
    public void setBusinessImpactScore(double businessImpactScore) { this.businessImpactScore = businessImpactScore; }

    public double getPerformanceSeverityScore() { return performanceSeverityScore; }
    public void setPerformanceSeverityScore(double performanceSeverityScore) { this.performanceSeverityScore = performanceSeverityScore; }

    public double getStabilityRiskScore() { return stabilityRiskScore; }
    public void setStabilityRiskScore(double stabilityRiskScore) { this.stabilityRiskScore = stabilityRiskScore; }

    public double getResourceConsumptionScore() { return resourceConsumptionScore; }
    public void setResourceConsumptionScore(double resourceConsumptionScore) { this.resourceConsumptionScore = resourceConsumptionScore; }

    public double getFinalPriorityScore() { return finalPriorityScore; }
    public void setFinalPriorityScore(double finalPriorityScore) { this.finalPriorityScore = finalPriorityScore; }

    public int getPriorityRank() { return priorityRank; }
    public void setPriorityRank(int priorityRank) { this.priorityRank = priorityRank; }

    /**
     * 计算错误率
     */
    public double getErrorRate() {
        return callCount > 0 ? (double) errorCount / callCount : 0.0;
    }

    /**
     * 计算10s以上响应时间占比
     */
    public double getOver10sRatio() {
        return callCount > 0 ? (double) timeOver10s / callCount : 0.0;
    }

    /**
     * 判断是否有错误信息
     */
    public boolean hasRecentError() {
        return recentError != null && !recentError.trim().isEmpty() && !recentError.trim().equals(" ");
    }

    @Override
    public String toString() {
        return String.format("InterfaceMetrics{url='%s', service='%s', callCount=%d, avgRT=%.2f, errorRate=%.2f%%, finalScore=%.2f}", 
                url, serviceName, callCount, avgResponseTime, getErrorRate() * 100, finalPriorityScore);
    }
}