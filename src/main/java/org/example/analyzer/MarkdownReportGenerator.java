package org.example.analyzer;

import java.io.FileWriter;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * Markdown报告生成器
 * 负责生成与Excel类似内容的Markdown格式分析报告
 */
public class MarkdownReportGenerator {
    
    /**
     * 生成完整的Markdown分析报告
     */
    public void generateReport(List<InterfaceMetrics> allMetrics, List<InterfaceMetrics> topNMetrics, 
                              Map<String, List<InterfaceMetrics>> serviceTopNMap, String outputPath) {
        
        // 生成Markdown文件路径
        String markdownPath = outputPath.replace(".xlsx", ".md");
        
        try (FileWriter writer = new FileWriter(markdownPath, false)) {
            
            // 写入报告标题和基本信息
            writeReportHeader(writer, allMetrics, topNMetrics);
            
            // 写入全局TOP N接口
            writeGlobalTopN(writer, topNMetrics);
            
            // 写入各服务TOP N接口
            if (serviceTopNMap != null && !serviceTopNMap.isEmpty()) {
                writeServiceTopN(writer, serviceTopNMap);
            }
            
            // 写入汇总分析
            writeSummaryAnalysis(writer, allMetrics, topNMetrics, serviceTopNMap);
            
            // 写入完整数据（前50条）
            writeAllDataSample(writer, allMetrics);
            
            System.out.println("Markdown报告已生成: " + markdownPath);
            
        } catch (IOException e) {
            System.err.println("生成Markdown报告失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 写入报告标题和基本信息
     */
    private void writeReportHeader(FileWriter writer, List<InterfaceMetrics> allMetrics, 
                                  List<InterfaceMetrics> topNMetrics) throws IOException {
        
        writer.write("# 接口优先级分析报告\n\n");
        
        // 基本信息
        writer.write("## 📋 基本信息\n\n");
        writer.write("| 项目 | 值 |\n");
        writer.write("|------|----|\n");
        writer.write("| 生成时间 | " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + " |\n");
        writer.write("| 总接口数 | " + allMetrics.size() + " 个 |\n");
        writer.write("| 分析周期 | 15天 |\n");
        writer.write("| TOP接口数 | " + PriorityConfig.TOP_N_COUNT + " 个 |\n\n");
        
        // 优先级分布统计
        long highPriorityCount = 0;
        long mediumPriorityCount = 0;
        for (InterfaceMetrics metric : allMetrics) {
            if (metric.getFinalPriorityScore() >= PriorityConfig.HIGH_PRIORITY_THRESHOLD) {
                highPriorityCount++;
            } else if (metric.getFinalPriorityScore() >= PriorityConfig.MEDIUM_PRIORITY_THRESHOLD) {
                mediumPriorityCount++;
            }
        }
        long lowPriorityCount = allMetrics.size() - highPriorityCount - mediumPriorityCount;
        
        writer.write("## 📊 优先级分布\n\n");
        writer.write("| 优先级 | 数量 | 占比 |\n");
        writer.write("|--------|------|------|\n");
        writer.write("| 高优先级(≥8.0分) | " + highPriorityCount + " | " + 
                    String.format("%.1f%%", (double)highPriorityCount / allMetrics.size() * 100) + " |\n");
        writer.write("| 中优先级(6.0-7.9分) | " + mediumPriorityCount + " | " + 
                    String.format("%.1f%%", (double)mediumPriorityCount / allMetrics.size() * 100) + " |\n");
        writer.write("| 低优先级(<6.0分) | " + lowPriorityCount + " | " + 
                    String.format("%.1f%%", (double)lowPriorityCount / allMetrics.size() * 100) + " |\n\n");
    }
    
    /**
     * 写入全局TOP N接口
     */
    private void writeGlobalTopN(FileWriter writer, List<InterfaceMetrics> topNMetrics) throws IOException {
        
        writer.write("## 🚨 全局TOP " + PriorityConfig.TOP_N_COUNT + " 优先修复接口\n\n");
        writer.write("> 跨所有服务的最高优先级接口，需要立即关注和处理\n\n");
        
        // 表格标题
        writer.write("| 排名 | 服务模块 | 接口地址 | HTTP方法 | 综合得分 | 主要问题 | 调用次数 | 错误率(%) | 平均RT(ms) | 最慢RT(ms) |\n");
        writer.write("|------|----------|----------|----------|----------|----------|----------|-----------|------------|------------|\n");
        
        // 填充数据
        for (InterfaceMetrics metrics : topNMetrics) {
            String priorityIcon = getPriorityIcon(metrics.getFinalPriorityScore());
            String mainIssue = generateMainIssue(metrics);
            
            writer.write(String.format("| %s %d | %s | `%s` | %s | **%.2f** | %s | %d | %.2f%% | %.0f | %.0f |\n",
                    priorityIcon,
                    metrics.getPriorityRank(),
                    escapeMarkdown(metrics.getServiceName()),
                    escapeMarkdown(metrics.getUrl()),
                    metrics.getMethod(),
                    metrics.getFinalPriorityScore(),
                    mainIssue,
                    metrics.getCallCount(),
                    metrics.getErrorRate() * 100,
                    metrics.getAvgResponseTime(),
                    metrics.getMaxResponseTime()));
        }
        
        writer.write("\n");
        
        // 详细信息
        writer.write("### 🔍 TOP 3 接口详细分析\n\n");
        for (int i = 0; i < Math.min(3, topNMetrics.size()); i++) {
            InterfaceMetrics metrics = topNMetrics.get(i);
            writeDetailedAnalysis(writer, metrics, i + 1);
        }
    }
    
    /**
     * 写入各服务TOP N接口
     */
    private void writeServiceTopN(FileWriter writer, Map<String, List<InterfaceMetrics>> serviceTopNMap) throws IOException {
        
        writer.write("## 🏢 各服务TOP " + PriorityConfig.TOP_N_COUNT + " 接口\n\n");
        writer.write("> 每个服务内部的优先级排序，便于各团队重点关注\n\n");
        
        for (Map.Entry<String, List<InterfaceMetrics>> entry : serviceTopNMap.entrySet()) {
            String serviceName = entry.getKey();
            List<InterfaceMetrics> serviceTopN = entry.getValue();
            
            writer.write("### 📦 " + escapeMarkdown(serviceName) + " 服务\n\n");
            
            // 表格标题
            writer.write("| 服务内排名 | 接口地址 | 综合得分 | 调用次数 | 错误率(%) | 平均RT(ms) | 主要问题 |\n");
            writer.write("|------------|----------|----------|----------|-----------|------------|----------|\n");
            
            // 填充数据
            for (InterfaceMetrics metrics : serviceTopN) {
                String priorityIcon = getPriorityIcon(metrics.getFinalPriorityScore());
                String mainIssue = generateMainIssue(metrics);
                
                writer.write(String.format("| %s %d | `%s` | **%.2f** | %d | %.2f%% | %.0f | %s |\n",
                        priorityIcon,
                        metrics.getPriorityRank(),
                        escapeMarkdown(metrics.getUrl()),
                        metrics.getFinalPriorityScore(),
                        metrics.getCallCount(),
                        metrics.getErrorRate() * 100,
                        metrics.getAvgResponseTime(),
                        mainIssue));
            }
            
            writer.write("\n");
        }
    }
    
    /**
     * 写入汇总分析
     */
    private void writeSummaryAnalysis(FileWriter writer, List<InterfaceMetrics> allMetrics, 
                                     List<InterfaceMetrics> topNMetrics, 
                                     Map<String, List<InterfaceMetrics>> serviceTopNMap) throws IOException {
        
        writer.write("## 📈 汇总分析\n\n");
        
        // 性能问题统计
        writer.write("### 🎯 性能问题统计\n\n");
        
        int slowInterfaces = 0;
        int errorInterfaces = 0;
        int highConcurrencyInterfaces = 0;
        int largeResponseInterfaces = 0;
        
        for (InterfaceMetrics metrics : allMetrics) {
            if (metrics.getAvgResponseTime() > 5000) slowInterfaces++;
            if (metrics.getErrorRate() > 0.01) errorInterfaces++;
            if (metrics.getMaxConcurrency() > 5) highConcurrencyInterfaces++;
            if (metrics.getMaxResponseSize() > 1024 * 1024) largeResponseInterfaces++;
        }
        
        writer.write("| 问题类型 | 接口数量 | 占比 |\n");
        writer.write("|----------|----------|------|\n");
        writer.write("| 响应时间>5秒 | " + slowInterfaces + " | " + 
                    String.format("%.1f%%", (double)slowInterfaces / allMetrics.size() * 100) + " |\n");
        writer.write("| 错误率>1% | " + errorInterfaces + " | " + 
                    String.format("%.1f%%", (double)errorInterfaces / allMetrics.size() * 100) + " |\n");
        writer.write("| 高并发(>5) | " + highConcurrencyInterfaces + " | " + 
                    String.format("%.1f%%", (double)highConcurrencyInterfaces / allMetrics.size() * 100) + " |\n");
        writer.write("| 大响应体(>1MB) | " + largeResponseInterfaces + " | " + 
                    String.format("%.1f%%", (double)largeResponseInterfaces / allMetrics.size() * 100) + " |\n\n");
        
        // 服务健康度排名
        if (serviceTopNMap != null && !serviceTopNMap.isEmpty()) {
            writer.write("### 🏥 服务健康度排名\n\n");
            writer.write("| 服务名称 | 平均得分 | 问题接口数 | 健康状态 |\n");
            writer.write("|----------|----------|------------|----------|\n");
            
            for (Map.Entry<String, List<InterfaceMetrics>> entry : serviceTopNMap.entrySet()) {
                String serviceName = entry.getKey();
                List<InterfaceMetrics> serviceMetrics = entry.getValue();
                
                double avgScore = serviceMetrics.stream()
                        .mapToDouble(InterfaceMetrics::getFinalPriorityScore)
                        .average()
                        .orElse(0.0);
                
                long problemCount = serviceMetrics.stream()
                        .mapToLong(m -> m.getFinalPriorityScore() >= 6.0 ? 1 : 0)
                        .sum();
                
                String healthStatus = getHealthStatus(avgScore);
                
                writer.write(String.format("| %s | %.2f | %d | %s |\n",
                        escapeMarkdown(serviceName),
                        avgScore,
                        problemCount,
                        healthStatus));
            }
            writer.write("\n");
        }
    }
    
    /**
     * 写入完整数据样本
     */
    private void writeAllDataSample(FileWriter writer, List<InterfaceMetrics> allMetrics) throws IOException {
        
        writer.write("## 📋 完整接口数据 (前50条)\n\n");
        writer.write("> 按优先级得分降序排列，显示前50条数据\n\n");
        
        // 表格标题
        writer.write("| 排名 | 服务 | 接口地址 | 方法 | 得分 | 调用次数 | 错误数 | 错误率 | 平均RT | 最慢RT | 10s以上 | 最大并发 |\n");
        writer.write("|------|------|----------|------|------|----------|-------|--------|--------|--------|---------|----------|\n");
        
        // 填充数据（前50条）
        int maxRows = Math.min(50, allMetrics.size());
        for (int i = 0; i < maxRows; i++) {
            InterfaceMetrics metrics = allMetrics.get(i);
            String priorityIcon = getPriorityIcon(metrics.getFinalPriorityScore());
            
            writer.write(String.format("| %s %d | %s | `%s` | %s | %.2f | %d | %d | %.2f%% | %.0f | %.0f | %d | %d |\n",
                    priorityIcon,
                    metrics.getPriorityRank(),
                    escapeMarkdown(metrics.getServiceName()),
                    escapeMarkdown(truncateUrl(metrics.getUrl(), 40)),
                    metrics.getMethod(),
                    metrics.getFinalPriorityScore(),
                    metrics.getCallCount(),
                    metrics.getErrorCount(),
                    metrics.getErrorRate() * 100,
                    metrics.getAvgResponseTime(),
                    metrics.getMaxResponseTime(),
                    metrics.getTimeOver10s(),
                    metrics.getMaxConcurrency()));
        }
        
        writer.write("\n");
        
        // 说明信息
        writer.write("### 📝 说明\n\n");
        writer.write("- **得分范围**: 2-10分，分数越高表示问题越严重\n");
        writer.write("- **优先级图标**: 🔴 高优先级(≥8.0) | 🟡 中优先级(6.0-7.9) | 🟢 低优先级(<6.0)\n");
        writer.write("- **完整数据**: 如需查看所有接口数据，请参考Excel报告\n");
        writer.write("- **评分规则**: 详见《接口优先级评分规则详细说明.md》\n\n");
        
        // 生成时间戳
        writer.write("---\n");
        writer.write("*报告生成时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) + "*\n");
    }
    
    /**
     * 写入详细分析
     */
    private void writeDetailedAnalysis(FileWriter writer, InterfaceMetrics metrics, int rank) throws IOException {
        
        writer.write("#### " + getPriorityIcon(metrics.getFinalPriorityScore()) + " 第" + rank + "名: " + 
                    escapeMarkdown(metrics.getServiceName()) + " - " + escapeMarkdown(metrics.getUrl()) + "\n\n");
        
        writer.write("**基本信息:**\n");
        writer.write("- 综合得分: **" + String.format("%.2f", metrics.getFinalPriorityScore()) + "分**\n");
        writer.write("- 调用统计: " + metrics.getCallCount() + "次调用，" + metrics.getErrorCount() + "次错误\n");
        writer.write("- 性能指标: 平均RT " + String.format("%.0f", metrics.getAvgResponseTime()) + "ms，最慢 " + 
                    String.format("%.0f", metrics.getMaxResponseTime()) + "ms\n");
        writer.write("- 时间分布: 10s以上 " + metrics.getTimeOver10s() + "次 (" + 
                    String.format("%.1f", metrics.getOver10sRatio() * 100) + "%)\n\n");
        
        writer.write("**得分详情:**\n");
        writer.write("- 响应时间性能: " + String.format("%.2f", metrics.getBusinessImpactScore()) + "分\n");
        writer.write("- 调用频率影响: " + String.format("%.2f", metrics.getPerformanceSeverityScore()) + "分\n");
        writer.write("- 稳定性风险: " + String.format("%.2f", metrics.getStabilityRiskScore()) + "分\n");
        writer.write("- 系统压力: " + String.format("%.2f", metrics.getResourceConsumptionScore()) + "分\n\n");
        
        if (metrics.hasRecentError()) {
            String error = metrics.getRecentError();
            if (error.length() > 100) {
                error = error.substring(0, 100) + "...";
            }
            writer.write("**最近错误:** `" + escapeMarkdown(error) + "`\n\n");
        }
    }
    
    // 辅助方法
    private String getPriorityIcon(double score) {
        if (score >= PriorityConfig.HIGH_PRIORITY_THRESHOLD) {
            return "🔴";
        } else if (score >= PriorityConfig.MEDIUM_PRIORITY_THRESHOLD) {
            return "🟡";
        } else {
            return "🟢";
        }
    }
    
    private String getHealthStatus(double avgScore) {
        if (avgScore >= 8.0) {
            return "🔴 需要紧急处理";
        } else if (avgScore >= 6.0) {
            return "🟡 需要关注";
        } else if (avgScore >= 4.0) {
            return "🟢 基本健康";
        } else {
            return "✅ 运行良好";
        }
    }
    
    private String escapeMarkdown(String text) {
        if (text == null) return "";
        return text.replace("|", "\\|").replace("\n", " ").replace("\r", "");
    }
    
    private String truncateUrl(String url, int maxLength) {
        if (url == null) return "";
        if (url.length() <= maxLength) return url;
        return url.substring(0, maxLength - 3) + "...";
    }
    
    private String generateMainIssue(InterfaceMetrics metrics) {
        StringBuilder issues = new StringBuilder();
        
        if (metrics.getAvgResponseTime() > 10000) {
            issues.append("🐌极高RT ");
        } else if (metrics.getAvgResponseTime() > 5000) {
            issues.append("🐌高RT ");
        }
        
        if (metrics.getErrorRate() > 0.01) {
            issues.append("❌高错误率 ");
        }
        
        if (metrics.hasRecentError()) {
            String error = metrics.getRecentError().toLowerCase();
            if (error.contains("timeout") || error.contains("timed out")) {
                issues.append("⏰超时 ");
            } else {
                issues.append("⚠️异常 ");
            }
        }
        
        if (metrics.getOver10sRatio() > 0.2) {
            issues.append("🐢慢请求多 ");
        }
        
        if (metrics.getCallCount() > 5000) {
            issues.append("🔥高频 ");
        }
        
        return issues.length() > 0 ? issues.toString().trim() : "⚡性能问题";
    }
}
