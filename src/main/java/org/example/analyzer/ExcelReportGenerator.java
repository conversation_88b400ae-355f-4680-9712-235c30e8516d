package org.example.analyzer;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileOutputStream;
import java.io.IOException;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * Excel报告生成器
 * 负责生成带格式的Excel分析报告
 */
public class ExcelReportGenerator {
    
    /**
     * 生成完整的Excel分析报告
     */
    public void generateReport(List<InterfaceMetrics> allMetrics, List<InterfaceMetrics> topNMetrics, String outputPath) {
        generateReport(allMetrics, topNMetrics, null, outputPath);
    }

    /**
     * 生成完整的Excel分析报告（包含每个服务的TOP N）
     */
    public void generateReport(List<InterfaceMetrics> allMetrics, List<InterfaceMetrics> topNMetrics,
                              Map<String, List<InterfaceMetrics>> serviceTopNMap, String outputPath) {
        try (Workbook workbook = new XSSFWorkbook()) {
            
            // 创建样式
            CellStyle headerStyle = createHeaderStyle(workbook);
            CellStyle highPriorityStyle = createHighPriorityStyle(workbook);
            CellStyle mediumPriorityStyle = createMediumPriorityStyle(workbook);
            CellStyle normalStyle = createNormalStyle(workbook);
            CellStyle numberStyle = createNumberStyle(workbook);
            
            // 创建全局TOP N工作表
            createTopNSheet(workbook, topNMetrics, headerStyle, highPriorityStyle, mediumPriorityStyle, normalStyle, numberStyle);

            // 创建每个服务的TOP N工作表
            if (serviceTopNMap != null && !serviceTopNMap.isEmpty()) {
                createServiceTopNSheets(workbook, serviceTopNMap, headerStyle, highPriorityStyle, mediumPriorityStyle, normalStyle, numberStyle);
            }

            // 创建完整数据工作表
            createAllDataSheet(workbook, allMetrics, headerStyle, highPriorityStyle, mediumPriorityStyle, normalStyle, numberStyle);

            // 创建汇总分析工作表
            createSummarySheet(workbook, allMetrics, topNMetrics, serviceTopNMap, headerStyle, normalStyle, numberStyle);
            
            // 保存文件
            try (FileOutputStream fos = new FileOutputStream(outputPath)) {
                workbook.write(fos);
            }
            
            System.out.println("Excel报告已生成: " + outputPath);
            
        } catch (IOException e) {
            System.err.println("生成Excel报告失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 创建TOP N工作表
     */
    private void createTopNSheet(Workbook workbook, List<InterfaceMetrics> topNMetrics, 
                                CellStyle headerStyle, CellStyle highPriorityStyle, 
                                CellStyle mediumPriorityStyle, CellStyle normalStyle, CellStyle numberStyle) {
        
        Sheet sheet = workbook.createSheet("全局TOP " + PriorityConfig.TOP_N_COUNT + " 优先修复接口");
        
        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {
            "排名", "服务模块", "接口地址", "HTTP方法", "综合得分", "主要问题",
            "调用次数", "错误数", "错误率(%)", "平均RT(ms)", "最慢RT(ms)", "10s以上次数",
            "响应时间性能得分", "调用频率影响得分", "稳定性风险得分", "系统压力得分", "最近错误"
        };
        
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
        
        // 填充数据
        for (int i = 0; i < topNMetrics.size(); i++) {
            InterfaceMetrics metrics = topNMetrics.get(i);
            Row row = sheet.createRow(i + 1);
            
            // 根据优先级得分选择样式
            CellStyle rowStyle = getRowStyle(metrics.getFinalPriorityScore(), highPriorityStyle, mediumPriorityStyle, normalStyle);
            
            // 填充数据
            createCell(row, 0, metrics.getPriorityRank(), rowStyle);
            createCell(row, 1, metrics.getServiceName(), rowStyle);
            createCell(row, 2, metrics.getUrl(), rowStyle);
            createCell(row, 3, metrics.getMethod(), rowStyle);
            createCell(row, 4, String.format("%.2f", metrics.getFinalPriorityScore()), rowStyle);
            createCell(row, 5, generateMainIssue(metrics), rowStyle);
            createCell(row, 6, metrics.getCallCount(), numberStyle);
            createCell(row, 7, metrics.getErrorCount(), numberStyle);
            createCell(row, 8, String.format("%.2f", metrics.getErrorRate() * 100), numberStyle);
            createCell(row, 9, String.format("%.2f", metrics.getAvgResponseTime()), numberStyle);
            createCell(row, 10, String.format("%.2f", metrics.getMaxResponseTime()), numberStyle);
            createCell(row, 11, metrics.getTimeOver10s(), numberStyle);
            createCell(row, 12, String.format("%.2f", metrics.getBusinessImpactScore()), numberStyle);
            createCell(row, 13, String.format("%.2f", metrics.getPerformanceSeverityScore()), numberStyle);
            createCell(row, 14, String.format("%.2f", metrics.getStabilityRiskScore()), numberStyle);
            createCell(row, 15, String.format("%.2f", metrics.getResourceConsumptionScore()), numberStyle);
            createCell(row, 16, metrics.getRecentError(), rowStyle);
        }
        
        // 设置列宽
        setColumnWidths(sheet, headers.length);
    }

    /**
     * 创建每个服务的TOP N工作表
     */
    private void createServiceTopNSheets(Workbook workbook, Map<String, List<InterfaceMetrics>> serviceTopNMap,
                                        CellStyle headerStyle, CellStyle highPriorityStyle,
                                        CellStyle mediumPriorityStyle, CellStyle normalStyle, CellStyle numberStyle) {

        for (Map.Entry<String, List<InterfaceMetrics>> entry : serviceTopNMap.entrySet()) {
            String serviceName = entry.getKey();
            List<InterfaceMetrics> serviceTopN = entry.getValue();

            // 创建服务专用工作表（确保名称安全）
            String sheetName = cleanSheetName(serviceName + "_TOP" + PriorityConfig.TOP_N_COUNT);
            Sheet sheet = workbook.createSheet(sheetName);

            // 创建标题行
            Row headerRow = sheet.createRow(0);
            String[] headers = {
                "服务内排名", "接口地址", "HTTP方法", "综合得分", "主要问题",
                "调用次数", "错误数", "错误率(%)", "平均RT(ms)", "最慢RT(ms)", "10s以上次数",
                "响应时间性能得分", "调用频率影响得分", "稳定性风险得分", "系统压力得分", "最近错误"
            };

            for (int i = 0; i < headers.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(headers[i]);
                cell.setCellStyle(headerStyle);
            }

            // 填充数据
            for (int i = 0; i < serviceTopN.size(); i++) {
                InterfaceMetrics metrics = serviceTopN.get(i);
                Row row = sheet.createRow(i + 1);

                // 根据优先级得分选择样式
                CellStyle rowStyle = getRowStyle(metrics.getFinalPriorityScore(), highPriorityStyle, mediumPriorityStyle, normalStyle);

                // 填充数据
                createCell(row, 0, metrics.getPriorityRank(), rowStyle);
                createCell(row, 1, metrics.getUrl(), rowStyle);
                createCell(row, 2, metrics.getMethod(), rowStyle);
                createCell(row, 3, String.format("%.2f", metrics.getFinalPriorityScore()), rowStyle);
                createCell(row, 4, generateMainIssue(metrics), rowStyle);
                createCell(row, 5, metrics.getCallCount(), numberStyle);
                createCell(row, 6, metrics.getErrorCount(), numberStyle);
                createCell(row, 7, String.format("%.2f", metrics.getErrorRate() * 100), numberStyle);
                createCell(row, 8, String.format("%.2f", metrics.getAvgResponseTime()), numberStyle);
                createCell(row, 9, String.format("%.2f", metrics.getMaxResponseTime()), numberStyle);
                createCell(row, 10, metrics.getTimeOver10s(), numberStyle);
                createCell(row, 11, String.format("%.2f", metrics.getBusinessImpactScore()), numberStyle);
                createCell(row, 12, String.format("%.2f", metrics.getPerformanceSeverityScore()), numberStyle);
                createCell(row, 13, String.format("%.2f", metrics.getStabilityRiskScore()), numberStyle);
                createCell(row, 14, String.format("%.2f", metrics.getResourceConsumptionScore()), numberStyle);
                createCell(row, 15, metrics.getRecentError(), rowStyle);
            }

            // 设置列宽
            setColumnWidths(sheet, headers.length);
        }
    }

    /**
     * 创建完整数据工作表
     */
    private void createAllDataSheet(Workbook workbook, List<InterfaceMetrics> allMetrics, 
                                   CellStyle headerStyle, CellStyle highPriorityStyle, 
                                   CellStyle mediumPriorityStyle, CellStyle normalStyle, CellStyle numberStyle) {
        
        Sheet sheet = workbook.createSheet("完整接口数据");
        
        // 创建标题行
        Row headerRow = sheet.createRow(0);
        String[] headers = {
            "排名", "服务模块", "接口地址", "HTTP方法", "综合得分",
            "调用次数", "错误数", "错误率(%)", "平均RT(ms)", "总时间(ms)", "最大并发", "最慢RT(ms)",
            "0-10ms", "10-100ms", "100-500ms", "500ms-1s", "1-10s", "10s以上",
            "最大响应体(Bytes)", "TP99", "TP95", "TP90", "业务异常次数", "最近错误"
        };
        
        for (int i = 0; i < headers.length; i++) {
            Cell cell = headerRow.createCell(i);
            cell.setCellValue(headers[i]);
            cell.setCellStyle(headerStyle);
        }
        
        // 填充数据（只显示前100条以避免文件过大）
        int maxRows = Math.min(allMetrics.size(), 100);
        for (int i = 0; i < maxRows; i++) {
            InterfaceMetrics metrics = allMetrics.get(i);
            Row row = sheet.createRow(i + 1);
            
            CellStyle rowStyle = getRowStyle(metrics.getFinalPriorityScore(), highPriorityStyle, mediumPriorityStyle, normalStyle);
            
            createCell(row, 0, metrics.getPriorityRank(), rowStyle);
            createCell(row, 1, metrics.getServiceName(), rowStyle);
            createCell(row, 2, metrics.getUrl(), rowStyle);
            createCell(row, 3, metrics.getMethod(), rowStyle);
            createCell(row, 4, String.format("%.2f", metrics.getFinalPriorityScore()), rowStyle);
            createCell(row, 5, metrics.getCallCount(), numberStyle);
            createCell(row, 6, metrics.getErrorCount(), numberStyle);
            createCell(row, 7, String.format("%.2f", metrics.getErrorRate() * 100), numberStyle);
            createCell(row, 8, String.format("%.2f", metrics.getAvgResponseTime()), numberStyle);
            createCell(row, 9, String.format("%.2f", metrics.getTotalTime()), numberStyle);
            createCell(row, 10, metrics.getMaxConcurrency(), numberStyle);
            createCell(row, 11, String.format("%.2f", metrics.getMaxResponseTime()), numberStyle);
            createCell(row, 12, metrics.getTime0To10ms(), numberStyle);
            createCell(row, 13, metrics.getTime10To100ms(), numberStyle);
            createCell(row, 14, metrics.getTime100To500ms(), numberStyle);
            createCell(row, 15, metrics.getTime500To1000ms(), numberStyle);
            createCell(row, 16, metrics.getTime1To10s(), numberStyle);
            createCell(row, 17, metrics.getTimeOver10s(), numberStyle);
            createCell(row, 18, metrics.getMaxResponseSize(), numberStyle);
            createCell(row, 19, metrics.getTp99(), rowStyle);
            createCell(row, 20, metrics.getTp95(), rowStyle);
            createCell(row, 21, metrics.getTp90(), rowStyle);
            createCell(row, 22, metrics.getBusinessExceptionCount(), numberStyle);
            createCell(row, 23, metrics.getRecentError(), rowStyle);
        }
        
        // 设置列宽
        setColumnWidths(sheet, headers.length);
    }
    
    /**
     * 创建汇总分析工作表
     */
    private void createSummarySheet(Workbook workbook, List<InterfaceMetrics> allMetrics,
                                   List<InterfaceMetrics> topNMetrics, Map<String, List<InterfaceMetrics>> serviceTopNMap,
                                   CellStyle headerStyle, CellStyle normalStyle, CellStyle numberStyle) {
        
        Sheet sheet = workbook.createSheet("汇总分析");
        
        int rowNum = 0;
        
        // 分析报告标题
        Row titleRow = sheet.createRow(rowNum++);
        Cell titleCell = titleRow.createCell(0);
        titleCell.setCellValue("接口优先级分析报告");
        titleCell.setCellStyle(headerStyle);
        
        rowNum++; // 空行
        
        // 基本统计信息
        createSummaryRow(sheet, rowNum++, "生成时间:", LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")), normalStyle);
        createSummaryRow(sheet, rowNum++, "总接口数:", String.valueOf(allMetrics.size()), normalStyle);
        createSummaryRow(sheet, rowNum++, "分析周期:", "15天", normalStyle);
        
        rowNum++; // 空行
        
        // 优先级分布统计
        long highPriorityCount = 0;
        long mediumPriorityCount = 0;
        for (InterfaceMetrics metric : allMetrics) {
            if (metric.getFinalPriorityScore() >= PriorityConfig.HIGH_PRIORITY_THRESHOLD) {
                highPriorityCount++;
            } else if (metric.getFinalPriorityScore() >= PriorityConfig.MEDIUM_PRIORITY_THRESHOLD) {
                mediumPriorityCount++;
            }
        }
        long lowPriorityCount = allMetrics.size() - highPriorityCount - mediumPriorityCount;
        
        createSummaryRow(sheet, rowNum++, "高优先级接口(≥8.0分):", String.valueOf(highPriorityCount), normalStyle);
        createSummaryRow(sheet, rowNum++, "中优先级接口(6.0-7.9分):", String.valueOf(mediumPriorityCount), normalStyle);
        createSummaryRow(sheet, rowNum++, "低优先级接口(<6.0分):", String.valueOf(lowPriorityCount), normalStyle);
        
        rowNum++; // 空行
        
        // 全局TOP N 接口列表
        Row topNTitleRow = sheet.createRow(rowNum++);
        Cell topNTitleCell = topNTitleRow.createCell(0);
        topNTitleCell.setCellValue("全局TOP " + PriorityConfig.TOP_N_COUNT + " 优先修复接口:");
        topNTitleCell.setCellStyle(headerStyle);

        for (int i = 0; i < topNMetrics.size(); i++) {
            InterfaceMetrics metrics = topNMetrics.get(i);
            String summary = String.format("%d. [%s] %s (得分: %.2f, RT: %.0fms, 调用: %d次)",
                    i + 1, metrics.getServiceName(), metrics.getUrl(),
                    metrics.getFinalPriorityScore(), metrics.getAvgResponseTime(), metrics.getCallCount());
            createSummaryRow(sheet, rowNum++, "", summary, normalStyle);
        }

        // 每个服务的TOP N接口列表
        if (serviceTopNMap != null && !serviceTopNMap.isEmpty()) {
            rowNum++; // 空行

            Row serviceTopNTitleRow = sheet.createRow(rowNum++);
            Cell serviceTopNTitleCell = serviceTopNTitleRow.createCell(0);
            serviceTopNTitleCell.setCellValue("各服务TOP " + PriorityConfig.TOP_N_COUNT + " 接口汇总:");
            serviceTopNTitleCell.setCellStyle(headerStyle);

            for (Map.Entry<String, List<InterfaceMetrics>> entry : serviceTopNMap.entrySet()) {
                String serviceName = entry.getKey();
                List<InterfaceMetrics> serviceTopN = entry.getValue();

                rowNum++; // 空行
                createSummaryRow(sheet, rowNum++, "【" + serviceName + " 服务】", "", headerStyle);

                for (int i = 0; i < serviceTopN.size(); i++) {
                    InterfaceMetrics metrics = serviceTopN.get(i);
                    String summary = String.format("  %d. %s (得分: %.2f, RT: %.0fms, 调用: %d次)",
                            i + 1, metrics.getUrl(),
                            metrics.getFinalPriorityScore(), metrics.getAvgResponseTime(), metrics.getCallCount());
                    createSummaryRow(sheet, rowNum++, "", summary, normalStyle);
                }
            }
        }
        
        // 设置列宽 - 汇总表特殊处理
        if (PriorityConfig.USE_AUTO_SIZE_COLUMN) {
            sheet.autoSizeColumn(0);
            sheet.autoSizeColumn(1);
            sheet.setColumnWidth(1, Math.max(sheet.getColumnWidth(1), PriorityConfig.DEFAULT_COLUMN_WIDTH));
        } else {
            sheet.setColumnWidth(0, PriorityConfig.DEFAULT_COLUMN_WIDTH);
            sheet.setColumnWidth(1, PriorityConfig.DEFAULT_COLUMN_WIDTH + 5120); // 第二列稍宽一些显示完整信息
        }
    }
    
    // 样式创建方法
    private CellStyle createHeaderStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        Font font = workbook.createFont();
        font.setBold(true);
        font.setColor(IndexedColors.WHITE.getIndex());
        style.setFont(font);
        style.setFillForegroundColor(IndexedColors.DARK_BLUE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        style.setAlignment(HorizontalAlignment.CENTER);
        return style;
    }
    
    private CellStyle createHighPriorityStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(IndexedColors.RED.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }
    
    private CellStyle createMediumPriorityStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setFillForegroundColor(IndexedColors.ORANGE.getIndex());
        style.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }
    
    private CellStyle createNormalStyle(Workbook workbook) {
        CellStyle style = workbook.createCellStyle();
        style.setBorderTop(BorderStyle.THIN);
        style.setBorderBottom(BorderStyle.THIN);
        style.setBorderLeft(BorderStyle.THIN);
        style.setBorderRight(BorderStyle.THIN);
        return style;
    }
    
    private CellStyle createNumberStyle(Workbook workbook) {
        CellStyle style = createNormalStyle(workbook);
        style.setAlignment(HorizontalAlignment.RIGHT);
        return style;
    }
    
    // 辅助方法
    private CellStyle getRowStyle(double score, CellStyle highStyle, CellStyle mediumStyle, CellStyle normalStyle) {
        if (score >= PriorityConfig.HIGH_PRIORITY_THRESHOLD) {
            return highStyle;
        } else if (score >= PriorityConfig.MEDIUM_PRIORITY_THRESHOLD) {
            return mediumStyle;
        } else {
            return normalStyle;
        }
    }

    /**
     * 统一设置工作表列宽
     * @param sheet 工作表
     * @param columnCount 列数
     */
    private void setColumnWidths(Sheet sheet, int columnCount) {
        if (PriorityConfig.USE_AUTO_SIZE_COLUMN) {
            // 使用自动调整模式
            for (int i = 0; i < columnCount; i++) {
                sheet.autoSizeColumn(i);
                // 限制最大宽度
                if (sheet.getColumnWidth(i) > PriorityConfig.MAX_COLUMN_WIDTH) {
                    sheet.setColumnWidth(i, PriorityConfig.MAX_COLUMN_WIDTH);
                }
            }
        } else {
            // 使用统一列宽模式
            for (int i = 0; i < columnCount; i++) {
                sheet.setColumnWidth(i, PriorityConfig.DEFAULT_COLUMN_WIDTH);
            }
        }
    }
    
    private void createCell(Row row, int column, Object value, CellStyle style) {
        Cell cell = row.createCell(column);
        if (value instanceof String) {
            cell.setCellValue((String) value);
        } else if (value instanceof Integer) {
            cell.setCellValue((Integer) value);
        } else if (value instanceof Double) {
            cell.setCellValue((Double) value);
        } else {
            cell.setCellValue(value != null ? value.toString() : "");
        }
        cell.setCellStyle(style);
    }
    
    private void createSummaryRow(Sheet sheet, int rowNum, String label, String value, CellStyle style) {
        Row row = sheet.createRow(rowNum);
        createCell(row, 0, label, style);
        createCell(row, 1, value, style);
    }
    
    private String generateMainIssue(InterfaceMetrics metrics) {
        StringBuilder issues = new StringBuilder();
        
        if (metrics.getAvgResponseTime() > 10000) {
            issues.append("极高RT ");
        } else if (metrics.getAvgResponseTime() > 5000) {
            issues.append("高RT ");
        }
        
        if (metrics.getErrorRate() > 0.01) {
            issues.append("高错误率 ");
        }
        
        if (metrics.hasRecentError()) {
            if (metrics.getRecentError().toLowerCase().contains("timeout")) {
                issues.append("超时 ");
            } else {
                issues.append("异常 ");
            }
        }
        
        if (metrics.getOver10sRatio() > 0.2) {
            issues.append("慢请求多 ");
        }
        
        return issues.length() > 0 ? issues.toString().trim() : "性能问题";
    }

    /**
     * 清理工作表名称，确保符合Excel规范
     * Excel工作表名称不能包含: \ / ? * [ ] :
     * 长度限制：最多31个字符
     */
    private String cleanSheetName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return "Sheet";
        }

        // 移除Excel不支持的字符
        String cleaned = name.replaceAll("[\\\\/:*?\\[\\]]", "");

        // 移除前后空格
        cleaned = cleaned.trim();

        // 如果清理后为空，使用默认名称
        if (cleaned.isEmpty()) {
            cleaned = "Sheet";
        }

        // 限制长度（Excel工作表名称最多31个字符）
        if (cleaned.length() > 31) {
            cleaned = cleaned.substring(0, 31);
        }

        return cleaned;
    }
}