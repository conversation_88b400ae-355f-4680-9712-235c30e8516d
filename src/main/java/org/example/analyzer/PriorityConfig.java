package org.example.analyzer;

/**
 * 优先级计算配置类
 * 可以在这里调整各种权重和阈值
 */
public class PriorityConfig {
    
    // ==================== 简化权重配置 ====================
    /** 响应时间性能权重 */
    public static final double RESPONSE_PERFORMANCE_WEIGHT = 0.55;

    /** 调用频率影响权重 */
    public static final double CALL_FREQUENCY_WEIGHT = 0.1;

    /** 稳定性风险权重 */
    public static final double STABILITY_RISK_WEIGHT = 0.20;

    /** 系统压力权重 */
    public static final double SYSTEM_PRESSURE_WEIGHT = 0.15;
    
    // ==================== 响应时间性能配置 ====================
    /** 平均响应时间权重 */
    public static final double AVG_RT_WEIGHT = 0.4;

    /** 最慢响应时间权重 */
    public static final double MAX_RT_WEIGHT = 0.3;

    /** 响应时间分布权重 */
    public static final double RT_DISTRIBUTION_WEIGHT = 0.3;

    // 响应时间阈值（毫秒）
    public static final double CRITICAL_RT_THRESHOLD = 10000;   // 10秒 - 严重
    public static final double HIGH_RT_THRESHOLD = 5000;        // 5秒 - 高
    public static final double MEDIUM_RT_THRESHOLD = 2000;      // 2秒 - 中等
    public static final double LOW_RT_THRESHOLD = 500;          // 500ms - 轻微

    // ==================== 调用频率影响配置 ====================
    /** 调用次数权重 */
    public static final double CALL_COUNT_WEIGHT = 0.3;

    /** 总时间消耗权重 */
    public static final double TOTAL_TIME_WEIGHT = 0.7;

    // 调用频率阈值
    public static final int ULTRA_HIGH_FREQUENCY_THRESHOLD = 5000;  // 超高频
    public static final int HIGH_FREQUENCY_THRESHOLD = 1000;        // 高频
    public static final int MEDIUM_FREQUENCY_THRESHOLD = 100;       // 中频
    
    // ==================== 稳定性风险配置 ====================
    /** 错误率权重 */
    public static final double ERROR_RATE_WEIGHT = 0.7;

    /** 最近错误权重 */
    public static final double RECENT_ERROR_WEIGHT = 0.3;

    // 错误率阈值
    public static final double CRITICAL_ERROR_RATE = 0.05;     // 5%
    public static final double HIGH_ERROR_RATE = 0.01;         // 1%
    public static final double MEDIUM_ERROR_RATE = 0.001;      // 0.1%

    // ==================== 系统压力配置 ====================
    /** 并发压力权重 */
    public static final double CONCURRENCY_WEIGHT = 0.6;

    /** 响应体大小权重 */
    public static final double RESPONSE_SIZE_WEIGHT = 0.4;

    // 并发压力阈值
    public static final int HIGH_CONCURRENCY_THRESHOLD = 10;
    public static final int MEDIUM_CONCURRENCY_THRESHOLD = 5;
    public static final int LOW_CONCURRENCY_THRESHOLD = 2;

    // 响应体大小阈值（字节）
    public static final long LARGE_RESPONSE_SIZE = 10 * 1024 * 1024;  // 10MB
    public static final long MEDIUM_RESPONSE_SIZE = 1 * 1024 * 1024;  // 1MB
    public static final long SMALL_RESPONSE_SIZE = 100 * 1024;        // 100KB
    

    
    // ==================== 输出配置 ====================
    /** TOP N 数量 */
    public static final int TOP_N_COUNT = 20;

    /** 高优先级阈值（用于红色标记） */
    public static final double HIGH_PRIORITY_THRESHOLD = 8.0;

    /** 中优先级阈值（用于橙色标记） */
    public static final double MEDIUM_PRIORITY_THRESHOLD = 6.0;

    // ==================== Excel列宽配置 ====================
    /** 默认列宽度（字符数） */
    public static final int DEFAULT_COLUMN_WIDTH_CHARS = 14;

    /** 默认列宽度（POI单位：1个字符 = 256个单位） */
    public static final int DEFAULT_COLUMN_WIDTH = DEFAULT_COLUMN_WIDTH_CHARS * 256;

    /** 最大列宽度限制（字符数） */
    public static final int MAX_COLUMN_WIDTH_CHARS = 50;

    /** 最大列宽度限制（POI单位） */
    public static final int MAX_COLUMN_WIDTH = MAX_COLUMN_WIDTH_CHARS * 256;

    /** 是否使用自动调整列宽（false=使用统一列宽，true=自动调整后限制最大宽度） */
    public static final boolean USE_AUTO_SIZE_COLUMN = false;
    

    
    // ==================== 错误关键词 ====================
    /** 超时错误关键词 */
    public static final String[] TIMEOUT_ERROR_KEYWORDS = {
        "timeout", "timed out", "连接超时", "读取超时", "Read timed out"
    };

    /** 连接错误关键词 */
    public static final String[] CONNECTION_ERROR_KEYWORDS = {
        "connection", "连接", "Connection refused", "Connection reset"
    };

    /** 业务异常关键词 */
    public static final String[] BUSINESS_ERROR_KEYWORDS = {
        "业务", "检查", "验证", "重复", "不存在", "已存在"
    };

    // ==================== URL关键字过滤配置 ====================
    /** URL关键字过滤开关 */
    public static final boolean ENABLE_URL_KEYWORD_FILTER = true;

    /** 需要降权的URL关键字（逗号分隔） */
    public static final String LOW_PRIORITY_URL_KEYWORDS = "export,import,erp,oss,init";

    /** 关键字匹配时的最低权重得分 */
    public static final double LOW_PRIORITY_SCORE = 1.0;
}