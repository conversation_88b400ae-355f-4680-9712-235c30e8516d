package org.example.analyzer.excel;

/**
 * Markdown格式测试类
 * 用于验证生成的Markdown表格格式是否正确
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since 2025-01-25
 */
public class MarkdownFormatTest {
    
    public static void main(String[] args) {
        System.out.println("=== Markdown格式测试 ===");
        
        // 测试表格格式
        testMarkdownTableFormat();
        
        System.out.println("测试完成！");
    }
    
    /**
     * 测试Markdown表格格式
     */
    private static void testMarkdownTableFormat() {
        System.out.println("\n生成的Markdown表格格式示例：");
        System.out.println();
        
        // 模拟生成的表格格式
        StringBuilder markdown = new StringBuilder();
        
        // 标题
        markdown.append("# Excel标注颜色记录整合表\n\n");
        markdown.append("生成时间: 2025-01-25 14:45:57\n\n");
        
        // 表头 (动态生成格式示例 - 假设17列原始数据)
        markdown.append("| 所在模块 | 列1 | 列2 | 列3 | 列4 | 列5 | 列6 | 列7 | 列8 | 列9 | 列10 | 列11 | 列12 | 列13 | 列14 | 列15 | 列16 | 列17 | 优先级 | 责任人 |\n");

        // 分隔行 (20列: 1+17+2)
        markdown.append("| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |\n");
        
        // 数据行示例 (20列: 1+17+2)
        markdown.append("| basedata | 1 | basedata | /api/test | POST | 6.68 | 性能问题 | 1000 | 5 | 0.5 | 1200.5 | 5000.0 | 2 | 7.2 | 8.5 | 3.1 | 6.8 | Connection timeout | 高 |  |\n");
        markdown.append("| gateway | 2 | gateway | /api/login | GET | 5.92 | 异常 | 800 | 3 | 0.375 | 950.2 | 3000.0 | 1 | 6.5 | 7.8 | 4.2 | 5.9 | Parse error | 中 |  |\n");
        
        // 统计信息
        markdown.append("\n## 统计信息\n\n");
        markdown.append("- 总计标注颜色的记录数: 2\n");
        markdown.append("- 按优先级分布:\n");
        markdown.append("  - 高: 1 条\n");
        markdown.append("  - 中: 1 条\n");
        markdown.append("- 按模块分布:\n");
        markdown.append("  - basedata: 1 条\n");
        markdown.append("  - gateway: 1 条\n");
        
        System.out.println(markdown.toString());
        
        // 验证格式
        validateFormat(markdown.toString());
    }
    
    /**
     * 验证Markdown格式
     */
    private static void validateFormat(String content) {
        System.out.println("\n=== 格式验证结果 ===");
        
        String[] lines = content.split("\n");
        boolean hasValidTable = false;
        int tableLineCount = 0;
        
        for (String line : lines) {
            line = line.trim();
            
            // 检查表格行
            if (line.startsWith("|") && line.endsWith("|")) {
                tableLineCount++;
                
                // 计算列数
                int columns = countColumns(line);
                System.out.println("表格行 " + tableLineCount + ": " + columns + " 列");
                
                if (tableLineCount == 1) {
                    System.out.println("  表头: " + line.substring(0, Math.min(50, line.length())) + "...");
                } else if (tableLineCount == 2) {
                    System.out.println("  分隔行: " + line.substring(0, Math.min(30, line.length())) + "...");
                    hasValidTable = isValidSeparator(line);
                } else {
                    System.out.println("  数据行: " + line.substring(0, Math.min(50, line.length())) + "...");
                }
            }
        }
        
        System.out.println("\n验证结果:");
        System.out.println("- 表格行数: " + tableLineCount);
        System.out.println("- 格式正确: " + (hasValidTable ? "✅" : "❌"));
        
        if (hasValidTable) {
            System.out.println("- Markdown表格格式符合标准");
        } else {
            System.out.println("- Markdown表格格式可能存在问题");
        }
    }
    
    /**
     * 计算表格列数
     */
    private static int countColumns(String line) {
        if (!line.startsWith("|") || !line.endsWith("|")) {
            return 0;
        }
        
        String content = line.substring(1, line.length() - 1);
        return content.split("\\|").length;
    }
    
    /**
     * 验证分隔行格式
     */
    private static boolean isValidSeparator(String line) {
        if (!line.startsWith("|") || !line.endsWith("|")) {
            return false;
        }
        
        String content = line.substring(1, line.length() - 1);
        String[] parts = content.split("\\|");
        
        for (String part : parts) {
            if (!part.trim().contains("---")) {
                return false;
            }
        }
        
        return true;
    }
}
