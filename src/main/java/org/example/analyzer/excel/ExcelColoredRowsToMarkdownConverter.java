package org.example.analyzer.excel;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFColor;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * Excel标注颜色行转整合Markdown表格工具
 * 读取指定工作表中标注了颜色的行，输出一个整合的Markdown表格
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since 2025-01-25
 */
public class ExcelColoredRowsToMarkdownConverter {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelColoredRowsToMarkdownConverter.class);
    
    // 配置
    private static final String EXCEL_PATH = "E:\\tools\\mcp_server_config\\excel\\接口优先级分析报告_20250725_144557.xlsx";
    private static final String OUTPUT_PATH = "D:\\Users\\ex_wuyh42.CN\\Desktop\\生产环境-慢接口\\提取Excel颜色标注-md";
    
    // 要处理的工作表列表
    private static final List<String> TARGET_SHEETS = Arrays.asList(

        "basedata_TOP20",
        "ctc_TOP20",
        "statistics_TOP20"
    );
    
    public static void main(String[] args) {
        logger.info("开始执行Excel标注颜色行整合工具...");
        
        try {
            ExcelColoredRowsToMarkdownConverter processor = new ExcelColoredRowsToMarkdownConverter();
            processor.processColoredRowsToMarkdown();
            
            logger.info("处理完成！输出文件: {}", OUTPUT_PATH);
            System.out.println("处理完成！输出文件: " + OUTPUT_PATH);
            
        } catch (Exception e) {
            logger.error("处理失败: {}", e.getMessage(), e);
            System.err.println("处理失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 处理标注颜色的行并生成整合的Markdown表格
     */
    public void processColoredRowsToMarkdown() throws IOException {
        List<ColoredRowData> allColoredRows = new ArrayList<>();
        
        try (FileInputStream fis = new FileInputStream(EXCEL_PATH);
             XSSFWorkbook workbook = new XSSFWorkbook(fis)) {
            
            logger.info("开始处理Excel文件: {}", EXCEL_PATH);
            
            // 处理每个指定的工作表
            for (String sheetName : TARGET_SHEETS) {
                Sheet sheet = workbook.getSheet(sheetName);
                if (sheet == null) {
                    logger.warn("工作表不存在: {}", sheetName);
                    continue;
                }
                
                logger.info("处理工作表: {}", sheetName);
                List<ColoredRowData> sheetColoredRows = extractColoredRowsFromSheet(sheet, sheetName);
                allColoredRows.addAll(sheetColoredRows);
                
                logger.info("工作表 {} 找到 {} 行标注颜色的数据", sheetName, sheetColoredRows.size());
            }
            
            logger.info("总计找到 {} 行标注颜色的数据", allColoredRows.size());
            
            // 生成整合的Markdown表格
            if (!allColoredRows.isEmpty()) {
                String markdownContent = generateIntegratedMarkdownTable(allColoredRows);
                writeMarkdownFile(markdownContent, OUTPUT_PATH);
            } else {
                logger.warn("没有找到任何标注颜色的行");
            }
        }
    }
    
    /**
     * 从工作表中提取标注颜色的行
     */
    private List<ColoredRowData> extractColoredRowsFromSheet(Sheet sheet, String sheetName) {
        List<ColoredRowData> coloredRows = new ArrayList<>();
        String moduleName = extractModuleName(sheetName);

        // 获取表头
        Row headerRow = sheet.getRow(0);
        if (headerRow == null) {
            logger.warn("工作表 {} 没有表头行", sheetName);
            return coloredRows;
        }

        // 记录实际的表头信息
        List<String> actualHeaders = extractActualHeaders(headerRow);
        logger.info("工作表 {} 的实际列数: {}, 表头: {}", sheetName, actualHeaders.size(), actualHeaders);
        
        // 遍历数据行
        for (int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
            Row row = sheet.getRow(rowIndex);
            if (row == null) continue;
            
            // 检查行是否有颜色标注
            if (isRowColored(row)) {
                ColoredRowData rowData = new ColoredRowData();
                rowData.setModuleName(moduleName);
                rowData.setSheetName(sheetName);
                rowData.setRowIndex(rowIndex);

                // 获取颜色优先级
                String priority = getRowColorPriority(row);
                rowData.setPriority(priority);

                // 提取单元格数据
                List<String> cellValues = new ArrayList<>();
                for (int cellIndex = 0; cellIndex < headerRow.getLastCellNum(); cellIndex++) {
                    Cell cell = row.getCell(cellIndex);
                    String cellValue = getCellValueAsString(cell);
                    cellValues.add(cellValue);
                }
                rowData.setCellValues(cellValues);

                coloredRows.add(rowData);
                logger.debug("发现标注颜色的行: 工作表={}, 行号={}, 优先级={}", sheetName, rowIndex + 1, priority);
            }
        }
        
        return coloredRows;
    }
    
    /**
     * 检查行是否有颜色标注
     */
    private boolean isRowColored(Row row) {
        for (Cell cell : row) {
            if (cell != null && isCellColored(cell)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 获取行的颜色优先级
     */
    private String getRowColorPriority(Row row) {
        for (Cell cell : row) {
            if (cell != null) {
                String priority = getCellColorPriority(cell);
                if (!"".equals(priority)) {
                    return priority;
                }
            }
        }
        return "";
    }

    /**
     * 检查单元格是否有颜色标注
     */
    private boolean isCellColored(Cell cell) {
        CellStyle cellStyle = cell.getCellStyle();

        if (cell instanceof XSSFCell) {
            XSSFCellStyle xssfStyle = (XSSFCellStyle) cellStyle;
            XSSFColor bgColor = xssfStyle.getFillForegroundXSSFColor();

            if (bgColor != null && !bgColor.isAuto()) {
                return true;
            }
        } else {
            short bgColorIndex = cellStyle.getFillForegroundColor();
            if (bgColorIndex != IndexedColors.AUTOMATIC.getIndex() &&
                bgColorIndex != IndexedColors.WHITE.getIndex()) {
                return true;
            }
        }

        return false;
    }

    /**
     * 获取单元格颜色对应的优先级
     */
    private String getCellColorPriority(Cell cell) {
        CellStyle cellStyle = cell.getCellStyle();

        if (cell instanceof XSSFCell) {
            XSSFCellStyle xssfStyle = (XSSFCellStyle) cellStyle;
            XSSFColor bgColor = xssfStyle.getFillForegroundXSSFColor();

            if (bgColor != null && !bgColor.isAuto()) {
                String colorHex = bgColor.getARGBHex();
                if (colorHex != null) {
                    // 红色系列 - 高优先级
                    if (colorHex.toUpperCase().contains("FF0000") ||
                        colorHex.toUpperCase().contains("FF6B6B") ||
                        colorHex.toUpperCase().contains("DC3545") ||
                        colorHex.toUpperCase().contains("E74C3C")) {
                        return "高";
                    }
                    // 黄色系列 - 中优先级
                    else if (colorHex.toUpperCase().contains("FFFF00") ||
                             colorHex.toUpperCase().contains("FFC107") ||
                             colorHex.toUpperCase().contains("F39C12") ||
                             colorHex.toUpperCase().contains("FFD700")) {
                        return "中";
                    }
                    // 其他颜色 - 中优先级
                    else {
                        return "中";
                    }
                }
            }
        } else {
            // 处理传统Excel格式的颜色索引
            short bgColorIndex = cellStyle.getFillForegroundColor();
            if (bgColorIndex != IndexedColors.AUTOMATIC.getIndex() &&
                bgColorIndex != IndexedColors.WHITE.getIndex()) {

                if (bgColorIndex == IndexedColors.RED.getIndex() ||
                    bgColorIndex == IndexedColors.ROSE.getIndex()) {
                    return "高";
                } else if (bgColorIndex == IndexedColors.YELLOW.getIndex() ||
                          bgColorIndex == IndexedColors.LIGHT_YELLOW.getIndex()) {
                    return "中";
                } else {
                    return "中";
                }
            }
        }

        return "";
    }
    
    /**
     * 从工作表名称提取模块名称
     */
    private String extractModuleName(String sheetName) {
        if (sheetName.contains("_")) {
            return sheetName.split("_")[0];
        }
        return sheetName;
    }

    /**
     * 提取Excel表头的实际列名
     */
    private List<String> extractActualHeaders(Row headerRow) {
        List<String> headers = new ArrayList<>();

        if (headerRow != null) {
            for (int cellIndex = 0; cellIndex < headerRow.getLastCellNum(); cellIndex++) {
                Cell cell = headerRow.getCell(cellIndex);
                String header = getCellValueAsString(cell);
                if (!header.trim().isEmpty()) {
                    headers.add(header.trim());
                } else {
                    headers.add("列" + (cellIndex + 1));
                }
            }
        }

        return headers;
    }

    /**
     * 获取第一个工作表的实际表头（用于生成统一的表头）
     */
    private List<String> getActualHeadersFromFirstSheet() {
        try (FileInputStream fis = new FileInputStream(EXCEL_PATH);
             XSSFWorkbook workbook = new XSSFWorkbook(fis)) {

            // 尝试从第一个目标工作表获取表头
            for (String sheetName : TARGET_SHEETS) {
                Sheet sheet = workbook.getSheet(sheetName);
                if (sheet != null) {
                    Row headerRow = sheet.getRow(0);
                    if (headerRow != null) {
                        List<String> headers = extractActualHeaders(headerRow);
                        logger.debug("从工作表 {} 获取表头: {}", sheetName, headers);
                        return headers;
                    }
                }
            }
        } catch (Exception e) {
            logger.warn("无法获取实际表头: {}", e.getMessage());
        }

        return new ArrayList<>();
    }
    
    /**
     * 获取单元格值的字符串表示
     */
    private String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return "";
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue().toString();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue().trim();
                } catch (Exception e) {
                    try {
                        return String.valueOf(cell.getNumericCellValue());
                    } catch (Exception ex) {
                        return cell.getCellFormula();
                    }
                }
            case BLANK:
            case _NONE:
            default:
                return "";
        }
    }
    
    /**
     * 生成整合的Markdown表格
     */
    private String generateIntegratedMarkdownTable(List<ColoredRowData> allColoredRows) {
        StringBuilder markdown = new StringBuilder();

        // 添加标题
        markdown.append("# Excel标注颜色记录整合表\n\n");
        markdown.append("生成时间: ").append(LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"))).append("\n\n");

        if (allColoredRows.isEmpty()) {
            markdown.append("没有找到标注颜色的记录。\n");
            return markdown.toString();
        }

        // 获取第一行数据来确定列结构
        ColoredRowData firstRow = allColoredRows.get(0);
        List<String> cellValues = firstRow.getCellValues();

        // 尝试获取实际的表头名称（从第一个工作表）
        List<String> actualHeaders = getActualHeadersFromFirstSheet();

        logger.info("开始生成表格，数据列数: {}, 实际表头数: {}", cellValues.size(), actualHeaders.size());

        // 构建表头，完全动态生成，确保与数据行列数一致
        markdown.append("| 所在模块 |");

        // 使用实际的表头名称或生成通用列名
        if (actualHeaders.size() == cellValues.size()) {
            // 使用实际的Excel表头名称
            for (String header : actualHeaders) {
                markdown.append(" ").append(escapeMarkdown(header)).append(" |");
            }
            logger.info("使用实际Excel表头名称");
        } else {
            // 使用通用列名
            for (int i = 0; i < cellValues.size(); i++) {
                markdown.append(" 列").append(i + 1).append(" |");
            }
            logger.warn("表头数量不匹配，使用通用列名。实际表头: {}, 数据列: {}", actualHeaders.size(), cellValues.size());
        }

        // 在最后添加优先级和责任人列
        markdown.append(" 优先级 | 责任人 |\n");

        // 记录表头列数用于验证
        int headerColumns = 1 + cellValues.size() + 2; // 所在模块 + 原有列数 + 优先级 + 责任人
        logger.info("动态生成表头完成，总列数: {}, 原有数据列数: {}", headerColumns, cellValues.size());

        // 添加分隔行，确保与表头列数完全一致
        int totalColumns = 1 + cellValues.size() + 2; // 所在模块 + 原有列数 + 优先级 + 责任人

        // 验证与表头列数一致
        if (totalColumns != headerColumns) {
            logger.error("分隔行列数({})与表头列数({})不一致！", totalColumns, headerColumns);
        }

        // 生成分隔行
        StringBuilder separatorRow = new StringBuilder();
        for (int i = 0; i < totalColumns; i++) {
            if (i == 0) {
                separatorRow.append("| --- ");
            } else {
                separatorRow.append("| --- ");
            }
        }
        separatorRow.append("|\n");
        markdown.append(separatorRow);

        logger.info("生成分隔行，列数: {}", totalColumns);

        // 添加数据行
        int rowIndex = 0;
        for (ColoredRowData rowData : allColoredRows) {
            StringBuilder row = new StringBuilder();
            int columnCount = 0;

            // 所在模块列
            row.append("| ").append(escapeMarkdown(rowData.getModuleName())).append(" ");
            columnCount++;

            // 原有数据列
            for (String cellValue : rowData.getCellValues()) {
                row.append("| ").append(escapeMarkdown(cellValue)).append(" ");
                columnCount++;
            }

            // 优先级列
            row.append("| ").append(escapeMarkdown(rowData.getPriority())).append(" ");
            columnCount++;

            // 责任人列（空）
            row.append("| ");
            columnCount++;

            row.append("|\n");
            markdown.append(row);

            // 验证每一行的列数是否与表头一致
            if (columnCount != totalColumns) {
                logger.error("第{}行数据列数({})与表头列数({})不一致！行内容: {}",
                           rowIndex + 1, columnCount, totalColumns, row.toString().trim());
            }

            // 记录第一行的详细信息
            if (rowIndex == 0) {
                logger.info("数据行列数: {}, 原有数据列数: {}, 模块: {}, 优先级: {}",
                          columnCount, rowData.getCellValues().size(),
                          rowData.getModuleName(), rowData.getPriority());
            }
            rowIndex++;
        }

        // 添加统计信息
        addStatistics(markdown, allColoredRows);

        return markdown.toString();
    }

    /**
     * 添加统计信息
     */
    private void addStatistics(StringBuilder markdown, List<ColoredRowData> allColoredRows) {
        markdown.append("\n## 统计信息\n\n");
        markdown.append("- 总计标注颜色的记录数: ").append(allColoredRows.size()).append("\n");

        // 按优先级统计
        Map<String, Integer> priorityStats = new HashMap<>();
        // 按模块统计
        Map<String, Integer> moduleStats = new HashMap<>();
        // 按工作表统计
        Map<String, Integer> sheetStats = new HashMap<>();

        for (ColoredRowData rowData : allColoredRows) {
            String priority = rowData.getPriority();
            String module = rowData.getModuleName();
            String sheet = rowData.getSheetName();

            priorityStats.put(priority, priorityStats.getOrDefault(priority, 0) + 1);
            moduleStats.put(module, moduleStats.getOrDefault(module, 0) + 1);
            sheetStats.put(sheet, sheetStats.getOrDefault(sheet, 0) + 1);
        }

        markdown.append("- 按优先级分布:\n");
        // 按优先级排序显示（高 -> 中 -> 其他）
        if (priorityStats.containsKey("高")) {
            markdown.append("  - 高: ").append(priorityStats.get("高")).append(" 条\n");
        }
        if (priorityStats.containsKey("中")) {
            markdown.append("  - 中: ").append(priorityStats.get("中")).append(" 条\n");
        }
        for (Map.Entry<String, Integer> entry : priorityStats.entrySet()) {
            if (!"高".equals(entry.getKey()) && !"中".equals(entry.getKey())) {
                markdown.append("  - ").append(entry.getKey()).append(": ").append(entry.getValue()).append(" 条\n");
            }
        }

        markdown.append("- 按模块分布:\n");
        for (Map.Entry<String, Integer> entry : moduleStats.entrySet()) {
            markdown.append("  - ").append(entry.getKey()).append(": ").append(entry.getValue()).append(" 条\n");
        }

        markdown.append("- 按工作表分布:\n");
        for (Map.Entry<String, Integer> entry : sheetStats.entrySet()) {
            markdown.append("  - ").append(entry.getKey()).append(": ").append(entry.getValue()).append(" 条\n");
        }
    }

    /**
     * 转义Markdown特殊字符
     */
    private String escapeMarkdown(String text) {
        if (text == null || text.isEmpty()) {
            return "";
        }

        // 转义Markdown表格中的特殊字符，确保格式正确
        String escaped = text.trim();

        // 首先处理反斜杠（必须最先处理）
        escaped = escaped.replace("\\", "\\\\");

        // 处理管道符（表格分隔符）
        escaped = escaped.replace("|", "\\|");

        // 处理换行符（在表格中用空格或HTML标签替换）
        escaped = escaped.replace("\n", " ");
        escaped = escaped.replace("\r", "");

        // 处理其他Markdown特殊字符
        escaped = escaped.replace("*", "\\*");
        escaped = escaped.replace("_", "\\_");
        escaped = escaped.replace("`", "\\`");
        escaped = escaped.replace("#", "\\#");
        escaped = escaped.replace("[", "\\[");
        escaped = escaped.replace("]", "\\]");
        escaped = escaped.replace("(", "\\(");
        escaped = escaped.replace(")", "\\)");

        // 处理连续的空格
        escaped = escaped.replaceAll("\\s+", " ");

        return escaped;
    }

    /**
     * 写入Markdown文件
     */
    private void writeMarkdownFile(String content, String filePath) throws IOException {
        // 创建输出目录
        File file = new File(filePath);
        File parentDir = file.getParentFile();
        if (parentDir != null && !parentDir.exists()) {
            boolean created = parentDir.mkdirs();
            if (created) {
                logger.info("创建输出目录: {}", parentDir.getAbsolutePath());
            }
        }

        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(filePath), StandardCharsets.UTF_8))) {
            writer.write(content);
        }
        logger.info("Markdown文件已保存: {}", filePath);

        // 验证Markdown格式
        validateMarkdownFormat(content);

        // 额外验证列数一致性
        validateColumnConsistency(content);
    }

    /**
     * 验证Markdown表格格式是否正确
     */
    private void validateMarkdownFormat(String content) {
        String[] lines = content.split("\n");
        boolean inTable = false;
        int headerColumns = 0;

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();

            // 检测表格开始
            if (line.startsWith("|") && line.endsWith("|")) {
                if (!inTable) {
                    inTable = true;
                    // 计算表头列数
                    headerColumns = countTableColumns(line);
                    logger.debug("检测到表格开始，列数: {}", headerColumns);

                    // 检查下一行是否是分隔行
                    if (i + 1 < lines.length) {
                        String nextLine = lines[i + 1].trim();
                        if (!isValidSeparatorRow(nextLine, headerColumns)) {
                            logger.warn("表格分隔行格式可能不正确: {}", nextLine);
                        }
                    }
                } else {
                    // 验证数据行列数
                    int dataColumns = countTableColumns(line);
                    if (dataColumns != headerColumns) {
                        logger.warn("第{}行列数不匹配，期望{}列，实际{}列: {}",
                                   i + 1, headerColumns, dataColumns, line);
                    }
                }
            } else if (inTable && !line.isEmpty() && !line.startsWith("#")) {
                inTable = false;
                logger.debug("表格结束");
            }
        }

        logger.info("Markdown格式验证完成");
    }

    /**
     * 计算表格行的列数
     */
    private int countTableColumns(String line) {
        if (!line.startsWith("|") || !line.endsWith("|")) {
            return 0;
        }

        // 移除首尾的|，然后按|分割
        String content = line.substring(1, line.length() - 1);
        return content.split("\\|").length;
    }

    /**
     * 验证分隔行格式是否正确
     */
    private boolean isValidSeparatorRow(String line, int expectedColumns) {
        if (!line.startsWith("|") || !line.endsWith("|")) {
            return false;
        }

        int actualColumns = countTableColumns(line);
        if (actualColumns != expectedColumns) {
            return false;
        }

        // 检查每个分隔符是否包含---
        String content = line.substring(1, line.length() - 1);
        String[] parts = content.split("\\|");
        for (String part : parts) {
            if (!part.trim().contains("---")) {
                return false;
            }
        }

        return true;
    }

    /**
     * 验证表格列数一致性
     */
    private void validateColumnConsistency(String content) {
        logger.info("开始验证表格列数一致性...");

        String[] lines = content.split("\n");
        Integer expectedColumns = null;
        int tableRowCount = 0;
        boolean hasInconsistency = false;

        for (int i = 0; i < lines.length; i++) {
            String line = lines[i].trim();

            if (line.startsWith("|") && line.endsWith("|")) {
                int columns = countTableColumns(line);
                tableRowCount++;

                if (expectedColumns == null) {
                    expectedColumns = columns;
                    logger.info("表头列数: {}", expectedColumns);
                } else if (columns != expectedColumns) {
                    logger.error("第{}行列数不一致: 期望{}列，实际{}列", i + 1, expectedColumns, columns);
                    logger.error("问题行内容: {}", line);
                    hasInconsistency = true;
                } else {
                    logger.debug("第{}行列数正确: {}列", i + 1, columns);
                }
            }
        }

        if (hasInconsistency) {
            logger.error("❌ 发现列数不一致问题！");
        } else {
            logger.info("✅ 所有表格行列数一致，共{}列，{}行", expectedColumns, tableRowCount);
        }
    }

    /**
     * 标注颜色的行数据类
     */
    public static class ColoredRowData {
        private String moduleName;
        private String sheetName;
        private int rowIndex;
        private String priority;
        private List<String> cellValues;

        public ColoredRowData() {
            this.cellValues = new ArrayList<>();
            this.priority = "";
        }

        // Getters and Setters
        public String getModuleName() { return moduleName; }
        public void setModuleName(String moduleName) { this.moduleName = moduleName; }

        public String getSheetName() { return sheetName; }
        public void setSheetName(String sheetName) { this.sheetName = sheetName; }

        public int getRowIndex() { return rowIndex; }
        public void setRowIndex(int rowIndex) { this.rowIndex = rowIndex; }

        public String getPriority() { return priority; }
        public void setPriority(String priority) { this.priority = priority; }

        public List<String> getCellValues() { return cellValues; }
        public void setCellValues(List<String> cellValues) { this.cellValues = cellValues; }
    }
}
