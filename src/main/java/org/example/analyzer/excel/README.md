# Excel标注颜色行转整合Markdown表格工具

## 功能说明

本工具用于读取指定Excel文件中的特定工作表，检测标注了颜色的行，并将这些行整合到一个Markdown表格中输出。

## 核心功能

✅ **读取指定工作表**: 处理预定义的工作表列表
✅ **颜色检测**: 自动识别单元格背景色标注
✅ **优先级识别**: 红色标注设为"高"，黄色标注设为"中"
✅ **模块名提取**: 从工作表名称前缀提取模块名
✅ **整合输出**: 生成单个Markdown文件包含所有标注颜色的记录
✅ **新增列**: 在表格最后添加"优先级"和"责任人"列
✅ **格式验证**: 自动验证Markdown表格格式和列数一致性
✅ **统计信息**: 提供按优先级、模块和工作表的分布统计

## 配置说明

### 默认配置
```java
// Excel文件路径
private static final String EXCEL_PATH = "E:\\tools\\mcp_server_config\\excel\\接口优先级分析报告_20250725_144557.xlsx";

// 输出文件路径
private static final String OUTPUT_PATH = "e:\\markdown\\整合的标注颜色记录.md";

// 要处理的工作表列表
private static final List<String> TARGET_SHEETS = Arrays.asList(
    "全局TOP 20 优先修复接口",
    "basedata_TOP20",
    "mdw_TOP20", 
    "ctc_TOP20",
    "gateway_TOP20",
    "crm_TOP20",
    "statistics_TOP20"
);
```

## 使用方法

### 1. 直接运行
```bash
# 编译项目
mvn clean compile

# 运行工具
mvn exec:java -Dexec.mainClass="org.example.analyzer.excel.ExcelColoredRowsToMarkdownConverter"
```

### 2. 在IDE中运行
直接运行 `ExcelColoredRowsToMarkdownConverter.main()` 方法

### 3. 自定义配置
修改类中的常量来自定义：
- Excel文件路径
- 输出文件路径  
- 要处理的工作表列表

## 输出格式

### Markdown表格结构
```markdown
# Excel标注颜色记录整合表

生成时间: 2025-01-25 14:45:57

| 所在模块 | 排名 | 服务模块 | 接口地址 | HTTP方法 | 综合得分 | 主要问题 | ... | 优先级 | 责任人 |
| --- | --- | --- | --- | --- | --- | --- | --- | --- | --- |
| basedata | 1 | basedata | /api/test | POST | 6.68 | 性能问题 | ... | 高 |  |
| gateway | 2 | gateway | /api/login | GET | 5.92 | 异常 | ... | 中 |  |

## 统计信息

- 总计标注颜色的记录数: 15
- 按优先级分布:
  - 高: 8 条
  - 中: 7 条
- 按模块分布:
  - basedata: 5 条
  - gateway: 7 条
  - mdw: 3 条
- 按工作表分布:
  - basedata_TOP20: 5 条
  - gateway_TOP20: 7 条
  - mdw_TOP20: 3 条
```

## 颜色检测机制

### 支持的颜色格式
- XSSF格式的ARGB颜色值
- 传统的颜色索引值
- 自动排除白色背景和自动颜色

### 优先级映射规则
- **红色系列** → 优先级: "高"
  - 包括: #FF0000, #FF6B6B, #DC3545, #E74C3C 等
- **黄色系列** → 优先级: "中"
  - 包括: #FFFF00, #FFC107, #F39C12, #FFD700 等
- **其他颜色** → 优先级: "中"

### 检测逻辑
1. 遍历每个指定的工作表
2. 逐行检查是否有单元格标注了颜色
3. 根据颜色确定优先级（红色=高，黄色=中）
4. 提取标注颜色行的所有数据
5. 按模块名整合到统一表格，添加优先级和责任人列

## 模块名提取规则

- `basedata_TOP20` → 模块名: `basedata`
- `gateway_TOP20` → 模块名: `gateway`  
- `全局TOP 20 优先修复接口` → 模块名: `全局TOP 20 优先修复接口`

## 注意事项

1. **文件格式**: 仅支持 `.xlsx` 格式
2. **工作表结构**: 第一行必须是表头，数据从第二行开始
3. **颜色标注**: 依赖Apache POI的颜色检测API
4. **输出编码**: 使用UTF-8编码保存Markdown文件
5. **路径配置**: 建议使用绝对路径避免路径问题

## 技术依赖

- Java 8+
- Apache POI 5.2.4
- SLF4J 1.7.36
- Logback 1.2.3

## 日志输出

工具运行时会输出详细的处理日志：
- 处理的工作表信息
- 找到的标注颜色行数
- 文件保存路径
- 错误和警告信息

## 扩展说明

如需处理其他Excel文件或工作表，只需修改类中的配置常量：
1. 更新 `EXCEL_PATH` 为新的Excel文件路径
2. 更新 `TARGET_SHEETS` 为要处理的工作表列表
3. 更新 `OUTPUT_PATH` 为期望的输出文件路径

工具会自动处理不同的表格结构和颜色标注方式。
