package org.example.analyzer;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.File;
import java.io.FileInputStream;
import java.io.FilenameFilter;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Excel文件读取器
 * 负责从Excel文件中读取接口性能指标数据
 */
public class ExcelMetricsReader {

    /**
     * 按服务分组的接口数据
     */
    private Map<String, List<InterfaceMetrics>> serviceMetricsMap = new HashMap<String, List<InterfaceMetrics>>();
    
    /**
     * 从指定目录读取所有Excel文件
     */
    public List<InterfaceMetrics> readFromDirectory(String directoryPath) {
        List<InterfaceMetrics> allMetrics = new ArrayList<>();
        
        File directory = new File(directoryPath);
        if (!directory.exists() || !directory.isDirectory()) {
            System.err.println("目录不存在或不是有效目录: " + directoryPath);
            return allMetrics;
        }
        
        File[] files = directory.listFiles(new FilenameFilter() {
            @Override
            public boolean accept(File dir, String name) {
                return name.toLowerCase().endsWith(".xlsx") || name.toLowerCase().endsWith(".xls");
            }
        });
        
        if (files == null || files.length == 0) {
            System.err.println("目录中没有找到Excel文件: " + directoryPath);
            return allMetrics;
        }
        
        System.out.println("找到 " + files.length + " 个Excel文件");
        
        for (File file : files) {
            System.out.println("正在读取文件: " + file.getName());
            List<InterfaceMetrics> metrics = readFromFile(file.getAbsolutePath());
            
            // 从文件名提取服务名称
            String serviceName = extractServiceName(file.getName());
            for (InterfaceMetrics metric : metrics) {
                metric.setServiceName(serviceName);
            }
            
            allMetrics.addAll(metrics);

            // 按服务分组存储
            serviceMetricsMap.put(serviceName, metrics);

            System.out.println("从 " + file.getName() + " 读取到 " + metrics.size() + " 条记录");
        }

        System.out.println("总共读取到 " + allMetrics.size() + " 条接口记录");
        System.out.println("共有 " + serviceMetricsMap.size() + " 个服务模块");
        return allMetrics;
    }
    
    /**
     * 从单个Excel文件读取数据
     */
    public List<InterfaceMetrics> readFromFile(String filePath) {
        List<InterfaceMetrics> metrics = new ArrayList<>();
        
        try (FileInputStream fis = new FileInputStream(filePath);
             Workbook workbook = new XSSFWorkbook(fis)) {
            
            // 读取第一个工作表
            Sheet sheet = workbook.getSheetAt(0);
            if (sheet == null) {
                System.err.println("工作表为空: " + filePath);
                return metrics;
            }
            
            // 跳过标题行，从第二行开始读取数据
            for (int i = 1; i <= sheet.getLastRowNum(); i++) {
                Row row = sheet.getRow(i);
                if (row == null) continue;
                
                InterfaceMetrics metric = parseRowToMetrics(row);
                if (metric != null && isValidMetric(metric)) {
                    metrics.add(metric);
                }
            }
            
        } catch (IOException e) {
            System.err.println("读取Excel文件失败: " + filePath);
            e.printStackTrace();
        }
        
        return metrics;
    }
    
    /**
     * 将Excel行数据解析为InterfaceMetrics对象
     */
    private InterfaceMetrics parseRowToMetrics(Row row) {
        try {
            InterfaceMetrics metrics = new InterfaceMetrics();
            
            // 按照Excel列顺序解析数据
            metrics.setUrl(getCellStringValue(row.getCell(0)));                    // A: url
            metrics.setMethod(getCellStringValue(row.getCell(1)));                 // B: method
            metrics.setCallCount((int) getCellNumericValue(row.getCell(2)));       // C: 次数
            metrics.setErrorCount((int) getCellNumericValue(row.getCell(3)));      // D: 错误数
            metrics.setAvgResponseTime(getCellNumericValue(row.getCell(4)));       // E: RT(ms)
            metrics.setTotalTime(getCellNumericValue(row.getCell(5)));             // F: 总时间(ms)
            metrics.setMaxConcurrency((int) getCellNumericValue(row.getCell(6)));  // G: 最大并发
            metrics.setMaxResponseTime(getCellNumericValue(row.getCell(7)));       // H: 最慢(ms)
            metrics.setRecentError(getCellStringValue(row.getCell(8)));            // I: 最近错误
            
            // 响应时间分布
            metrics.setTime0To10ms((int) getCellNumericValue(row.getCell(9)));     // J: 0-10ms
            metrics.setTime10To100ms((int) getCellNumericValue(row.getCell(10)));  // K: 10-100ms
            metrics.setTime100To500ms((int) getCellNumericValue(row.getCell(11))); // L: 100-500ms
            metrics.setTime500To1000ms((int) getCellNumericValue(row.getCell(12)));// M: 500-1000ms
            metrics.setTime1To10s((int) getCellNumericValue(row.getCell(13)));     // N: 1-10s
            metrics.setTimeOver10s((int) getCellNumericValue(row.getCell(14)));    // O: 10s以上
            
            metrics.setMaxResponseSize((long) getCellNumericValue(row.getCell(15))); // P: 最大响应体大小
            metrics.setTp99(getCellStringValue(row.getCell(16)));                  // Q: TP99
            metrics.setTp95(getCellStringValue(row.getCell(17)));                  // R: TP95
            metrics.setTp90(getCellStringValue(row.getCell(18)));                  // S: TP90
            metrics.setCallSource(getCellStringValue(row.getCell(19)));            // T: 调用源
            metrics.setBusinessExceptionCount((int) getCellNumericValue(row.getCell(20))); // U: 业务异常次数
            
            return metrics;
            
        } catch (Exception e) {
            System.err.println("解析行数据失败，行号: " + (row.getRowNum() + 1));
            e.printStackTrace();
            return null;
        }
    }
    
    /**
     * 获取单元格字符串值
     */
    private String getCellStringValue(Cell cell) {
        if (cell == null) return "";
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                return String.valueOf((long) cell.getNumericCellValue());
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue().trim();
                } catch (Exception e) {
                    return String.valueOf(cell.getNumericCellValue());
                }
            default:
                return "";
        }
    }
    
    /**
     * 获取单元格数值
     */
    private double getCellNumericValue(Cell cell) {
        if (cell == null) return 0.0;
        
        switch (cell.getCellType()) {
            case NUMERIC:
                return cell.getNumericCellValue();
            case STRING:
                try {
                    String value = cell.getStringCellValue().trim();
                    if (value.isEmpty()) return 0.0;
                    return Double.parseDouble(value);
                } catch (NumberFormatException e) {
                    return 0.0;
                }
            case FORMULA:
                try {
                    return cell.getNumericCellValue();
                } catch (Exception e) {
                    return 0.0;
                }
            default:
                return 0.0;
        }
    }
    
    /**
     * 从文件名提取服务名称
     */
    private String extractServiceName(String fileName) {
        String serviceName;

        // 从文件名中提取服务名称，例如：basedata_url详情[汇总_ 2025-07-18 09_40_00]-15天.xlsx -> basedata
        if (fileName.contains("_")) {
            serviceName = fileName.substring(0, fileName.indexOf("_"));
        } else if (fileName.contains("-")) {
            serviceName = fileName.substring(0, fileName.indexOf("-"));
        } else {
            // 移除扩展名
            int dotIndex = fileName.lastIndexOf(".");
            serviceName = dotIndex > 0 ? fileName.substring(0, dotIndex) : fileName;
        }

        // 清理服务名称，移除Excel工作表名称不支持的字符
        serviceName = cleanSheetName(serviceName);

        return serviceName;
    }

    /**
     * 清理工作表名称，移除Excel不支持的字符
     * Excel工作表名称不能包含: \ / ? * [ ] :
     * 长度限制：最多31个字符
     */
    private String cleanSheetName(String name) {
        if (name == null || name.trim().isEmpty()) {
            return "Unknown";
        }

        // 移除Excel不支持的字符
        String cleaned = name.replaceAll("[\\\\/:*?\\[\\]]", "");

        // 移除前后空格
        cleaned = cleaned.trim();

        // 如果清理后为空，使用默认名称
        if (cleaned.isEmpty()) {
            cleaned = "Service";
        }

        // 限制长度（Excel工作表名称最多31个字符，为TOP10后缀预留空间）
        if (cleaned.length() > 20) {
            cleaned = cleaned.substring(0, 20);
        }

        return cleaned;
    }
    
    /**
     * 验证指标数据是否有效
     */
    private boolean isValidMetric(InterfaceMetrics metric) {
        return metric.getUrl() != null &&
               !metric.getUrl().trim().isEmpty() &&
               metric.getCallCount() > 0;
    }

    /**
     * 获取按服务分组的接口数据
     */
    public Map<String, List<InterfaceMetrics>> getServiceMetricsMap() {
        return serviceMetricsMap;
    }
}