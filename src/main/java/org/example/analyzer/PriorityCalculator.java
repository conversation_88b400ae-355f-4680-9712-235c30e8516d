package org.example.analyzer;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 优先级计算器
 * 负责计算接口的综合优先级得分
 */
public class PriorityCalculator {
    
    /**
     * 计算单个接口的优先级得分（简化版本）
     */
    public void calculatePriority(InterfaceMetrics metrics, List<InterfaceMetrics> allMetrics) {
        // 1. 计算响应时间性能得分
        double responsePerformanceScore = calculateResponsePerformanceScore(metrics);
        metrics.setBusinessImpactScore(responsePerformanceScore); // 复用字段存储

        // 2. 计算调用频率影响得分
        double callFrequencyScore = calculateCallFrequencyScore(metrics, allMetrics);
        metrics.setPerformanceSeverityScore(callFrequencyScore); // 复用字段存储

        // 3. 计算稳定性风险得分
        double stabilityRiskScore = calculateStabilityRiskScore(metrics);
        metrics.setStabilityRiskScore(stabilityRiskScore);

        // 4. 计算系统压力得分
        double systemPressureScore = calculateSystemPressureScore(metrics);
        metrics.setResourceConsumptionScore(systemPressureScore); // 复用字段存储

        // 5. 计算最终综合得分
        double finalScore = responsePerformanceScore * PriorityConfig.RESPONSE_PERFORMANCE_WEIGHT +
                           callFrequencyScore * PriorityConfig.CALL_FREQUENCY_WEIGHT +
                           stabilityRiskScore * PriorityConfig.STABILITY_RISK_WEIGHT +
                           systemPressureScore * PriorityConfig.SYSTEM_PRESSURE_WEIGHT;

        // 6. 检查URL关键字过滤
        if (containsLowPriorityKeywords(metrics.getUrl())) {
            finalScore = PriorityConfig.LOW_PRIORITY_SCORE;
            System.out.println("URL包含关键字，权重已降至最低: " + metrics.getUrl());
        }

        metrics.setFinalPriorityScore(finalScore);
    }
    
    /**
     * 计算所有接口的优先级并排序
     */
    public List<InterfaceMetrics> calculateAndSortPriorities(List<InterfaceMetrics> allMetrics) {
        // 先计算所有接口的得分
        for (InterfaceMetrics metrics : allMetrics) {
            calculatePriority(metrics, allMetrics);
        }
        
        // 按最终得分降序排序
        List<InterfaceMetrics> sortedMetrics = new ArrayList<InterfaceMetrics>(allMetrics);
        Collections.sort(sortedMetrics, new Comparator<InterfaceMetrics>() {
            @Override
            public int compare(InterfaceMetrics o1, InterfaceMetrics o2) {
                return Double.compare(o2.getFinalPriorityScore(), o1.getFinalPriorityScore());
            }
        });
        
        // 设置排名
        for (int i = 0; i < sortedMetrics.size(); i++) {
            sortedMetrics.get(i).setPriorityRank(i + 1);
        }
        
        return sortedMetrics;
    }
    
    /**
     * 获取TOP N接口
     */
    public List<InterfaceMetrics> getTopNInterfaces(List<InterfaceMetrics> sortedMetrics, int topN) {
        List<InterfaceMetrics> topNMetrics = new ArrayList<InterfaceMetrics>();
        int limit = Math.min(topN, sortedMetrics.size());
        for (int i = 0; i < limit; i++) {
            topNMetrics.add(sortedMetrics.get(i));
        }
        return topNMetrics;
    }

    /**
     * 计算每个服务的TOP N接口
     */
    public Map<String, List<InterfaceMetrics>> calculateServiceTopN(Map<String, List<InterfaceMetrics>> serviceMetricsMap, int topN) {
        Map<String, List<InterfaceMetrics>> serviceTopNMap = new HashMap<String, List<InterfaceMetrics>>();

        for (Map.Entry<String, List<InterfaceMetrics>> entry : serviceMetricsMap.entrySet()) {
            String serviceName = entry.getKey();
            List<InterfaceMetrics> serviceMetrics = entry.getValue();

            // 为每个服务的接口计算优先级
            List<InterfaceMetrics> sortedServiceMetrics = calculateAndSortPriorities(serviceMetrics);

            // 获取该服务的TOP N
            List<InterfaceMetrics> serviceTopN = getTopNInterfaces(sortedServiceMetrics, topN);

            // 重新设置排名（在服务内的排名）
            for (int i = 0; i < serviceTopN.size(); i++) {
                serviceTopN.get(i).setPriorityRank(i + 1);
            }

            serviceTopNMap.put(serviceName, serviceTopN);

            System.out.println("服务 [" + serviceName + "] TOP " + topN + " 接口计算完成，共 " + serviceTopN.size() + " 个接口");
        }

        return serviceTopNMap;
    }
    
    /**
     * 计算响应时间性能得分
     * 基于：RT(ms)、最慢(ms)、响应时间分布(0-10ms到10s以上)
     */
    private double calculateResponsePerformanceScore(InterfaceMetrics metrics) {
        // 1. 平均响应时间得分 (40%)
        double avgRtScore = calculateAvgRtScore(metrics.getAvgResponseTime());

        // 2. 最慢响应时间得分 (30%)
        double maxRtScore = calculateMaxRtScore(metrics.getMaxResponseTime());

        // 3. 响应时间分布得分 (30%)
        double distributionScore = calculateRtDistributionScore(metrics);

        return avgRtScore * PriorityConfig.AVG_RT_WEIGHT +
               maxRtScore * PriorityConfig.MAX_RT_WEIGHT +
               distributionScore * PriorityConfig.RT_DISTRIBUTION_WEIGHT;
    }

    /**
     * 计算调用频率影响得分
     * 基于：次数、总时间(ms)
     */
    private double calculateCallFrequencyScore(InterfaceMetrics metrics, List<InterfaceMetrics> allMetrics) {
        // 1. 调用次数得分 (60%)
        double callCountScore = calculateCallCountScore(metrics.getCallCount());

        // 2. 总时间消耗得分 (40%)
        double totalTimeScore = calculateTotalTimeScore(metrics, allMetrics);

        return callCountScore * PriorityConfig.CALL_COUNT_WEIGHT +
               totalTimeScore * PriorityConfig.TOTAL_TIME_WEIGHT;
    }
    
    /**
     * 计算稳定性风险得分（简化版本）
     * 基于：错误数、最近错误
     */
    private double calculateStabilityRiskScore(InterfaceMetrics metrics) {
        // 1. 错误率得分 (70%)
        double errorRateScore = calculateErrorRateScore(metrics);

        // 2. 最近错误得分 (30%)
        double recentErrorScore = calculateRecentErrorScore(metrics);

        return errorRateScore * PriorityConfig.ERROR_RATE_WEIGHT +
               recentErrorScore * PriorityConfig.RECENT_ERROR_WEIGHT;
    }

    /**
     * 计算系统压力得分
     * 基于：最大并发、最大响应体大小
     */
    private double calculateSystemPressureScore(InterfaceMetrics metrics) {
        // 1. 并发压力得分 (60%)
        double concurrencyScore = calculateConcurrencyScore(metrics.getMaxConcurrency());

        // 2. 响应体大小得分 (40%)
        double responseSizeScore = calculateResponseSizeScore(metrics.getMaxResponseSize());

        return concurrencyScore * PriorityConfig.CONCURRENCY_WEIGHT +
               responseSizeScore * PriorityConfig.RESPONSE_SIZE_WEIGHT;
    }
    
    // ==================== 具体评分方法 ====================

    /**
     * 计算平均响应时间得分
     */
    private double calculateAvgRtScore(double avgRT) {
        if (avgRT > PriorityConfig.CRITICAL_RT_THRESHOLD) {
            return 10.0; // 严重
        } else if (avgRT > PriorityConfig.HIGH_RT_THRESHOLD) {
            return 8.0;  // 高
        } else if (avgRT > PriorityConfig.MEDIUM_RT_THRESHOLD) {
            return 6.0;  // 中等
        } else if (avgRT > PriorityConfig.LOW_RT_THRESHOLD) {
            return 4.0;  // 轻微
        } else {
            return 2.0;  // 正常
        }
    }

    /**
     * 计算最慢响应时间得分
     */
    private double calculateMaxRtScore(double maxRT) {
        if (maxRT > PriorityConfig.CRITICAL_RT_THRESHOLD * 2) {
            return 10.0; // 极慢
        } else if (maxRT > PriorityConfig.CRITICAL_RT_THRESHOLD) {
            return 8.0;  // 很慢
        } else if (maxRT > PriorityConfig.HIGH_RT_THRESHOLD) {
            return 6.0;  // 慢
        } else if (maxRT > PriorityConfig.MEDIUM_RT_THRESHOLD) {
            return 4.0;  // 偏慢
        } else {
            return 2.0;  // 正常
        }
    }
    
    /**
     * 计算响应时间分布得分
     * 基于：0-10ms、10-100ms、100-500ms、500-1000ms、1-10s、10s以上
     */
    private double calculateRtDistributionScore(InterfaceMetrics metrics) {
        if (metrics.getCallCount() == 0) return 2.0;

        int totalCalls = metrics.getCallCount();

        // 计算各时间段占比
        double over10sRatio = (double) metrics.getTimeOver10s() / totalCalls;
        double slow1To10sRatio = (double) metrics.getTime1To10s() / totalCalls;
        double medium500To1000Ratio = (double) metrics.getTime500To1000ms() / totalCalls;
        double fast0To100Ratio = (double) (metrics.getTime0To10ms() + metrics.getTime10To100ms()) / totalCalls;

        // 评分逻辑：越慢的请求占比越高，得分越高
        if (over10sRatio > 0.5) {
            return 10.0; // 超过50%的请求都很慢
        } else if (over10sRatio > 0.2) {
            return 8.0;  // 20-50%的请求很慢
        } else if (slow1To10sRatio > 0.6) {
            return 6.0;  // 大部分请求偏慢
        } else if (medium500To1000Ratio > 0.4) {
            return 4.0;  // 部分请求偏慢
        } else if (fast0To100Ratio > 0.8) {
            return 2.0;  // 大部分请求都很快
        } else {
            return 3.0;  // 中等分布
        }
    }
    
    /**
     * 计算调用次数得分
     */
    private double calculateCallCountScore(int callCount) {
        if (callCount > PriorityConfig.ULTRA_HIGH_FREQUENCY_THRESHOLD) {
            return 10.0; // 超高频
        } else if (callCount > PriorityConfig.HIGH_FREQUENCY_THRESHOLD) {
            return 8.0;  // 高频
        } else if (callCount > PriorityConfig.MEDIUM_FREQUENCY_THRESHOLD) {
            return 6.0;  // 中频
        } else if (callCount > 50) {
            return 4.0;  // 低频
        } else {
            return 2.0;  // 极低频
        }
    }

    /**
     * 计算总时间消耗得分
     */
    private double calculateTotalTimeScore(InterfaceMetrics metrics, List<InterfaceMetrics> allMetrics) {
        // 计算在所有接口中的总时间排名
        long betterCount = 0;
        for (InterfaceMetrics m : allMetrics) {
            if (m.getTotalTime() > metrics.getTotalTime()) {
                betterCount++;
            }
        }

        double rankPercentile = (double) betterCount / allMetrics.size();

        if (rankPercentile <= 0.1) {
            return 10.0; // 前10%
        } else if (rankPercentile <= 0.2) {
            return 8.0;  // 前20%
        } else if (rankPercentile <= 0.5) {
            return 6.0;  // 前50%
        } else {
            return 4.0;  // 其他
        }
    }
    
    /**
     * 计算错误率得分
     */
    private double calculateErrorRateScore(InterfaceMetrics metrics) {
        double errorRate = metrics.getErrorRate();
        if (errorRate > PriorityConfig.CRITICAL_ERROR_RATE) {
            return 10.0; // 严重错误率
        } else if (errorRate > PriorityConfig.HIGH_ERROR_RATE) {
            return 8.0;  // 高错误率
        } else if (errorRate > PriorityConfig.MEDIUM_ERROR_RATE) {
            return 6.0;  // 中等错误率
        } else if (errorRate > 0) {
            return 4.0;  // 轻微错误
        } else {
            return 2.0;  // 无错误
        }
    }
    
    /**
     * 计算最近错误得分
     */
    private double calculateRecentErrorScore(InterfaceMetrics metrics) {
        if (!metrics.hasRecentError()) {
            return 2.0; // 无错误
        }

        String recentError = metrics.getRecentError().toLowerCase();

        // 检查超时错误
        for (String keyword : PriorityConfig.TIMEOUT_ERROR_KEYWORDS) {
            if (recentError.contains(keyword.toLowerCase())) {
                return 10.0; // 超时错误最严重
            }
        }

        // 检查连接错误
        for (String keyword : PriorityConfig.CONNECTION_ERROR_KEYWORDS) {
            if (recentError.contains(keyword.toLowerCase())) {
                return 9.0; // 连接错误
            }
        }

        // 检查业务异常
        for (String keyword : PriorityConfig.BUSINESS_ERROR_KEYWORDS) {
            if (recentError.contains(keyword)) {
                return 6.0; // 业务异常
            }
        }

        return 7.0; // 其他错误
    }

    /**
     * 计算并发压力得分
     */
    private double calculateConcurrencyScore(int maxConcurrency) {
        if (maxConcurrency > PriorityConfig.HIGH_CONCURRENCY_THRESHOLD) {
            return 10.0; // 高并发压力
        } else if (maxConcurrency > PriorityConfig.MEDIUM_CONCURRENCY_THRESHOLD) {
            return 8.0;  // 中等并发压力
        } else if (maxConcurrency > PriorityConfig.LOW_CONCURRENCY_THRESHOLD) {
            return 6.0;  // 轻微并发压力
        } else if (maxConcurrency > 1) {
            return 4.0;  // 有并发
        } else {
            return 2.0;  // 无并发压力
        }
    }

    /**
     * 计算响应体大小得分
     */
    private double calculateResponseSizeScore(long maxResponseSize) {
        if (maxResponseSize > PriorityConfig.LARGE_RESPONSE_SIZE) {
            return 10.0; // 超大响应体
        } else if (maxResponseSize > PriorityConfig.MEDIUM_RESPONSE_SIZE) {
            return 8.0;  // 大响应体
        } else if (maxResponseSize > PriorityConfig.SMALL_RESPONSE_SIZE) {
            return 6.0;  // 中等响应体
        } else if (maxResponseSize > 10 * 1024) { // 10KB
            return 4.0;  // 小响应体
        } else {
            return 2.0;  // 很小的响应体
        }
    }

    /**
     * 检查URL是否包含需要降权的关键字
     */
    private boolean containsLowPriorityKeywords(String url) {
        if (!PriorityConfig.ENABLE_URL_KEYWORD_FILTER || url == null) {
            return false;
        }

        String[] keywords = PriorityConfig.LOW_PRIORITY_URL_KEYWORDS.split(",");
        String lowerUrl = url.toLowerCase();

        for (String keyword : keywords) {
            if (lowerUrl.contains(keyword.trim().toLowerCase())) {
                return true;
            }
        }
        return false;
    }
}