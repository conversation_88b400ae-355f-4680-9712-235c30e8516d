package org.example.analyzer.curl;

import okhttp3.Response;
import okhttp3.ResponseBody;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 文件下载管理器
 * 负责处理HTTP响应，下载并保存Excel文件
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
public class FileDownloadManager {
    
    private final DownloadConfig config;
    private static final Pattern FILENAME_PATTERN = Pattern.compile("filename[*]?=([^;]+)");
    
    public FileDownloadManager(DownloadConfig config) {
        this.config = config;
    }
    
    /**
     * 下载文件并保存到指定目录
     * 
     * @param response HTTP响应
     * @param curlCommand cURL命令对象
     * @return 保存的文件完整路径
     * @throws CurlProcessException 下载失败时抛出异常
     */
    public String downloadFile(Response response, CurlCommand curlCommand) throws CurlProcessException {
        ResponseBody body = response.body();
        if (body == null) {
            throw new CurlProcessException(
                CurlProcessException.ErrorType.DOWNLOAD_ERROR,
                "响应体为空",
                curlCommand.getSourceFileName()
            );
        }
        
        try {
            // 确保下载目录存在
            ensureDirectoryExists(config.getDownloadDirectory());
            
            // 生成文件名
            String fileName = generateFileName(response, curlCommand);
            String filePath = Paths.get(config.getDownloadDirectory(), fileName).toString();
            
            // 处理文件名冲突
            filePath = handleFileNameConflict(filePath);
            
            if (config.isEnableVerboseLogging()) {
                System.out.println("开始下载文件: " + fileName);
                System.out.println("保存路径: " + filePath);
            }
            
            // 下载文件
            try (InputStream inputStream = body.byteStream();
                 FileOutputStream outputStream = new FileOutputStream(filePath);
                 BufferedOutputStream bufferedOutput = new BufferedOutputStream(outputStream)) {
                
                byte[] buffer = new byte[8192];
                int bytesRead;
                long totalBytes = 0;
                
                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    bufferedOutput.write(buffer, 0, bytesRead);
                    totalBytes += bytesRead;
                }
                
                bufferedOutput.flush();
                
                if (config.isEnableVerboseLogging()) {
                    System.out.println("文件下载完成，大小: " + formatFileSize(totalBytes));
                }
                
                return filePath;
                
            }
            
        } catch (IOException e) {
            throw new CurlProcessException(
                CurlProcessException.ErrorType.FILE_IO_ERROR,
                "文件下载失败: " + e.getMessage(),
                curlCommand.getSourceFileName(),
                e
            );
        } finally {
            response.close();
        }
    }
    
    /**
     * 生成文件名
     * 规则：从cURL文件名提取服务标识 + 时间戳 + .xlsx
     */
    private String generateFileName(Response response, CurlCommand curlCommand) {
        // 尝试从响应头获取原始文件名
        String originalFileName = extractFileNameFromResponse(response);
        
        // 从cURL文件名提取服务标识
        String serviceIdentifier = extractServiceIdentifier(curlCommand.getSourceFileName());
        
        // 生成时间戳
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"));
        
        // 构建最终文件名
        if (originalFileName != null && !originalFileName.isEmpty()) {
            // 如果响应头中有文件名，使用服务标识作为前缀
            String nameWithoutExt = removeFileExtension(originalFileName);
            return serviceIdentifier + "_" + nameWithoutExt + "_" + timestamp + ".xlsx";
        } else {
            // 如果没有原始文件名，使用默认格式
            return serviceIdentifier + "_导出文件_" + timestamp + ".xlsx";
        }
    }
    
    /**
     * 从HTTP响应头中提取文件名
     */
    private String extractFileNameFromResponse(Response response) {
        String contentDisposition = response.header("Content-Disposition");
        if (contentDisposition != null) {
            Matcher matcher = FILENAME_PATTERN.matcher(contentDisposition);
            if (matcher.find()) {
                String fileName = matcher.group(1);
                // 移除引号
                fileName = fileName.replaceAll("^\"|\"$", "");
                fileName = fileName.replaceAll("^'|'$", "");
                return fileName;
            }
        }
        return null;
    }
    
    /**
     * 从cURL文件名中提取服务标识
     * 例如：service-a-curl.txt -> service-a
     */
    private String extractServiceIdentifier(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "unknown-service";
        }
        
        // 移除文件扩展名
        String nameWithoutExt = removeFileExtension(fileName);
        
        // 移除常见的后缀（如-curl, _curl等）
        nameWithoutExt = nameWithoutExt.replaceAll("[-_]?curl$", "");
        
        // 如果处理后为空，使用原始文件名
        if (nameWithoutExt.isEmpty()) {
            nameWithoutExt = removeFileExtension(fileName);
        }
        
        return nameWithoutExt;
    }
    
    /**
     * 移除文件扩展名
     */
    private String removeFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return fileName;
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(0, lastDotIndex);
        }
        
        return fileName;
    }
    
    /**
     * 处理文件名冲突
     */
    private String handleFileNameConflict(String originalPath) {
        Path path = Paths.get(originalPath);
        if (!Files.exists(path)) {
            return originalPath;
        }
        
        String directory = path.getParent().toString();
        String fileName = path.getFileName().toString();
        String nameWithoutExt = removeFileExtension(fileName);
        String extension = fileName.substring(nameWithoutExt.length());
        
        int counter = 1;
        String newPath;
        do {
            String newFileName = nameWithoutExt + "_(" + counter + ")" + extension;
            newPath = Paths.get(directory, newFileName).toString();
            counter++;
        } while (Files.exists(Paths.get(newPath)));
        
        return newPath;
    }
    
    /**
     * 确保目录存在
     */
    private void ensureDirectoryExists(String directoryPath) throws CurlProcessException {
        try {
            Path path = Paths.get(directoryPath);
            if (!Files.exists(path)) {
                Files.createDirectories(path);
                if (config.isEnableVerboseLogging()) {
                    System.out.println("创建目录: " + directoryPath);
                }
            }
        } catch (IOException e) {
            throw new CurlProcessException(
                CurlProcessException.ErrorType.DIRECTORY_CREATE_ERROR,
                "无法创建目录: " + directoryPath + ", 错误: " + e.getMessage(),
                e
            );
        }
    }
    
    /**
     * 格式化文件大小
     */
    private String formatFileSize(long bytes) {
        if (bytes < 1024) {
            return bytes + " B";
        } else if (bytes < 1024 * 1024) {
            return String.format("%.2f KB", bytes / 1024.0);
        } else {
            return String.format("%.2f MB", bytes / (1024.0 * 1024.0));
        }
    }
}
