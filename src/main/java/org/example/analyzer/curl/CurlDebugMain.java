package org.example.analyzer.curl;

import java.io.File;
import java.io.IOException;
import java.nio.file.Files;

/**
 * cURL调试主程序
 * 用于详细对比原始cURL命令和解析结果
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
public class CurlDebugMain {
    
    public static void main(String[] args) {
        // 配置参数
        String curlDirectory = "D:\\Users\\ex_wuyh42.CN\\Desktop\\生产环境-慢接口\\monitor-export-curl";
        String testFileName = "basedata-export.txt"; // 指定要调试的文件
        
        try {
            System.out.println("=== cURL命令调试工具 ===");
            System.out.println("目标文件: " + testFileName);
            System.out.println();
            
            // 读取原始cURL文件
            File curlFile = new File(curlDirectory, testFileName);
            if (!curlFile.exists()) {
                System.err.println("❌ 文件不存在: " + curlFile.getAbsolutePath());
                return;
            }
            
            String originalCurl = new String(Files.readAllBytes(curlFile.toPath()), "UTF-8").trim();
            
            System.out.println("📄 原始cURL命令:");
            System.out.println("----------------------------------------");
            System.out.println(originalCurl);
            System.out.println("----------------------------------------");
            System.out.println();
            
            // 解析cURL命令
            CurlParser parser = new CurlParser();
            String fileNameWithoutExt = testFileName.replaceAll("\\.txt$", "");
            CurlCommand command = parser.parse(originalCurl, fileNameWithoutExt);
            
            System.out.println("🔍 解析结果详情:");
            System.out.println("URL: " + command.getUrl());
            System.out.println("方法: " + command.getMethod());
            System.out.println("请求头数量: " + command.getHeaders().size());
            System.out.println();
            
            System.out.println("📋 所有请求头:");
            for (String key : command.getHeaders().keySet()) {
                String value = command.getHeaders().get(key);
                // 对敏感信息进行部分隐藏
                if (key.toLowerCase().contains("authorization") || 
                    key.toLowerCase().contains("cookie") ||
                    key.toLowerCase().contains("token")) {
                    if (value.length() > 20) {
                        value = value.substring(0, 10) + "..." + value.substring(value.length() - 10);
                    }
                }
                System.out.println("  " + key + ": " + value);
            }
            System.out.println();
            
            if (command.getBody() != null && !command.getBody().isEmpty()) {
                System.out.println("📦 请求体:");
                String body = command.getBody();
                if (body.length() > 200) {
                    System.out.println(body.substring(0, 200) + "... (总长度: " + body.length() + " 字符)");
                } else {
                    System.out.println(body);
                }
                System.out.println();
            } else {
                System.out.println("⚠️  请求体为空或解析失败");
                System.out.println();
            }
            
            // 检查关键参数
            System.out.println("🔧 关键参数检查:");
            checkCriticalHeaders(command);
            
            System.out.println("\n💡 建议:");
            System.out.println("1. 请对比上述解析结果与Postman中的参数是否完全一致");
            System.out.println("2. 特别注意Cookie、Authorization等认证相关的请求头");
            System.out.println("3. 检查请求体的格式和内容是否正确");
            System.out.println("4. 确认URL中的参数是否完整");
            
        } catch (Exception e) {
            System.err.println("❌ 调试过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    /**
     * 检查关键请求头
     */
    private static void checkCriticalHeaders(CurlCommand command) {
        // 检查User-Agent
        if (!command.getHeaders().containsKey("User-Agent")) {
            System.out.println("⚠️  缺少User-Agent请求头");
        } else {
            System.out.println("✓ User-Agent: " + command.getHeaders().get("User-Agent"));
        }
        
        // 检查认证相关
        boolean hasAuth = false;
        for (String key : command.getHeaders().keySet()) {
            if (key.toLowerCase().contains("authorization") || 
                key.toLowerCase().contains("cookie") ||
                key.toLowerCase().contains("token")) {
                hasAuth = true;
                System.out.println("✓ 认证头: " + key);
            }
        }
        if (!hasAuth) {
            System.out.println("⚠️  未检测到认证相关请求头");
        }
        
        // 检查Content-Type
        if (command.getBody() != null && !command.getBody().isEmpty()) {
            if (!command.getHeaders().containsKey("Content-Type")) {
                System.out.println("⚠️  有请求体但缺少Content-Type");
            } else {
                System.out.println("✓ Content-Type: " + command.getHeaders().get("Content-Type"));
            }
        }
        
        // 检查Accept
        if (!command.getHeaders().containsKey("Accept")) {
            System.out.println("⚠️  缺少Accept请求头");
        } else {
            System.out.println("✓ Accept: " + command.getHeaders().get("Accept"));
        }
        
        // 检查Referer
        if (!command.getHeaders().containsKey("Referer")) {
            System.out.println("⚠️  缺少Referer请求头（可能影响某些网站的验证）");
        } else {
            System.out.println("✓ Referer: " + command.getHeaders().get("Referer"));
        }
    }
}
