package org.example.analyzer.curl;

import okhttp3.Response;
import okhttp3.ResponseBody;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * HTTP响应分析器
 * 用于分析服务器响应内容，判断是否为预期的Excel文件
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
public class ResponseAnalyzer {
    
    private final DownloadConfig config;
    
    public ResponseAnalyzer(DownloadConfig config) {
        this.config = config;
    }
    
    /**
     * 分析响应结果
     */
    public static class AnalysisResult {
        private final boolean isValidExcel;
        private final String contentType;
        private final String analysis;
        private final String savedFilePath;
        
        public AnalysisResult(boolean isValidExcel, String contentType, String analysis, String savedFilePath) {
            this.isValidExcel = isValidExcel;
            this.contentType = contentType;
            this.analysis = analysis;
            this.savedFilePath = savedFilePath;
        }
        
        public boolean isValidExcel() { return isValidExcel; }
        public String getContentType() { return contentType; }
        public String getAnalysis() { return analysis; }
        public String getSavedFilePath() { return savedFilePath; }
    }
    
    /**
     * 分析HTTP响应
     * 
     * @param response HTTP响应
     * @param curlCommand cURL命令对象
     * @return 分析结果
     * @throws CurlProcessException 分析失败时抛出异常
     */
    public AnalysisResult analyzeResponse(Response response, CurlCommand curlCommand) throws CurlProcessException {
        String contentType = response.header("Content-Type", "");
        
        if (config.isEnableVerboseLogging()) {
            System.out.println("开始分析响应内容...");
            System.out.println("Content-Type: " + contentType);
            System.out.println("Content-Length: " + response.header("Content-Length", "未知"));
        }
        
        // 保存响应内容到临时文件进行分析
        String tempFilePath = saveResponseToTempFile(response, curlCommand);
        
        try {
            // 分析文件内容
            AnalysisResult result = analyzeFileContent(tempFilePath, contentType, curlCommand);
            
            if (config.isEnableVerboseLogging()) {
                System.out.println("分析结果: " + (result.isValidExcel() ? "✓ 有效Excel文件" : "✗ 非Excel文件"));
                System.out.println("分析详情: " + result.getAnalysis());
            }
            
            return result;
            
        } finally {
            // 清理临时文件（如果分析失败）
            if (!new File(tempFilePath).delete() && config.isEnableVerboseLogging()) {
                System.out.println("警告: 无法删除临时文件: " + tempFilePath);
            }
        }
    }
    
    /**
     * 保存响应内容到临时文件
     */
    private String saveResponseToTempFile(Response response, CurlCommand curlCommand) throws CurlProcessException {
        ResponseBody body = response.body();
        if (body == null) {
            throw new CurlProcessException(
                CurlProcessException.ErrorType.DOWNLOAD_ERROR,
                "响应体为空",
                curlCommand.getSourceFileName()
            );
        }
        
        try {
            // 生成临时文件名
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss_SSS"));
            String tempFileName = "temp_response_" + curlCommand.getSourceFileName() + "_" + timestamp + ".tmp";
            String tempFilePath = Paths.get(config.getDownloadDirectory(), tempFileName).toString();
            
            // 确保目录存在
            new File(config.getDownloadDirectory()).mkdirs();
            
            // 保存文件
            try (FileOutputStream fos = new FileOutputStream(tempFilePath)) {
                fos.write(body.bytes());
                fos.flush();
            }
            
            if (config.isEnableVerboseLogging()) {
                System.out.println("响应内容已保存到临时文件: " + tempFilePath);
                System.out.println("文件大小: " + new File(tempFilePath).length() + " 字节");
            }
            
            return tempFilePath;
            
        } catch (IOException e) {
            throw new CurlProcessException(
                CurlProcessException.ErrorType.FILE_IO_ERROR,
                "保存响应内容到临时文件失败: " + e.getMessage(),
                curlCommand.getSourceFileName(),
                e
            );
        }
    }
    
    /**
     * 分析文件内容
     */
    private AnalysisResult analyzeFileContent(String filePath, String contentType, CurlCommand curlCommand) throws CurlProcessException {
        File file = new File(filePath);
        
        try {
            // 读取文件头部字节进行分析
            byte[] header = readFileHeader(file, 512);
            
            // 分析文件类型
            FileTypeAnalysis typeAnalysis = analyzeFileType(header, contentType);
            
            if (typeAnalysis.isExcel) {
                // 如果是Excel文件，重命名为最终文件名
                String finalFilePath = generateFinalFileName(curlCommand);
                if (file.renameTo(new File(finalFilePath))) {
                    return new AnalysisResult(true, contentType, typeAnalysis.analysis, finalFilePath);
                } else {
                    // 重命名失败，复制文件
                    copyFile(filePath, finalFilePath);
                    return new AnalysisResult(true, contentType, typeAnalysis.analysis, finalFilePath);
                }
            } else {
                // 如果不是Excel文件，保存为文本文件以便查看
                String errorFilePath = generateErrorFileName(curlCommand, contentType);
                if (file.renameTo(new File(errorFilePath))) {
                    System.out.println("❌ 错误响应已保存到: " + errorFilePath);
                    System.out.println("💡 请检查该文件内容以了解服务器返回的具体错误信息");

                    return new AnalysisResult(false, contentType,
                        typeAnalysis.analysis + "\n\n错误响应已保存到: " + errorFilePath +
                        "\n建议：请检查该文件内容，通常包含认证失败或参数错误的具体信息",
                        errorFilePath);
                } else {
                    return new AnalysisResult(false, contentType, typeAnalysis.analysis, filePath);
                }
            }
            
        } catch (IOException e) {
            throw new CurlProcessException(
                CurlProcessException.ErrorType.FILE_IO_ERROR,
                "分析文件内容失败: " + e.getMessage(),
                curlCommand.getSourceFileName(),
                e
            );
        }
    }
    
    /**
     * 文件类型分析结果
     */
    private static class FileTypeAnalysis {
        final boolean isExcel;
        final String analysis;
        
        FileTypeAnalysis(boolean isExcel, String analysis) {
            this.isExcel = isExcel;
            this.analysis = analysis;
        }
    }
    
    /**
     * 分析文件类型
     */
    private FileTypeAnalysis analyzeFileType(byte[] header, String contentType) {
        StringBuilder analysis = new StringBuilder();
        
        // 检查Excel文件魔数
        if (header.length >= 4) {
            // Excel 2007+ (.xlsx) 文件魔数: PK (ZIP格式)
            if (header[0] == 0x50 && header[1] == 0x4B) {
                analysis.append("检测到ZIP格式文件头 (可能是.xlsx文件)");
                return new FileTypeAnalysis(true, analysis.toString());
            }
            
            // Excel 97-2003 (.xls) 文件魔数
            if (header.length >= 8 && 
                header[0] == (byte)0xD0 && header[1] == (byte)0xCF && 
                header[2] == (byte)0x11 && header[3] == (byte)0xE0) {
                analysis.append("检测到OLE2格式文件头 (可能是.xls文件)");
                return new FileTypeAnalysis(true, analysis.toString());
            }
        }
        
        // 检查是否为HTML内容
        String headerStr = new String(header, 0, Math.min(header.length, 200)).toLowerCase();
        if (headerStr.contains("<html") || headerStr.contains("<!doctype html") || 
            headerStr.contains("<head") || headerStr.contains("<body")) {
            analysis.append("检测到HTML内容，可能是错误页面或登录页面");
            
            // 尝试提取有用信息
            if (headerStr.contains("login") || headerStr.contains("登录")) {
                analysis.append(" - 可能需要重新登录");
            }
            if (headerStr.contains("error") || headerStr.contains("错误")) {
                analysis.append(" - 可能是错误页面");
            }
            if (headerStr.contains("unauthorized") || headerStr.contains("403") || headerStr.contains("401")) {
                analysis.append(" - 可能是认证失败");
            }
            
            return new FileTypeAnalysis(false, analysis.toString());
        }
        
        // 检查是否为JSON错误响应
        if (headerStr.trim().startsWith("{") || headerStr.trim().startsWith("[")) {
            analysis.append("检测到JSON格式响应，可能是API错误信息");
            return new FileTypeAnalysis(false, analysis.toString());
        }
        
        // 其他情况
        analysis.append("未知文件格式，Content-Type: ").append(contentType);
        analysis.append("，文件头: ").append(bytesToHex(header, 16));
        
        // 如果Content-Type声明是Excel，尝试信任它
        if (contentType != null && (
            contentType.toLowerCase().contains("excel") ||
            contentType.toLowerCase().contains("spreadsheet") ||
            contentType.toLowerCase().contains("application/octet-stream"))) {
            analysis.append(" - 根据Content-Type判断可能是Excel文件，尝试处理");
            return new FileTypeAnalysis(true, analysis.toString());
        }
        
        return new FileTypeAnalysis(false, analysis.toString());
    }
    
    /**
     * 读取文件头部字节
     */
    private byte[] readFileHeader(File file, int maxBytes) throws IOException {
        try (java.io.FileInputStream fis = new java.io.FileInputStream(file)) {
            byte[] buffer = new byte[maxBytes];
            int bytesRead = fis.read(buffer);
            if (bytesRead < maxBytes) {
                byte[] result = new byte[bytesRead];
                System.arraycopy(buffer, 0, result, 0, bytesRead);
                return result;
            }
            return buffer;
        }
    }
    
    /**
     * 字节数组转十六进制字符串
     */
    private String bytesToHex(byte[] bytes, int maxBytes) {
        StringBuilder sb = new StringBuilder();
        int limit = Math.min(bytes.length, maxBytes);
        for (int i = 0; i < limit; i++) {
            sb.append(String.format("%02X ", bytes[i]));
        }
        return sb.toString().trim();
    }
    
    /**
     * 生成最终文件名
     */
    private String generateFinalFileName(CurlCommand curlCommand) {
        String serviceIdentifier = extractServiceIdentifier(curlCommand.getSourceFileName());
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"));
        String fileName = serviceIdentifier + "_导出文件_" + timestamp + ".xlsx";
        return Paths.get(config.getDownloadDirectory(), fileName).toString();
    }
    
    /**
     * 生成错误文件名
     */
    private String generateErrorFileName(CurlCommand curlCommand, String contentType) {
        String serviceIdentifier = extractServiceIdentifier(curlCommand.getSourceFileName());
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"));

        // 根据Content-Type确定文件扩展名
        String extension = ".txt";
        if (contentType != null) {
            String lowerContentType = contentType.toLowerCase();
            if (lowerContentType.contains("text/html")) {
                extension = ".html";
            } else if (lowerContentType.contains("application/json")) {
                extension = ".json";
            } else if (lowerContentType.contains("text/xml") || lowerContentType.contains("application/xml")) {
                extension = ".xml";
            }
        }

        String fileName = serviceIdentifier + "_错误响应_" + timestamp + extension;
        return Paths.get(config.getDownloadDirectory(), fileName).toString();
    }
    
    /**
     * 从文件名提取服务标识
     */
    private String extractServiceIdentifier(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return "unknown-service";
        }
        
        // 移除常见后缀
        String identifier = fileName.replaceAll("[-_]?(curl|export)$", "");
        return identifier.isEmpty() ? fileName : identifier;
    }
    
    /**
     * 复制文件
     */
    private void copyFile(String sourcePath, String targetPath) throws IOException {
        try (java.io.FileInputStream fis = new java.io.FileInputStream(sourcePath);
             java.io.FileOutputStream fos = new java.io.FileOutputStream(targetPath)) {
            
            byte[] buffer = new byte[8192];
            int bytesRead;
            while ((bytesRead = fis.read(buffer)) != -1) {
                fos.write(buffer, 0, bytesRead);
            }
        }
    }
}
