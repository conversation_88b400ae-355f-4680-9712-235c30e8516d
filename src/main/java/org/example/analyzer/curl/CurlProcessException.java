package org.example.analyzer.curl;

/**
 * cURL处理过程中的自定义异常
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
public class CurlProcessException extends Exception {
    
    /**
     * 错误类型枚举
     */
    public enum ErrorType {
        CURL_PARSE_ERROR("cURL命令解析错误"),
        HTTP_REQUEST_ERROR("HTTP请求执行错误"),
        FILE_IO_ERROR("文件IO操作错误"),
        NETWORK_ERROR("网络连接错误"),
        DOWNLOAD_ERROR("文件下载错误"),
        INVALID_RESPONSE_ERROR("无效响应错误"),
        FILE_NOT_FOUND_ERROR("文件未找到错误"),
        DIRECTORY_CREATE_ERROR("目录创建错误");
        
        private final String description;
        
        ErrorType(String description) {
            this.description = description;
        }
        
        public String getDescription() {
            return description;
        }
    }
    
    private final ErrorType errorType;
    private final String sourceFile;
    
    public CurlProcessException(ErrorType errorType, String message) {
        super(message);
        this.errorType = errorType;
        this.sourceFile = null;
    }
    
    public CurlProcessException(ErrorType errorType, String message, String sourceFile) {
        super(message);
        this.errorType = errorType;
        this.sourceFile = sourceFile;
    }
    
    public CurlProcessException(ErrorType errorType, String message, Throwable cause) {
        super(message, cause);
        this.errorType = errorType;
        this.sourceFile = null;
    }
    
    public CurlProcessException(ErrorType errorType, String message, String sourceFile, Throwable cause) {
        super(message, cause);
        this.errorType = errorType;
        this.sourceFile = sourceFile;
    }
    
    public ErrorType getErrorType() {
        return errorType;
    }
    
    public String getSourceFile() {
        return sourceFile;
    }
    
    /**
     * 获取详细的错误信息
     */
    public String getDetailedMessage() {
        StringBuilder sb = new StringBuilder();
        sb.append("[").append(errorType.getDescription()).append("] ");
        sb.append(getMessage());
        
        if (sourceFile != null) {
            sb.append(" (来源文件: ").append(sourceFile).append(")");
        }
        
        if (getCause() != null) {
            sb.append(" - 原因: ").append(getCause().getMessage());
        }
        
        return sb.toString();
    }
    
    @Override
    public String toString() {
        return "CurlProcessException{" +
                "errorType=" + errorType +
                ", sourceFile='" + sourceFile + '\'' +
                ", message='" + getMessage() + '\'' +
                '}';
    }
}
