package org.example.analyzer.curl;

import okhttp3.Response;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

/**
 * cURL文件处理器 - 主入口类
 * 负责协调整个cURL文件处理流程
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
public class CurlFileProcessor {
    
    private final DownloadConfig config;
    private final CurlParser parser;
    private final HttpRequestExecutor httpExecutor;
    private final FileDownloadManager downloadManager;
    
    /**
     * 处理结果记录
     */
    public static class ProcessResult {
        private final String sourceFile;
        private final boolean success;
        private final String message;
        private final String downloadedFilePath;
        private final long processingTimeMs;
        
        public ProcessResult(String sourceFile, boolean success, String message, 
                           String downloadedFilePath, long processingTimeMs) {
            this.sourceFile = sourceFile;
            this.success = success;
            this.message = message;
            this.downloadedFilePath = downloadedFilePath;
            this.processingTimeMs = processingTimeMs;
        }
        
        // Getter方法
        public String getSourceFile() { return sourceFile; }
        public boolean isSuccess() { return success; }
        public String getMessage() { return message; }
        public String getDownloadedFilePath() { return downloadedFilePath; }
        public long getProcessingTimeMs() { return processingTimeMs; }
    }
    
    public CurlFileProcessor(DownloadConfig config) {
        this.config = config;
        this.parser = new CurlParser();
        this.httpExecutor = new HttpRequestExecutor(config);
        this.downloadManager = new FileDownloadManager(config);
    }
    
    /**
     * 处理指定目录下的所有cURL文件
     * 
     * @param curlDirectory cURL文件目录路径
     * @throws CurlProcessException 处理失败时抛出异常
     */
    public void processDirectory(String curlDirectory) throws CurlProcessException {
        System.out.println("=== cURL文件批量处理开始 ===");
        System.out.println("处理目录: " + curlDirectory);
        System.out.println("下载目录: " + config.getDownloadDirectory());
        System.out.println("开始时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        System.out.println();
        
        // 获取所有cURL文件
        List<File> curlFiles = getCurlFiles(curlDirectory);
        if (curlFiles.isEmpty()) {
            throw new CurlProcessException(
                CurlProcessException.ErrorType.FILE_NOT_FOUND_ERROR,
                "在目录中未找到任何.txt文件: " + curlDirectory
            );
        }
        
        System.out.println("找到 " + curlFiles.size() + " 个cURL文件");
        System.out.println();
        
        // 处理所有文件
        processCurlFiles(curlFiles);
    }
    
    /**
     * 处理cURL文件列表
     */
    public void processCurlFiles(List<File> curlFiles) throws CurlProcessException {
        List<ProcessResult> results = new ArrayList<>();
        
        for (int i = 0; i < curlFiles.size(); i++) {
            File curlFile = curlFiles.get(i);
            
            System.out.println("处理进度: [" + (i + 1) + "/" + curlFiles.size() + "] " + curlFile.getName());
            
            try {
                ProcessResult result = processSingleFile(curlFile);
                results.add(result);
                
                if (result.isSuccess()) {
                    System.out.println("✓ 处理成功: " + result.getDownloadedFilePath());
                } else {
                    System.out.println("✗ 处理失败: " + result.getMessage());
                    // 遇到错误立即停止
                    throw new CurlProcessException(
                        CurlProcessException.ErrorType.HTTP_REQUEST_ERROR,
                        "处理文件失败，停止后续处理: " + result.getMessage(),
                        curlFile.getName()
                    );
                }
                
            } catch (CurlProcessException e) {
                // 记录失败结果
                ProcessResult failResult = new ProcessResult(
                    curlFile.getName(), 
                    false, 
                    e.getDetailedMessage(), 
                    null, 
                    0
                );
                results.add(failResult);
                
                // 生成处理报告
                generateProcessReport(results);
                
                // 重新抛出异常以停止处理
                throw e;
            }
            
            System.out.println();
        }
        
        // 生成最终处理报告
        generateProcessReport(results);
    }
    
    /**
     * 处理单个cURL文件
     */
    public ProcessResult processSingleFile(File curlFile) throws CurlProcessException {
        long startTime = System.currentTimeMillis();
        String fileName = curlFile.getName();
        
        try {
            // 1. 读取cURL文件内容
            String curlCommand = readCurlFile(curlFile);
            
            // 2. 解析cURL命令
            String fileNameWithoutExt = removeFileExtension(fileName);
            CurlCommand command = parser.parse(curlCommand, fileNameWithoutExt);
            command.setSourceFilePath(curlFile.getAbsolutePath());
            
            if (config.isEnableVerboseLogging()) {
                System.out.println("解析结果: " + command);
            }
            
            // 3. 执行HTTP请求 - 按照SimpleCurlExecutor的逻辑
            Response response = httpExecutor.executeRequest(command);

            // 4. 保存文件 - 按照SimpleCurlExecutor的逻辑
            String downloadedFilePath = saveFileDirectly(response, command);
            
            long processingTime = System.currentTimeMillis() - startTime;
            
            return new ProcessResult(
                fileName,
                true,
                "处理成功",
                downloadedFilePath,
                processingTime
            );
            
        } catch (CurlProcessException e) {
            long processingTime = System.currentTimeMillis() - startTime;
            return new ProcessResult(
                fileName,
                false,
                e.getDetailedMessage(),
                null,
                processingTime
            );
        }
    }
    
    /**
     * 读取cURL文件内容
     */
    private String readCurlFile(File file) throws CurlProcessException {
        try {
            return new String(Files.readAllBytes(file.toPath()), "UTF-8").trim();
        } catch (IOException e) {
            throw new CurlProcessException(
                CurlProcessException.ErrorType.FILE_IO_ERROR,
                "读取cURL文件失败: " + e.getMessage(),
                file.getName(),
                e
            );
        }
    }
    
    /**
     * 获取目录下的所有cURL文件（.txt文件）
     */
    private List<File> getCurlFiles(String directoryPath) throws CurlProcessException {
        File directory = new File(directoryPath);
        
        if (!directory.exists()) {
            throw new CurlProcessException(
                CurlProcessException.ErrorType.FILE_NOT_FOUND_ERROR,
                "目录不存在: " + directoryPath
            );
        }
        
        if (!directory.isDirectory()) {
            throw new CurlProcessException(
                CurlProcessException.ErrorType.FILE_NOT_FOUND_ERROR,
                "路径不是目录: " + directoryPath
            );
        }
        
        File[] files = directory.listFiles((dir, name) -> 
            name.toLowerCase().endsWith(".txt"));
        
        List<File> curlFiles = new ArrayList<>();
        if (files != null) {
            for (File file : files) {
                curlFiles.add(file);
            }
        }
        
        return curlFiles;
    }
    
    /**
     * 移除文件扩展名
     */
    private String removeFileExtension(String fileName) {
        if (fileName == null || fileName.isEmpty()) {
            return fileName;
        }
        
        int lastDotIndex = fileName.lastIndexOf('.');
        if (lastDotIndex > 0) {
            return fileName.substring(0, lastDotIndex);
        }
        
        return fileName;
    }
    
    /**
     * 生成处理报告
     */
    public void generateProcessReport(List<ProcessResult> results) {
        System.out.println("=== 处理报告 ===");

        int successCount = 0;
        int failCount = 0;
        long totalProcessingTime = 0;

        for (ProcessResult result : results) {
            if (result.isSuccess()) {
                successCount++;
            } else {
                failCount++;
            }
            totalProcessingTime += result.getProcessingTimeMs();
        }

        System.out.println("总文件数: " + results.size());
        System.out.println("成功数量: " + successCount);
        System.out.println("失败数量: " + failCount);
        System.out.println("总处理时间: " + formatTime(totalProcessingTime));
        System.out.println("完成时间: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));

        if (failCount > 0) {
            System.out.println("\n失败详情:");
            for (ProcessResult result : results) {
                if (!result.isSuccess()) {
                    System.out.println("- " + result.getSourceFile() + ": " + result.getMessage());
                }
            }
        }

        if (successCount > 0) {
            System.out.println("\n成功下载的文件:");
            for (ProcessResult result : results) {
                if (result.isSuccess()) {
                    System.out.println("- " + result.getDownloadedFilePath());
                }
            }
        }

        System.out.println("=== 报告结束 ===");
    }

    /**
     * 格式化时间
     */
    private String formatTime(long milliseconds) {
        if (milliseconds < 1000) {
            return milliseconds + "ms";
        } else if (milliseconds < 60000) {
            return String.format("%.2fs", milliseconds / 1000.0);
        } else {
            long minutes = milliseconds / 60000;
            long seconds = (milliseconds % 60000) / 1000;
            return minutes + "m " + seconds + "s";
        }
    }

    /**
     * 直接保存文件 - 完全按照SimpleCurlExecutor的逻辑
     */
    private String saveFileDirectly(Response response, CurlCommand command) throws CurlProcessException {
        try {
            // 确保目录存在
            new File(config.getDownloadDirectory()).mkdirs();

            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"));
            // 清理源文件名，移除-export后缀，避免重复
            String cleanSourceName = command.getSourceFileName().replaceAll("[-_]?export$", "");
            String fileName = cleanSourceName + "_" + timestamp + ".xlsx";
            String filePath = Paths.get(config.getDownloadDirectory(), fileName).toString();

            try (InputStream inputStream = response.body().byteStream();
                 FileOutputStream outputStream = new FileOutputStream(filePath)) {

                byte[] buffer = new byte[8192];
                int bytesRead;
                long totalBytes = 0;

                while ((bytesRead = inputStream.read(buffer)) != -1) {
                    outputStream.write(buffer, 0, bytesRead);
                    totalBytes += bytesRead;
                }

                if (config.isEnableVerboseLogging()) {
                    System.out.println("✓ 文件下载成功!");
                    System.out.println("文件路径: " + filePath);
                    System.out.println("文件大小: " + totalBytes + " 字节");
                }

                return filePath;
            }

        } catch (IOException e) {
            throw new CurlProcessException(
                CurlProcessException.ErrorType.FILE_IO_ERROR,
                "文件保存失败: " + e.getMessage(),
                command.getSourceFileName(),
                e
            );
        } finally {
            response.close();
        }
    }

    /**
     * 关闭资源
     */
    public void close() {
        if (httpExecutor != null) {
            httpExecutor.close();
        }
    }
}
