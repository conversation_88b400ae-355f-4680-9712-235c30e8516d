package org.example.analyzer.curl;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * cURL命令解析器
 * 负责解析cURL命令字符串，提取URL、方法、请求头、请求体等信息
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
public class CurlParser {
    
    // 正则表达式模式
    private static final Pattern URL_PATTERN = Pattern.compile("curl\\s+['\"]?([^'\"\\s]+)['\"]?");
    private static final Pattern METHOD_PATTERN = Pattern.compile("-X\\s+([A-Z]+)");
    private static final Pattern HEADER_PATTERN = Pattern.compile("-H\\s+['\"]([^'\"]+)['\"]");
    private static final Pattern COOKIE_PATTERN = Pattern.compile("-b\\s+['\"]([^'\"]+)['\"]");
    // 暂时禁用正则表达式，强制使用手动解析
    private static final Pattern DATA_PATTERN = null;
    
    /**
     * 解析cURL命令
     * 
     * @param curlCommand 原始cURL命令字符串
     * @param sourceFileName 来源文件名
     * @return 解析后的CurlCommand对象
     * @throws CurlProcessException 解析失败时抛出异常
     */
    public CurlCommand parse(String curlCommand, String sourceFileName) throws CurlProcessException {
        if (curlCommand == null || curlCommand.trim().isEmpty()) {
            throw new CurlProcessException(
                CurlProcessException.ErrorType.CURL_PARSE_ERROR,
                "cURL命令为空",
                sourceFileName
            );
        }
        
        // 清理命令字符串（移除多余的空白字符和换行符）
        String cleanCommand = cleanCurlCommand(curlCommand);
        
        CurlCommand command = new CurlCommand(cleanCommand, sourceFileName);
        
        try {
            // 解析URL
            String url = extractUrl(cleanCommand);
            if (url == null || url.isEmpty()) {
                throw new CurlProcessException(
                    CurlProcessException.ErrorType.CURL_PARSE_ERROR,
                    "无法从cURL命令中提取URL",
                    sourceFileName
                );
            }
            command.setUrl(url);
            
            // 解析HTTP方法
            String method = extractMethod(cleanCommand);
            command.setMethod(method);
            
            // 解析请求头
            extractHeaders(cleanCommand, command);

            // 解析Cookie并添加到请求头
            extractCookies(cleanCommand, command);

            // 解析请求体
            String body = extractBody(cleanCommand);
            command.setBody(body);
            
            return command;
            
        } catch (Exception e) {
            throw new CurlProcessException(
                CurlProcessException.ErrorType.CURL_PARSE_ERROR,
                "解析cURL命令时发生错误: " + e.getMessage(),
                sourceFileName,
                e
            );
        }
    }
    
    /**
     * 清理cURL命令字符串
     */
    private String cleanCurlCommand(String curlCommand) {
        return curlCommand
                .replaceAll("\\\\\\s*\\n\\s*", " ")  // 移除行继续符和换行
                .replaceAll("\\s+", " ")             // 合并多个空格
                .trim();
    }
    
    /**
     * 提取URL
     */
    private String extractUrl(String curlCommand) {
        Matcher matcher = URL_PATTERN.matcher(curlCommand);
        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
    
    /**
     * 提取HTTP方法
     */
    private String extractMethod(String curlCommand) {
        Matcher matcher = METHOD_PATTERN.matcher(curlCommand);
        if (matcher.find()) {
            return matcher.group(1);
        }
        
        // 如果没有明确指定方法，根据是否有数据判断
        if (curlCommand.contains("--data") || curlCommand.contains("-d ")) {
            return "POST";
        }
        
        return "GET";
    }
    
    /**
     * 提取请求头
     */
    private void extractHeaders(String curlCommand, CurlCommand command) {
        Matcher matcher = HEADER_PATTERN.matcher(curlCommand);
        while (matcher.find()) {
            String headerLine = matcher.group(1);
            String[] parts = headerLine.split(":", 2);
            if (parts.length == 2) {
                String name = parts[0].trim();
                String value = parts[1].trim();
                command.addHeader(name, value);
            }
        }
    }

    /**
     * 提取Cookie并添加到请求头
     */
    private void extractCookies(String curlCommand, CurlCommand command) {
        Matcher matcher = COOKIE_PATTERN.matcher(curlCommand);
        if (matcher.find()) {
            String cookieValue = matcher.group(1);
            command.addHeader("Cookie", cookieValue);
        }
    }
    
    /**
     * 提取请求体 - 直接使用手动解析
     */
    private String extractBody(String curlCommand) {
        // 直接使用手动解析，不依赖正则表达式
        return extractBodyManually(curlCommand);
    }

    /**
     * 手动提取请求体（处理复杂的JSON字符串）
     * 使用最简单可靠的方法：找到--data-raw '之后的内容，直到命令结尾的单引号
     */
    private String extractBodyManually(String curlCommand) {
        // 查找 --data-raw ' 的位置
        String dataPrefix = "--data-raw '";
        int dataStart = curlCommand.indexOf(dataPrefix);
        if (dataStart == -1) {
            dataPrefix = "--data '";
            dataStart = curlCommand.indexOf(dataPrefix);
            if (dataStart == -1) {
                return null;
            }
        }

        dataStart += dataPrefix.length();

        // 从后往前找最后一个单引号，这应该是JSON的结束位置
        // 因为cURL命令的结构是: --data-raw 'JSON内容'
        int dataEnd = curlCommand.lastIndexOf('\'');

        // 确保找到的结束引号在数据开始位置之后，且不是开始的引号
        if (dataEnd > dataStart) {
            String result = curlCommand.substring(dataStart, dataEnd);
            // 验证结果是否合理（应该是JSON格式）
            if (result.trim().startsWith("{") && result.trim().endsWith("}")) {
                return result;
            }
        }

        return null;
    }
}
