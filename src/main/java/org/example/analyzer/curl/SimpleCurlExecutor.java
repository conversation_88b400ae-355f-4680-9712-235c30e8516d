package org.example.analyzer.curl;

import okhttp3.*;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 最简易的cURL执行器
 * 直接解析cURL命令并执行下载
 */
public class SimpleCurlExecutor {
    
    public static void main(String[] args) {
        String downloadDir = "D:\\Users\\ex_wuyh42.CN\\Desktop\\生产环境-慢接口\\auto-export";

        try {
            System.out.println("=== 简易cURL执行器 - 使用固定参数测试 ===");

            // 直接使用您提供的参数执行请求
            executeFixedRequest(downloadDir);

        } catch (Exception e) {
            System.err.println("执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
    
    private static void executeFixedRequest(String downloadDir) throws Exception {
        // 创建HTTP客户端
        OkHttpClient client = new OkHttpClient();

        // 创建请求体
        MediaType mediaType = MediaType.parse("application/json");
        RequestBody body = RequestBody.create(mediaType, "{\"storageObject\":{\"objectType\":\"env\",\"objectId\":\"9f9faf3008584df89b283df3ca4f6b0b\",\"monitorItemId\":\"2d7d1ae0f5674353844aa7a18b278c89\",\"pluginName\":\"sys-java-url\",\"envUuid\":\"9f9faf3008584df89b283df3ca4f6b0b\"},\"tableName\":\"sys-java-url.detail\",\"attachment\":{\"envUuid\":\"9f9faf3008584df89b283df3ca4f6b0b\",\"storageDomain\":\"NanHai\"},\"systemUuid\":null,\"sourceSystemUuid\":null,\"filter\":null,\"title\":\"url详情\",\"startTime\":\"-15d\",\"endTime\":\"now\",\"type\":\"table\",\"groupBy\":\"url,method\",\"expressionList\":[{\"expression\":\"SUM(invokeCount)\",\"as\":\"次数\"},{\"expression\":\"SUM(errorCount)\",\"as\":\"错误数\"},{\"expression\":\"SUM(totalTime)/SUM(invokeCount)\",\"as\":\"RT（ms）\"},{\"expression\":\"SUM(totalTime)\",\"as\":\"总时间(ms)\"},{\"expression\":\"MAX(concurrentMax)\",\"as\":\"最大并发\"},{\"expression\":\"MAX(maxTime)\",\"as\":\"最慢（ms）\"},{\"expression\":\"LAST(lastError)\",\"as\":\"最近错误\"},{\"expression\":\"SUM(range1)\",\"as\":\"0-10ms\"},{\"expression\":\"SUM(range2)\",\"as\":\"10-100ms\"},{\"expression\":\"SUM(range3)\",\"as\":\"100-500ms\"},{\"expression\":\"SUM(range4)\",\"as\":\"500-1000ms\"},{\"expression\":\"SUM(range5)\",\"as\":\"1-10s\"},{\"expression\":\"SUM(range6)\",\"as\":\"10s以上\"},{\"expression\":\"MAX(maxResponseSize)\",\"as\":\"最大响应体大小（Bytes）\"},{\"expression\":\"TP99(histogram)\",\"as\":\"TP99\"},{\"expression\":\"TP95(histogram)\",\"as\":\"TP95\"},{\"expression\":\"TP90(histogram)\",\"as\":\"TP90\"},{\"expression\":\"MERGE(source_env)\",\"as\":\"调用源\"},{\"expression\":\"SUM(bizErrorCount)\",\"as\":\"业务异常次数\"}],\"expressionStyles\":[{\"expression\":\"SUM(errorCount)\",\"styles\":[{\"css\":{\"color\":\"red\"},\"filter\":\"${value} > 0\",\"columns\":null}]},{\"expression\":\"SUM(totalTime)/SUM(invokeCount)\",\"styles\":[{\"css\":{\"color\":\"red\"},\"filter\":\"${value} > 2000\",\"columns\":\"$[0],$[4]\"}]}],\"columnToEnvName\":null,\"columnToServiceName\":null,\"columnToSystemName\":null,\"specifiedClickModels\":[{\"columnName\":\"url\",\"type\":\"call_chain_url\"}],\"pageNo\":1,\"pageSize\":10,\"orderBy\":4,\"order\":\"desc\",\"searchFilter\":\"\",\"searchFilterFields\":null,\"checkboxLabel\":\"只展示慢调用\",\"checkboxHaving\":null,\"latest\":false,\"envUuid\":\"9f9faf3008584df89b283df3ca4f6b0b\"}");

        // 构建请求
        Request request = new Request.Builder()
                .url("https://monitor.midea.com/xops/web/monitor/view/v1/table/export")
                .post(body)
                .addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36")
                .addHeader("Accept", "application/json, text/plain, */*")
                .addHeader("Content-Type", "application/json")
                .addHeader("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
                .addHeader("Cache-Control", "no-cache")
                .addHeader("Origin", "https://monitor.midea.com")
                .addHeader("Pragma", "no-cache")
                .addHeader("Referer", "https://monitor.midea.com/monitoring/monitor/view/application?systemId=60155b1d54644d6888982f8ee9f3f847&nodeId=9f9faf3008584df89b283df3ca4f6b0b&timeRange=p_l_15_days&instance=all&monitorItemId=2d7d1ae0f5674353844aa7a18b278c89&viewType=url%25E8%25AF%25A6%25E6%2583%2585")
                .addHeader("Sec-Fetch-Dest", "empty")
                .addHeader("Sec-Fetch-Mode", "cors")
                .addHeader("Sec-Fetch-Site", "same-origin")
                .addHeader("m_monitor_system_list", "60155b1d54644d6888982f8ee9f3f847")
                .addHeader("midea-env-id", "appKey:af6dabf6c2024097984c4fa07cd69dc7")
                .addHeader("midea-gtrace-id", "0f6ecb2e-a321-41c0-a34a-218a18d5edb1")
                .addHeader("sec-ch-ua", "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Google Chrome\";v=\"138\"")
                .addHeader("sec-ch-ua-mobile", "?0")
                .addHeader("sec-ch-ua-platform", "\"Windows\"")
                .addHeader("Cookie", "mip_curr_lang=zh-CN; mip_lang=zh; mas_privacy_uid=ex_wuyh42; oam_locale=zh; MAS_TGC=eyJhbGciOiJIUzUxMiJ9.WlhsS05tRllRV2xQYVVwRlVsVlphVXhEU21oaVIyTnBUMmxLYTJGWVNXbE1RMHBzWW0xTmFVOXBTa0pOVkVrMFVUQktSRXhWYUZSTmFsVXlTVzR3TGk1dk0wRnVYMkpVVDIxVlZsSmZYMnR0TTB4SGJqaG5MbTFwWmtNeGFtWldia1J5VjB0Q2RsVTVObXhuTkVFNWRqRmZjMTk2U1ZOZlRVOWFjWGhmU1dOQ2RVNUpSV3hIYmpsMlF6TnBhRGRWVVdoSGNHbG9NRzVRVFdoclIwc3RSRnBaUjBwMGFFb3hXSGQ2TUZWRlIyd3djblJNVldoWWNXTlFaMWRyYlZGbVgwcG5RMVI2UjBFMWRGRnFjMVpTYjFodGRITm9kV0pHWWxCUlRVdEpXakZHVTI1aVZGOUNUREV6VEVSclVHYzNhMHRDZVVZeGNEVnlNbmh3ZW5waE9Xb3lkeTV4WmpZeVRWTmhlbVl5Ym5SNmQzQkpXVTF1T1VkQg.-UC-Pb_8sfFP2jdM8hKBqfTUmJlc6ycFXlKd5OEbO28zZWNs-t-67qHq-0EcHSAH_jQbfsTQZcbE2TvRzzhJtA; midea_sso_token=SRwXaRCgVFGCaUwdVDbDIYJoEf2tmWB44lZ%2Ff2ub3B9RliCseYK8Kx%2BRC1ihJ2T7; bVisitorId=8340835f6a7a0ddd36ee71ac611ae448; MAS_TGC_UAT=eyJhbGciOiJIUzUxMiJ9.WlhsS05tRllRV2xQYVVwRlVsVlphVXhEU21oaVIyTnBUMmxLYTJGWVNXbE1RMHBzWW0xTmFVOXBTa0pOVkVrMFVUQktSRXhWYUZSTmFsVXlTVzR3TGk1d1gxRmlkVVpOV2pKb2FHRjZaalpoVVdzMVIzUjNMamxrWmtFdFlsQm5OR0pWWkVOMVNFbFVTWGx2V0hWTmMwOVRObGh4VFVScFFuVlhYMjlUWWxrNWF6VXRlREp3Vm05c1MzbDBTV1pYWTBKSlpFWnBaaTFNWmpseFFrTndUamMxZDJSVFUzQmtaM2RKWnpGNU4wNVBaVmgwV0hjNE1sTnFZbGsxVkZWNE5tRnZTa3gwYUY5b1FrWm1NVm8zT1dWVVRYRnlSMlE0V0VJMWFGZGlkbEJrVm5GQlZqRjFTbVIwWlhWYVVTNHROa051Y0dSMVdHcFNjbUZ3UjJSeVZVYzFkVkpC.gfsay1YPymuijiTQpn1XAFG9Y2BfLeMuTUdQZotmsN4EqG9dqwY-OrJiYE_5bBYoo8I740KTYSN9p70BxiFpGQ; mideatest_tgc=7POXO0ktegqEEhsxmr2jMtwc3Hs2D%2BfBrHDDiXZ3Abx747W7iNowmHofEXCDJ%2BCo; apmUser=ex_wuyh42; bVisitorId_UAT=7d25b5d233c1e2d74d110f9dcfbcf7bb; gw_region=sd; MOD_AUTH_CAS=66u1f5G1VbHfqEZ9UrXelYLuhQ11Iesy")
                .build();

        System.out.println("发送HTTP请求到: " + request.url());
        System.out.println("请求方法: " + request.method());
        System.out.println("请求头数量: " + request.headers().size());

        // 执行请求
        try (Response response = client.newCall(request).execute()) {
            System.out.println("响应状态码: " + response.code());
            System.out.println("响应消息: " + response.message());
            System.out.println("Content-Type: " + response.header("Content-Type"));
            System.out.println("Content-Length: " + response.header("Content-Length"));

            if (response.isSuccessful()) {
                // 保存文件
                saveFile(response, downloadDir);
            } else {
                // 保存错误响应
                saveErrorResponse(response, downloadDir);
            }
        }
    }
    

    
    private static void saveFile(Response response, String downloadDir) throws IOException {
        new File(downloadDir).mkdirs();
        
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"));
        String fileName = "basedata_export_" + timestamp + ".xlsx";
        String filePath = Paths.get(downloadDir, fileName).toString();
        
        try (InputStream inputStream = response.body().byteStream();
             FileOutputStream outputStream = new FileOutputStream(filePath)) {
            
            byte[] buffer = new byte[8192];
            int bytesRead;
            long totalBytes = 0;
            
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
                totalBytes += bytesRead;
            }
            
            System.out.println("✓ 文件下载成功!");
            System.out.println("文件路径: " + filePath);
            System.out.println("文件大小: " + totalBytes + " 字节");
        }
    }
    
    private static void saveErrorResponse(Response response, String downloadDir) throws IOException {
        new File(downloadDir).mkdirs();
        
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd_HH-mm-ss"));
        String fileName = "error_response_" + timestamp + ".html";
        String filePath = Paths.get(downloadDir, fileName).toString();
        
        try (FileWriter writer = new FileWriter(filePath)) {
            String responseBody = response.body().string();
            writer.write(responseBody);
            
            System.out.println("✗ 请求失败，错误响应已保存");
            System.out.println("错误文件: " + filePath);
            System.out.println("响应内容前200字符: " + responseBody.substring(0, Math.min(200, responseBody.length())));
        }
    }
}
