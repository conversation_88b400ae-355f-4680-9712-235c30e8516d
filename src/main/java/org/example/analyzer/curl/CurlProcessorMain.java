package org.example.analyzer.curl;

/**
 * cURL文件处理器主程序
 * 用于演示如何使用CurlFileProcessor处理监控平台的接口调用日志导出
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
public class CurlProcessorMain {
    
    public static void main(String[] args) {
        // 配置参数
        String curlDirectory = "D:\\Users\\ex_wuyh42.CN\\Desktop\\生产环境-慢接口\\monitor-export-curl";  // 修改为您的cURL文件目录
        String downloadDirectory = "D:\\Users\\ex_wuyh42.CN\\Desktop\\生产环境-慢接口\\auto-export";
        
        // 如果通过命令行参数指定目录
        if (args.length > 0) {
            curlDirectory = args[0];
        }
        if (args.length > 1) {
            downloadDirectory = args[1];
        }
        
        // 创建配置
        DownloadConfig config = new DownloadConfig(curlDirectory, downloadDirectory);
        config.setEnableVerboseLogging(true);  // 启用详细日志
        
        // 创建处理器
        CurlFileProcessor processor = new CurlFileProcessor(config);
        
        try {
            System.out.println("监控平台接口调用日志导出工具");
            System.out.println("================================");
            System.out.println("配置信息:");
            System.out.println(config);
            System.out.println();
            
            // 处理目录下的所有cURL文件
            processor.processDirectory(curlDirectory);
            
            System.out.println("\n所有文件处理完成！");
            
        } catch (CurlProcessException e) {
            System.err.println("\n处理过程中发生错误:");
            System.err.println(e.getDetailedMessage());
            System.err.println("\n程序已停止执行。");
            
            // 打印堆栈跟踪以便调试
            if (config.isEnableVerboseLogging()) {
                System.err.println("\n详细错误信息:");
                e.printStackTrace();
            }
            
            System.exit(1);
            
        } catch (Exception e) {
            System.err.println("\n发生未知错误:");
            System.err.println(e.getMessage());
            e.printStackTrace();
            System.exit(1);
            
        } finally {
            // 确保资源被正确关闭
            processor.close();
        }
    }
    
    /**
     * 打印使用说明
     */
    private static void printUsage() {
        System.out.println("使用方法:");
        System.out.println("java -cp target/classes org.example.analyzer.curl.CurlProcessorMain [cURL文件目录] [下载目录]");
        System.out.println();
        System.out.println("参数说明:");
        System.out.println("  cURL文件目录: 包含.txt文件的目录路径（可选，默认: D:\\curl-files）");
        System.out.println("  下载目录:     Excel文件保存目录（可选，默认: D:\\Users\\ex_wuyh42.CN\\Desktop\\生产环境-慢接口\\auto-export）");
        System.out.println();
        System.out.println("示例:");
        System.out.println("  java -cp target/classes org.example.analyzer.curl.CurlProcessorMain");
        System.out.println("  java -cp target/classes org.example.analyzer.curl.CurlProcessorMain D:\\my-curl-files");
        System.out.println("  java -cp target/classes org.example.analyzer.curl.CurlProcessorMain D:\\my-curl-files D:\\downloads");
    }
}
