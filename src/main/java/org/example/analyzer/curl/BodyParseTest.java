package org.example.analyzer.curl;

/**
 * 请求体解析测试
 */
public class BodyParseTest {
    
    public static void main(String[] args) {
        String testCurl = "--data-raw '{\"test\":\"value\",\"nested\":{\"key\":\"data\"}}'";
        
        System.out.println("测试cURL片段: " + testCurl);
        
        // 测试手动解析
        String result = extractBodyManually(testCurl);
        System.out.println("解析结果: " + result);
        System.out.println("结果长度: " + (result != null ? result.length() : "null"));
    }
    
    private static String extractBodyManually(String curlCommand) {
        // 查找 --data-raw ' 的位置
        int dataStart = curlCommand.indexOf("--data-raw '");
        if (dataStart == -1) {
            dataStart = curlCommand.indexOf("--data '");
            if (dataStart == -1) {
                return null;
            }
            dataStart += "--data '".length();
        } else {
            dataStart += "--data-raw '".length();
        }
        
        System.out.println("数据开始位置: " + dataStart);
        System.out.println("从该位置开始的内容: " + curlCommand.substring(dataStart, Math.min(dataStart + 50, curlCommand.length())));
        
        // 从dataStart开始，找到匹配的结束引号
        int dataEnd = -1;
        boolean inEscape = false;
        
        for (int i = dataStart; i < curlCommand.length(); i++) {
            char c = curlCommand.charAt(i);
            
            if (inEscape) {
                inEscape = false;
                continue;
            }
            
            if (c == '\\') {
                inEscape = true;
                continue;
            }
            
            if (c == '\'') {
                // 检查是否是结束引号（后面应该跟空格或命令结束）
                if (i + 1 >= curlCommand.length() || 
                    curlCommand.charAt(i + 1) == ' ' || 
                    curlCommand.charAt(i + 1) == '\n' ||
                    curlCommand.charAt(i + 1) == '\r') {
                    dataEnd = i;
                    System.out.println("找到结束位置: " + dataEnd);
                    break;
                }
            }
        }
        
        if (dataEnd > dataStart) {
            return curlCommand.substring(dataStart, dataEnd);
        }
        
        return null;
    }
}
