package org.example.analyzer.curl;

import okhttp3.*;
import java.io.IOException;
import java.util.Map;
import java.util.concurrent.TimeUnit;

/**
 * HTTP请求执行器
 * 负责执行HTTP请求并返回响应
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
public class HttpRequestExecutor {
    
    private final OkHttpClient client;
    private final DownloadConfig config;
    
    public HttpRequestExecutor(DownloadConfig config) {
        this.config = config;
        this.client = createHttpClient();
    }
    
    /**
     * 创建HTTP客户端 - 与SimpleCurlExecutor保持完全一致
     */
    private OkHttpClient createHttpClient() {
        return new OkHttpClient();
    }
    
    /**
     * 执行HTTP请求并返回分析结果
     *
     * @param curlCommand cURL命令对象
     * @return 响应分析结果
     * @throws CurlProcessException 请求执行失败时抛出异常
     */
    public ResponseAnalyzer.AnalysisResult executeRequestAndAnalyze(CurlCommand curlCommand) throws CurlProcessException {
        Response response = executeRequest(curlCommand);

        // 使用响应分析器分析结果
        ResponseAnalyzer analyzer = new ResponseAnalyzer(config);
        return analyzer.analyzeResponse(response, curlCommand);
    }

    /**
     * 执行HTTP请求 - 完全按照SimpleCurlExecutor的逻辑
     *
     * @param curlCommand cURL命令对象
     * @return HTTP响应
     * @throws CurlProcessException 请求执行失败时抛出异常
     */
    public Response executeRequest(CurlCommand curlCommand) throws CurlProcessException {
        if (!curlCommand.isValid()) {
            throw new CurlProcessException(
                CurlProcessException.ErrorType.HTTP_REQUEST_ERROR,
                "无效的cURL命令",
                curlCommand.getSourceFileName()
            );
        }

        try {
            // 完全按照SimpleCurlExecutor的方式构建请求
            Request.Builder requestBuilder = new Request.Builder().url(curlCommand.getUrl());

            // 添加所有请求头 - 与SimpleCurlExecutor完全一致
            for (Map.Entry<String, String> header : curlCommand.getHeaders().entrySet()) {
                requestBuilder.addHeader(header.getKey(), header.getValue());
                if (config.isEnableVerboseLogging()) {
                    String name = header.getKey();
                    String value = header.getValue();
                    System.out.println("添加请求头: " + name + " = " + (name.toLowerCase().contains("cookie") ? "[HIDDEN]" : value));
                }
            }

            // 添加请求体 - 与SimpleCurlExecutor完全一致
            String body = curlCommand.getBody();
            if (body != null && !body.isEmpty()) {
                if (config.isEnableVerboseLogging()) {
                    System.out.println("请求体长度: " + body.length() + " 字符");
                    System.out.println("请求体前100字符: " + body.substring(0, Math.min(100, body.length())));
                }

                MediaType mediaType = MediaType.parse("application/json");
                RequestBody requestBody = RequestBody.create(mediaType, body);
                requestBuilder.post(requestBody);
            } else {
                if (config.isEnableVerboseLogging()) {
                    System.out.println("无请求体");
                }
                requestBuilder.get();
            }

            Request request = requestBuilder.build();

            if (config.isEnableVerboseLogging()) {
                System.out.println("发送HTTP请求到: " + request.url());
                System.out.println("请求方法: " + request.method());
                System.out.println("请求头数量: " + request.headers().size());
            }

            // 执行请求
            Response response = client.newCall(request).execute();

            if (config.isEnableVerboseLogging()) {
                System.out.println("响应状态码: " + response.code());
                System.out.println("响应消息: " + response.message());
                System.out.println("Content-Type: " + response.header("Content-Type"));
                System.out.println("Content-Length: " + response.header("Content-Length"));
            }

            if (!response.isSuccessful()) {
                throw new CurlProcessException(
                    CurlProcessException.ErrorType.HTTP_REQUEST_ERROR,
                    "HTTP请求失败，状态码: " + response.code() + ", 消息: " + response.message(),
                    curlCommand.getSourceFileName()
                );
            }

            return response;

        } catch (IOException e) {
            throw new CurlProcessException(
                CurlProcessException.ErrorType.NETWORK_ERROR,
                "网络请求失败: " + e.getMessage(),
                curlCommand.getSourceFileName(),
                e
            );
        } catch (Exception e) {
            throw new CurlProcessException(
                CurlProcessException.ErrorType.HTTP_REQUEST_ERROR,
                "执行HTTP请求时发生未知错误: " + e.getMessage(),
                curlCommand.getSourceFileName(),
                e
            );
        }
    }
    

    
    /**
     * 读取响应内容（用于错误分析）
     */
    private String readResponseContent(Response response) {
        try {
            ResponseBody body = response.body();
            if (body == null) {
                return "[响应体为空]";
            }

            // 读取所有内容
            String content = body.string();
            if (content.isEmpty()) {
                return "[响应体为空]";
            }

            return content;

        } catch (Exception e) {
            return "[无法读取响应内容: " + e.getMessage() + "]";
        }
    }

    /**
     * 检查是否为Excel文件的Content-Type
     */
    private boolean isExcelContentType(String contentType) {
        if (contentType == null || contentType.isEmpty()) {
            return false;
        }

        String lowerContentType = contentType.toLowerCase();
        return lowerContentType.contains("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet") ||
               lowerContentType.contains("application/vnd.ms-excel") ||
               lowerContentType.contains("application/octet-stream") ||
               lowerContentType.contains("application/force-download") ||
               lowerContentType.contains("application/download");
    }
    
    /**
     * 关闭HTTP客户端
     */
    public void close() {
        if (client != null) {
            client.dispatcher().executorService().shutdown();
            client.connectionPool().evictAll();
        }
    }
}
