package org.example.analyzer.curl;

import java.util.HashMap;
import java.util.Map;

/**
 * cURL命令数据模型
 * 用于存储解析后的cURL命令信息
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
public class CurlCommand {
    
    /**
     * 请求URL
     */
    private String url;
    
    /**
     * HTTP方法（GET, POST等）
     */
    private String method = "GET";
    
    /**
     * 请求头信息
     */
    private Map<String, String> headers = new HashMap<>();
    
    /**
     * 请求体内容
     */
    private String body;
    
    /**
     * 原始cURL命令
     */
    private String originalCommand;
    
    /**
     * 来源文件名（不含扩展名）
     */
    private String sourceFileName;
    
    /**
     * 来源文件完整路径
     */
    private String sourceFilePath;
    
    // 构造函数
    public CurlCommand() {}
    
    public CurlCommand(String originalCommand, String sourceFileName) {
        this.originalCommand = originalCommand;
        this.sourceFileName = sourceFileName;
    }
    
    // Getter和Setter方法
    public String getUrl() {
        return url;
    }
    
    public void setUrl(String url) {
        this.url = url;
    }
    
    public String getMethod() {
        return method;
    }
    
    public void setMethod(String method) {
        this.method = method;
    }
    
    public Map<String, String> getHeaders() {
        return headers;
    }
    
    public void setHeaders(Map<String, String> headers) {
        this.headers = headers;
    }
    
    public void addHeader(String name, String value) {
        this.headers.put(name, value);
    }
    
    public String getBody() {
        return body;
    }
    
    public void setBody(String body) {
        this.body = body;
    }
    
    public String getOriginalCommand() {
        return originalCommand;
    }
    
    public void setOriginalCommand(String originalCommand) {
        this.originalCommand = originalCommand;
    }
    
    public String getSourceFileName() {
        return sourceFileName;
    }
    
    public void setSourceFileName(String sourceFileName) {
        this.sourceFileName = sourceFileName;
    }
    
    public String getSourceFilePath() {
        return sourceFilePath;
    }
    
    public void setSourceFilePath(String sourceFilePath) {
        this.sourceFilePath = sourceFilePath;
    }
    
    /**
     * 检查是否为有效的cURL命令
     */
    public boolean isValid() {
        return url != null && !url.trim().isEmpty();
    }
    
    @Override
    public String toString() {
        return "CurlCommand{" +
                "url='" + url + '\'' +
                ", method='" + method + '\'' +
                ", headers=" + headers.size() + " items" +
                ", hasBody=" + (body != null && !body.isEmpty()) +
                ", sourceFileName='" + sourceFileName + '\'' +
                '}';
    }
}
