package org.example.analyzer.curl;

/**
 * cURL文件下载配置类
 * 
 * <AUTHOR>
 * @since 2025-07-25
 */
public class DownloadConfig {
    
    /**
     * cURL文件存放目录（包含.txt文件的目录）
     */
    private String curlDirectory;
    
    /**
     * Excel文件下载保存目录
     */
    private String downloadDirectory = "D:\\Users\\ex_wuyh42.CN\\Desktop\\生产环境-慢接口\\auto-export";
    
    /**
     * HTTP连接超时时间（毫秒）
     */
    private int connectionTimeout = 30000;
    
    /**
     * HTTP读取超时时间（毫秒）
     */
    private int readTimeout = 120000; // 2分钟，考虑到Excel文件可能较大
    
    /**
     * 是否启用详细日志
     */
    private boolean enableVerboseLogging = true;
    
    // 构造函数
    public DownloadConfig() {}
    
    public DownloadConfig(String curlDirectory) {
        this.curlDirectory = curlDirectory;
    }
    
    public DownloadConfig(String curlDirectory, String downloadDirectory) {
        this.curlDirectory = curlDirectory;
        this.downloadDirectory = downloadDirectory;
    }
    
    // Getter和Setter方法
    public String getCurlDirectory() {
        return curlDirectory;
    }
    
    public void setCurlDirectory(String curlDirectory) {
        this.curlDirectory = curlDirectory;
    }
    
    public String getDownloadDirectory() {
        return downloadDirectory;
    }
    
    public void setDownloadDirectory(String downloadDirectory) {
        this.downloadDirectory = downloadDirectory;
    }
    
    public int getConnectionTimeout() {
        return connectionTimeout;
    }
    
    public void setConnectionTimeout(int connectionTimeout) {
        this.connectionTimeout = connectionTimeout;
    }
    
    public int getReadTimeout() {
        return readTimeout;
    }
    
    public void setReadTimeout(int readTimeout) {
        this.readTimeout = readTimeout;
    }
    
    public boolean isEnableVerboseLogging() {
        return enableVerboseLogging;
    }
    
    public void setEnableVerboseLogging(boolean enableVerboseLogging) {
        this.enableVerboseLogging = enableVerboseLogging;
    }
    
    @Override
    public String toString() {
        return "DownloadConfig{" +
                "curlDirectory='" + curlDirectory + '\'' +
                ", downloadDirectory='" + downloadDirectory + '\'' +
                ", connectionTimeout=" + connectionTimeout +
                ", readTimeout=" + readTimeout +
                ", enableVerboseLogging=" + enableVerboseLogging +
                '}';
    }
}
