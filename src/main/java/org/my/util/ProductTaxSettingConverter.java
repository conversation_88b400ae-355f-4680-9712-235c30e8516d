package org.my.util;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 产品税收编码配置Excel数据转SQL INSERT语句转换器
 * 专门用于处理产品税收编码配置数据的转换
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since 2025-06-23
 */
public class ProductTaxSettingConverter {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 将Excel数据转换为SQL INSERT语句并保存到指定路径
     *
     * @param excelFilePath Excel文件路径
     * @param outputSqlPath 输出SQL文件路径
     * @param tableName     目标表名
     * @throws IOException 文件操作异常
     */
    public static void convertExcelToInsertSql(String excelFilePath, String outputSqlPath, String tableName) throws IOException {
        System.out.println("开始读取Excel文件: " + excelFilePath);
        
        // 读取Excel数据
        String jsonData = ExcelToJsonUtil.convertExcelToJson(excelFilePath);
        JsonNode rootNode = objectMapper.readTree(jsonData);
        JsonNode sheetsArray = rootNode.get("sheets");
        
        if (sheetsArray == null || sheetsArray.size() == 0) {
            throw new RuntimeException("Excel文件中没有找到工作表");
        }
        
        // 获取第一个sheet（product_tax_setting）
        JsonNode firstSheet = sheetsArray.get(0);
        JsonNode dataArray = firstSheet.get("data");
        
        if (dataArray == null || dataArray.size() == 0) {
            throw new RuntimeException("Excel文件中没有找到数据");
        }
        
        System.out.println("找到 " + dataArray.size() + " 行数据");
        
        // 生成SQL语句
        List<String> sqlStatements = generateInsertStatements(dataArray, tableName);
        
        // 保存到文件
        saveSqlToFile(sqlStatements, outputSqlPath);
        
        System.out.println("SQL文件已生成: " + outputSqlPath);
        System.out.println("共生成 " + sqlStatements.size() + " 条INSERT语句");
    }

    /**
     * 生成INSERT语句列表
     *
     * @param dataArray 数据数组
     * @param tableName 表名
     * @return SQL语句列表
     */
    private static List<String> generateInsertStatements(JsonNode dataArray, String tableName) {
        List<String> sqlStatements = new ArrayList<>();
        
        // 定义字段映射（按照数据库表结构顺序）
        String[] fields = {
            "id", "ou_id", "tax_code", "tax_name", "spec_business_tag", 
            "start_time", "end_time", "remark", "create_by", "create_at", 
            "update_by", "create_user_name", "update_at", "deleted_flag"
        };
        
        // 构建INSERT语句的字段部分
        String fieldsPart = String.join(", ", fields);
        String insertPrefix = "INSERT INTO " + tableName + " (" + fieldsPart + ") VALUES ";
        
        // 遍历每行数据
        for (JsonNode row : dataArray) {
            StringBuilder valuesPart = new StringBuilder("(");
            
            for (int i = 0; i < fields.length; i++) {
                if (i > 0) {
                    valuesPart.append(", ");
                }
                
                String fieldName = fields[i];
                JsonNode fieldValue = row.get(fieldName);
                String sqlValue = convertToSqlValue(fieldValue, fieldName);
                valuesPart.append(sqlValue);
            }
            
            valuesPart.append(")");
            sqlStatements.add(insertPrefix + valuesPart.toString() + ";");
        }
        
        return sqlStatements;
    }

    /**
     * 将JSON值转换为SQL值
     *
     * @param fieldValue JSON字段值
     * @param fieldName  字段名
     * @return SQL格式的值
     */
    private static String convertToSqlValue(JsonNode fieldValue, String fieldName) {
        if (fieldValue == null || fieldValue.isNull()) {
            return "NULL";
        }
        
        String stringValue = fieldValue.asText();
        
        // 处理特殊的NULL标记
        if ("[NULL]".equals(stringValue) || stringValue.trim().isEmpty()) {
            return "NULL";
        }
        
        // 根据字段类型处理
        switch (fieldName) {
            case "id":
                // ID字段，保持字符串格式
                return "'" + escapeSqlString(stringValue) + "'";
                
            case "ou_id":
            case "create_by":
            case "update_by":
            case "spec_business_tag":
            case "deleted_flag":
                // 数值类型字段
                try {
                    if (stringValue.contains(".")) {
                        // 处理可能的浮点数，转换为整数
                        double doubleValue = Double.parseDouble(stringValue);
                        return String.valueOf((long) doubleValue);
                    } else {
                        return stringValue;
                    }
                } catch (NumberFormatException e) {
                    return "NULL";
                }
                
            case "start_time":
            case "end_time":
            case "create_at":
            case "update_at":
                // 日期时间字段
                if (stringValue.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                    return "'" + stringValue + "'";
                } else {
                    return "NULL";
                }
                
            case "tax_code":
            case "tax_name":
            case "remark":
            case "create_user_name":
                // 字符串字段
                String escapedValue = escapeSqlString(stringValue);
                return "'" + escapedValue + "'";
                
            default:
                // 默认作为字符串处理
                String defaultEscaped = escapeSqlString(stringValue);
                return "'" + defaultEscaped + "'";
        }
    }

    /**
     * SQL字符串转义
     *
     * @param value 原始字符串
     * @return 转义后的字符串
     */
    private static String escapeSqlString(String value) {
        if (value == null) {
            return "";
        }

        // 基本的SQL转义
        return value
            .replace("\\", "\\\\")  // 反斜杠转义
            .replace("'", "''")     // 单引号转义
            .replace("\"", "\\\"")  // 双引号转义
            .replace("\n", "\\n")   // 换行符转义
            .replace("\r", "\\r")   // 回车符转义
            .replace("\t", "\\t");  // 制表符转义
    }

    /**
     * 将SQL语句保存到文件
     *
     * @param sqlStatements SQL语句列表
     * @param outputPath    输出文件路径
     * @throws IOException 文件写入异常
     */
    private static void saveSqlToFile(List<String> sqlStatements, String outputPath) throws IOException {
        // 确保输出目录存在
        String parentDir = Paths.get(outputPath).getParent().toString();
        Files.createDirectories(Paths.get(parentDir));
        
        try (FileWriter writer = new FileWriter(outputPath, false)) {
            // 写入文件头信息
            writer.write("-- =====================================================\n");
            writer.write("-- 产品税收编码配置数据INSERT语句\n");
            writer.write("-- =====================================================\n");
            writer.write("-- 生成时间: " + dateFormat.format(new Date()) + "\n");
            writer.write("-- 记录数量: " + sqlStatements.size() + "\n");
            writer.write("-- 生成工具: ProductTaxSettingConverter v1.0\n");
            writer.write("-- =====================================================\n\n");
            
            // 添加事务控制
            writer.write("-- 开始事务\n");
            writer.write("START TRANSACTION;\n\n");
            
            // 写入SQL语句
            int batchSize = 50;
            for (int i = 0; i < sqlStatements.size(); i++) {
                // 每50条语句添加一个注释
                if (i % batchSize == 0) {
                    writer.write("-- Batch " + (i / batchSize + 1) + " (statements " + (i + 1) + " to " +
                        Math.min(i + batchSize, sqlStatements.size()) + ")\n");
                }
                
                writer.write(sqlStatements.get(i) + "\n");
                
                // 每50条语句后添加空行
                if ((i + 1) % batchSize == 0 && i < sqlStatements.size() - 1) {
                    writer.write("\n");
                }
            }
            
            writer.write("\n-- 提交事务\n");
            writer.write("COMMIT;\n\n");
            writer.write("-- 文件结束\n");
            
            writer.flush();
        }
    }

    /**
     * 将项目终止检查条件配置Excel数据转换为SQL INSERT语句并保存到指定路径
     *
     * @param excelFilePath Excel文件路径
     * @param outputSqlPath 输出SQL文件路径
     * @param tableName     目标表名
     * @throws IOException 文件操作异常
     */
    public static void convertProjectTerminationCheckToInsertSql(String excelFilePath, String outputSqlPath, String tableName) throws IOException {
        System.out.println("开始读取项目终止检查条件配置Excel文件: " + excelFilePath);

        // 读取Excel数据
        String jsonData = ExcelToJsonUtil.convertExcelToJson(excelFilePath);
        JsonNode rootNode = objectMapper.readTree(jsonData);
        JsonNode sheetsArray = rootNode.get("sheets");

        if (sheetsArray == null || sheetsArray.size() == 0) {
            throw new RuntimeException("Excel文件中没有找到工作表");
        }

        // 获取第一个sheet（project_termination_check_rel）
        JsonNode firstSheet = sheetsArray.get(0);
        JsonNode dataArray = firstSheet.get("data");

        if (dataArray == null || dataArray.size() == 0) {
            throw new RuntimeException("Excel文件中没有找到数据");
        }

        System.out.println("找到 " + dataArray.size() + " 行数据");

        // 生成SQL语句
        List<String> sqlStatements = generateProjectTerminationCheckInsertStatements(dataArray, tableName);

        // 保存到文件
        saveSimpleSqlToFile(sqlStatements, outputSqlPath);

        System.out.println("SQL文件已生成: " + outputSqlPath);
        System.out.println("共生成 " + sqlStatements.size() + " 条INSERT语句");
    }

    /**
     * 生成项目终止检查条件配置INSERT语句列表
     *
     * @param dataArray 数据数组
     * @param tableName 表名
     * @return SQL语句列表
     */
    private static List<String> generateProjectTerminationCheckInsertStatements(JsonNode dataArray, String tableName) {
        List<String> sqlStatements = new ArrayList<>();

        // 定义字段映射（按照数据库表结构顺序）
        String[] fields = {
            "id", "unit_id", "termination_type_id", "check_item_id", "yes_or_no",
            "create_by", "create_at", "update_by", "update_at", "version"
        };

        // 构建INSERT语句的字段部分
        String fieldsPart = String.join(", ", fields);
        String insertPrefix = "INSERT INTO " + tableName + " (" + fieldsPart + ") VALUES ";

        // 遍历每行数据
        for (JsonNode row : dataArray) {
            StringBuilder valuesPart = new StringBuilder("(");

            for (int i = 0; i < fields.length; i++) {
                if (i > 0) {
                    valuesPart.append(", ");
                }

                String fieldName = fields[i];
                JsonNode fieldValue = row.get(fieldName);
                String sqlValue = convertProjectTerminationCheckToSqlValue(fieldValue, fieldName);
                valuesPart.append(sqlValue);
            }

            valuesPart.append(")");
            sqlStatements.add(insertPrefix + valuesPart.toString() + ";");
        }

        return sqlStatements;
    }

    /**
     * 将项目终止检查条件配置JSON值转换为SQL值
     *
     * @param fieldValue JSON字段值
     * @param fieldName  字段名
     * @return SQL格式的值
     */
    private static String convertProjectTerminationCheckToSqlValue(JsonNode fieldValue, String fieldName) {
        if (fieldValue == null || fieldValue.isNull()) {
            return "NULL";
        }

        String stringValue = fieldValue.asText();

        // 处理特殊的NULL标记
        if ("[NULL]".equals(stringValue) || stringValue.trim().isEmpty()) {
            return "NULL";
        }

        // 根据字段类型处理
        switch (fieldName) {
            case "id":
                // ID字段，保持字符串格式
                return "'" + escapeSqlString(stringValue) + "'";

            case "unit_id":
            case "termination_type_id":
            case "check_item_id":
            case "yes_or_no":
            case "create_by":
            case "update_by":
            case "version":
                // 数值类型字段
                try {
                    if (stringValue.contains(".")) {
                        // 处理可能的浮点数，转换为整数
                        double doubleValue = Double.parseDouble(stringValue);
                        return String.valueOf((long) doubleValue);
                    } else {
                        return stringValue;
                    }
                } catch (NumberFormatException e) {
                    return "NULL";
                }

            case "create_at":
            case "update_at":
                // 日期时间字段
                if (stringValue.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
                    return "'" + stringValue + "'";
                } else {
                    return "NULL";
                }

            default:
                // 默认作为字符串处理
                String defaultEscaped = escapeSqlString(stringValue);
                return "'" + defaultEscaped + "'";
        }
    }

    /**
     * 将SQL语句保存到文件（简化版本，无注释）
     *
     * @param sqlStatements SQL语句列表
     * @param outputPath    输出文件路径
     * @throws IOException 文件写入异常
     */
    private static void saveSimpleSqlToFile(List<String> sqlStatements, String outputPath) throws IOException {
        // 确保输出目录存在
        String parentDir = Paths.get(outputPath).getParent().toString();
        Files.createDirectories(Paths.get(parentDir));

        try (FileWriter writer = new FileWriter(outputPath, false)) {
            // 写入SQL语句，不包含任何注释
            for (String sql : sqlStatements) {
                writer.write(sql + "\n");
            }

            writer.flush();
        }
    }

    /**
     * 使用独立方法处理项目终止检查条件配置Excel转SQL
     */
    public static void processProjectTerminationCheckConfig() {
        try {
            ProjectTerminationCheckSqlGenerator.generateProjectTerminationCheckInserts();
        } catch (Exception e) {
            System.err.println("处理项目终止检查条件配置时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 使用独立方法处理项目终止分类配置Excel转SQL
     *
     * 功能说明：
     * 1. 读取Excel数据：D:\Users\ex_wuyh42.CN\Desktop\文件\WDS环境搭建-配置\0623\项目终止分类配置.xlsx
     * 2. 查询数据库表结构：pam_ctc.project_termination_type
     * 3. 将Excel数据转换为SQL INSERT语句
     * 4. 处理数据类型转换、NULL值处理、SQL字符串转义
     * 5. 生成的SQL文件不包含任何注释
     * 6. 输出到：E:\output\project_termination_type_insert.sql
     */
    public static void processProjectTerminationTypeConfig() {
        try {
            ProjectTerminationTypeSqlGenerator.generateProjectTerminationTypeInserts();
        } catch (Exception e) {
            System.err.println("处理项目终止分类配置时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 使用独立方法处理用于我方签约单位印章管理员数据同步Excel转SQL
     *
     * 功能说明：
     * 1. 读取Excel数据：D:\Users\ex_wuyh42.CN\Desktop\文件\WDS环境搭建-配置\0623\用于我方签约单位印章管理员数据同步.xlsx
     * 2. 查询数据库表结构：pam_ctc.apply_contract_unit_config
     * 3. 将Excel数据转换为SQL INSERT语句
     * 4. 处理数据类型转换、NULL值处理、SQL字符串转义
     * 5. 生成的SQL文件不包含任何注释
     * 6. 输出到：E:\output\apply_contract_unit_config_insert.sql
     */
    public static void processApplyContractUnitConfig() {
        try {
            ApplyContractUnitConfigSqlGenerator.generateApplyContractUnitConfigInserts();
        } catch (Exception e) {
            System.err.println("处理用于我方签约单位印章管理员数据同步时发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 主方法，用于测试和执行转换
     */
    public static void main(String[] args) {
        try {
            // 调用独立方法处理用于我方签约单位印章管理员数据同步
            processApplyContractUnitConfig();

        } catch (Exception e) {
            System.err.println("转换过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
