package org.my.util;

import lombok.Data;

import java.math.BigDecimal;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

public class Main {
    public static void main(String[] args) {
        String s = "SDP-PAM-GERPPOSTANDARDPO-250626-0004308";
        System.out.println(s.substring(0, s.lastIndexOf("-", s.lastIndexOf("-") - 1)));
    }



    @Data
    public static class TestA{
        private String name;
    }
}