package org.my.util;

import com.google.common.collect.Lists;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicInteger;

public class sqlInsertIntoTemplate {

    public static void main(String[] args) {
        String template = "INSERT INTO pam_basedata.light_role_menu " +
                "(id, role_id, menu_id) " +
                "VALUES(%s, %s, %s);";

        ArrayList<Long> roleIdList = Lists.newArrayList(
                607792944841228288L,
                607794116561993728L,
                846699682464792576L,
                846701148978020352L,
                846702136141021184L,
                846704048324214784L,
                846705141103984640L,
                846707731543883776L,
                846738398847696896L,
                846739916510461952L,
                851501346434580480L,
                893283894571302912L,
                944267548621275136L,
                1144718503433994240L,
                1167551448238317568L,
                849283230233264128L,
                849285549968588800L,
                1014922322026430464L,
                1014929939297730560L,
                1014930148933238784L,
                1016087142964658176L,
                1016088479915835392L,
                1034045819210170368L,
                1214501994101133312L,
                844949703685570560L,
                846380615749074944L,
                846385746976702464L,
                846387157441445888L,
                846388256130990080L,
                846388838409437184L,
                846389517760856064L,
                964159203318956032L,
                893463297834090496L,
                893489014936436736L,
                893490026405429248L,
                893491834553106432L,
                893492671690047488L,
                893493507082158080L,
                893494196436992000L,
                893499253853257728L
        );
        StringBuilder sb = new StringBuilder();
        for (Long roleId : roleIdList) {
            sb.append(String.format(template, generateId(), roleId, "1306557270139297792"));
            sb.append("\n");
        }
        System.out.println(sb);
    }

    private static final AtomicInteger counter = new AtomicInteger(0);
    private static String lastGeneratedId = "";
    private static final Object lock = new Object();

    public static String generateId() {
        synchronized (lock) {
            String newId;
            do {
                // 获取当前时间
                LocalDateTime now = LocalDateTime.now();

                // 格式化年月日时分秒
                String timeStr = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

                // 获取计数器值并递增（0-999循环）
                int count = counter.getAndUpdate(n -> (n + 1) % 1000);

                // 组合最终ID：时间戳 + 3位序号
                newId = timeStr + String.format("%03d", count);

            } while (newId.equals(lastGeneratedId));

            // 保存最后生成的ID
            lastGeneratedId = newId;
            return newId;
        }
    }

}
