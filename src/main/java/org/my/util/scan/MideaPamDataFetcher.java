package org.my.util.scan;


import java.util.Arrays;
import java.util.List;

public class MideaPamDataFetcher {

    public static void main(String[] args) {
        String s = "wbstest01-01-040-230101-001-002/P20990509000608/P20990509000609";
        boolean contains = s.contains("/");
        if(contains){
            String[] array = s.split("/");
            for (int i = 1; i < array.length; i++) {
                System.out.println(array[i]);
            }
        }
    }
}
