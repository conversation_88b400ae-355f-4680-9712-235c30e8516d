package org.my.util;

/**
 * 特殊字符清理工具类 - 使用代码点遍历实现
 */
public final class StringSanitizerByIteration {

    private static final String REPLACEMENT_CHAR = " ";

    private StringSanitizerByIteration() {
    }

    /**
     * 替换字符串中所有定义的特殊字符为空格。
     *
     * @param input 可能包含特殊字符的输入字符串
     * @return 清理后的字符串，如果输入为null则返回null
     */
    public static String sanitize(String input) {
        if (input == null || input.isEmpty()) {
            return input;
        }

        final StringBuilder sb = new StringBuilder(input.length());

        // 遍历每一个Unicode代码点
        input.codePoints().forEach(codePoint -> {
            if (isInvalid(codePoint)) {
                sb.append(REPLACEMENT_CHAR);
            } else {
                sb.append(Character.toChars(codePoint));
            }
        });

        return sb.toString();
    }

    /**
     * 判断一个Unicode代码点是否为需要被替换的特殊字符。
     *
     * @param codePoint Unicode代码点
     * @return 如果是特殊字符则返回true，否则返回false
     */
    private static boolean isInvalid(int codePoint) {
        // 场景一：XML 1.0 非法字符
        if ((codePoint >= 0x00 && codePoint <= 0x08) ||
                (codePoint >= 0x0B && codePoint <= 0x0C) ||
                (codePoint >= 0x0E && codePoint <= 0x1F) ||
                (codePoint >= 0x7F && codePoint <= 0x9F) ||
                (codePoint == 0xFFFE || codePoint == 0xFFFF)) {
            return true;
        }

        // 场景二：4字节UTF-8字符（Unicode增补平面）
        // Character.isSupplementaryCodePoint(codePoint) 是更简洁的判断方式
        if (codePoint >= 0x10000) { // 所有增补平面的代码点都大于等于 0x10000
            return true;
        }

        return false;
    }

    public static void main(String[] args) {
        String s = "测试 特殊字 符|替|换\uDBC0\uDC03\uDBC0\uDC2D\uDBC0\uDC03\uDBC0\uDC1A\uDBC0\uDC03\uDBC0\uDC2D\uDBC0\uDC03\uDBC0\uDC17\uDBC0\uDC1C\uDBC0\uDC17\uDBC0\uDC1C11111111";
        String sanitize = sanitize(s);
        System.out.println(sanitize);
    }
}