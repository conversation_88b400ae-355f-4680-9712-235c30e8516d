# Excel转SQL工具使用说明

## 概述

`ExcelToSqlConverter` 是一个专门为PAM项目设计的Java工具类，用于将Excel文件中的数据转换为MySQL数据库的INSERT语句。

## 功能特性

- ✅ 支持读取Excel文件（.xlsx格式）
- ✅ 自动跳过标题行，处理数据行
- ✅ 支持4个工作表的数据转换
- ✅ 生成符合MySQL 5.7语法的INSERT语句
- ✅ 自动处理NULL值和特殊字符转义
- ✅ 为每个工作表生成独立的SQL文件
- ✅ 完整的错误处理和日志记录
- ✅ 数据类型自动识别和转换
- ✅ 事务控制和批量处理

## 支持的工作表映射

| Excel工作表名称 | 目标数据库表 |
|----------------|-------------|
| `material_custom_dict_header-MY4` | `pam_ctc.material_custom_dict_header` |
| `material_custom_dict-MY4` | `pam_ctc.material_custom_dict` |
| `material_custom_dict_header-MY5` | `pam_ctc.material_custom_dict_header` |
| `material_custom_dict-MY5` | `pam_ctc.material_custom_dict` |

## 使用方法

### 1. 配置文件路径

在 `ExcelToSqlConverter.java` 中修改以下常量：

```java
// Excel文件路径
private static final String EXCEL_FILE_PATH = "D:\\Users\\ex_wuyh42.CN\\Desktop\\文件\\WDS环境搭建-配置\\物料类别属性配置.xlsx";

// SQL文件输出目录
private static final String SQL_OUTPUT_DIR = "e:\\sql";
```

### 2. 运行工具

```bash
# 编译
javac -cp "lib/*" src/main/java/org/my/util/ExcelToSqlConverter.java

# 运行
java -cp "lib/*:src/main/java" org.my.util.ExcelToSqlConverter
```

或者在IDE中直接运行 `ExcelToSqlConverter.main()` 方法。

### 3. 查看输出

工具执行完成后，会在指定的输出目录生成以下SQL文件：

- `material_custom_dict_header-MY4.sql`
- `material_custom_dict-MY4.sql`
- `material_custom_dict_header-MY5.sql`
- `material_custom_dict-MY5.sql`

## 数据处理规则

### 字段类型处理

1. **ID字段**: 自动生成唯一ID（格式：yyyyMMddHHmmss + 3位序号）
2. **数值字段**: 自动识别并转换为相应的数值类型
3. **日期字段**: 支持多种日期格式，自动转换为MySQL标准格式
4. **字符串字段**: 自动转义特殊字符，限制长度为500字符

### NULL值处理

- Excel中的空单元格或`[NULL]`值转换为SQL的`NULL`
- 必填字段（如`id`、`organization_id`）会自动生成默认值
- `deleted_flag`字段默认值为`0`

### 特殊字符转义

- 单引号 `'` → `''`
- 反斜杠 `\` → `\\`
- 换行符 `\n` → `\\n`
- 制表符 `\t` → `\\t`
- 移除控制字符

## 生成的SQL文件格式

```sql
-- =====================================================
-- SQL INSERT statements generated from Excel sheet
-- =====================================================
-- Source sheet: material_custom_dict_header-MY4
-- Target table: pam_ctc.material_custom_dict_header
-- Generated at: 2025-06-18 14:30:00
-- Total statements: 486
-- Generated by: ExcelToSqlConverter v1.0
-- =====================================================

-- 开始事务
START TRANSACTION;

-- Batch 1 (statements 1 to 100)
INSERT INTO pam_ctc.material_custom_dict_header (id, coding_class, ...) VALUES ('202506170001', '机械设计类标准物料', ...);
...

-- 提交事务
COMMIT;

-- 文件结束
```

## 错误处理

### 常见错误及解决方案

1. **Excel文件不存在**
   - 检查文件路径是否正确
   - 确保文件存在且可读

2. **工作表不存在**
   - 检查Excel文件中是否包含所需的工作表
   - 确保工作表名称完全匹配

3. **数据格式错误**
   - 检查Excel中的数据格式
   - 查看日志中的具体错误信息

4. **输出目录无法创建**
   - 检查输出目录路径是否有效
   - 确保有足够的磁盘空间和写入权限

### 日志级别

- `INFO`: 正常处理信息
- `WARN`: 警告信息（如数据格式问题）
- `ERROR`: 错误信息（如必填字段为空）
- `DEBUG`: 调试信息（如跳过空行）

## 性能优化

- 支持大文件处理，每100行输出一次进度
- 批量写入SQL文件，提高I/O效率
- 错误容忍机制，单行错误不影响整体处理
- 内存优化，逐行处理避免内存溢出

## 数据验证

### 必填字段验证

- `id`: 主键，必须有值
- `organization_id`: 组织ID，必须有值

### 数据完整性检查

- SQL语句长度检查（超过10000字符会警告）
- 字符串长度限制（超过500字符会截断）
- 数值格式验证
- 日期格式验证

## 注意事项

1. **数据备份**: 执行SQL前请备份目标数据库
2. **事务控制**: 生成的SQL包含事务控制，确保数据一致性
3. **字符编码**: 输出文件使用UTF-8编码
4. **ID唯一性**: 自动生成的ID基于时间戳，确保唯一性
5. **数据类型**: 严格按照数据库表结构进行类型转换

## 扩展功能

如需支持其他Excel文件或数据库表，可以：

1. 修改 `SHEET_TABLE_MAPPING` 添加新的映射关系
2. 在 `HEADER_COLUMNS` 或 `DICT_COLUMNS` 中定义新表的字段
3. 更新 `getColumnsForTable()` 方法支持新表
4. 根据需要调整数据类型判断逻辑

## 技术依赖

- Java 8+
- Apache POI 5.2.4（Excel处理）
- SLF4J 1.7.36（日志）
- Logback 1.2.3（日志实现）

## 版本历史

- v1.0 (2025-06-18): 初始版本，支持物料类别属性配置数据转换
