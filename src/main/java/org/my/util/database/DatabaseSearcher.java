package org.my.util.database;


import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.*;
import java.util.*;
import java.util.concurrent.*;

/**
 * DatabaseSearcher 是一个用于在指定MySQL数据库中搜索关键字的工具类。
 * 它会遍历所有表的所有字段，查找包含指定关键字的记录，并返回匹配的表名和字段名。
 * 使用多线程和数据库连接池来提高检索效率。
 */
public class DatabaseSearcher {

    private static final Logger logger = LoggerFactory.getLogger(DatabaseSearcher.class);

    // 数据库连接配置
    private static final String JDBC_URL = "******************************************************************************************************************************************";
    private static final String DB_USER = "pam_sit";
    private static final String DB_PASSWORD = "tqVhr1zM5";

    // 搜索关键字
    private final String keyword;

    // 是否仅对整数类型字段使用精准匹配
    private final boolean useExactMatchForIntegers;

    // 线程池配置
    private static final int THREAD_POOL_SIZE = 10;

    // Connection Pool
    private final HikariDataSource dataSource;

    /**
     * 构造函数
     *
     * @param keyword                 搜索关键字
     * @param useExactMatchForIntegers 是否仅对整数类型字段使用精准匹配
     */
    public DatabaseSearcher(String keyword, boolean useExactMatchForIntegers) {
        this.keyword = keyword;
        this.useExactMatchForIntegers = useExactMatchForIntegers;
        this.dataSource = setupDataSource();
    }

    /**
     * 设置并返回HikariCP数据源
     *
     * @return HikariDataSource
     */
    private HikariDataSource setupDataSource() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(JDBC_URL);
        config.setUsername(DB_USER);
        config.setPassword(DB_PASSWORD);
        config.setMaximumPoolSize(THREAD_POOL_SIZE);
        config.setMinimumIdle(2);
        config.setIdleTimeout(30000);
        config.setConnectionTimeout(30000);
        config.setMaxLifetime(1800000);
        // 其他HikariCP配置根据需要调整
        return new HikariDataSource(config);
    }

    /**
     * 执行关键字搜索，并返回匹配的表名和字段名。
     *
     * @return List of MatchResult 包含匹配的表名和字段名
     */
    public List<MatchResult> search() {
        List<MatchResult> results = Collections.synchronizedList(new ArrayList<MatchResult>());
        ExecutorService executor = Executors.newFixedThreadPool(THREAD_POOL_SIZE);
        List<Future<Void>> futures = new ArrayList<>();

        try (Connection connection = dataSource.getConnection()) {
            logger.info("成功连接到数据库。URL: {}", JDBC_URL);

            // 获取所有列信息，根据开关决定是否仅选择整数类型字段
            List<ColumnInfo> columns = getAllColumns(connection, useExactMatchForIntegers);
            logger.info("找到 {} 个列需要搜索。", columns.size());

            // 遍历每个列，提交搜索任务
            for (ColumnInfo column : columns) {
                Future<Void> future = executor.submit(() -> {
                    searchColumn(column, results);
                    return null;
                });
                futures.add(future);
            }

            // 等待所有任务完成
            for (Future<Void> future : futures) {
                try {
                    future.get();
                } catch (InterruptedException ie) {
                    logger.error("搜索任务被中断: {}", ie.getMessage());
                    Thread.currentThread().interrupt();
                } catch (ExecutionException ee) {
                    logger.error("搜索任务执行出错: {}", ee.getCause().getMessage());
                }
            }

            logger.info("所有搜索任务已完成。共找到 {} 个匹配结果。", results.size());

        } catch (SQLException e) {
            logger.error("数据库连接或操作失败: {}", e.getMessage());
        } finally {
            // 关闭线程池
            executor.shutdown();
            try {
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException ie) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }

            // 关闭数据源
            if (dataSource != null && !dataSource.isClosed()) {
                dataSource.close();
            }
        }

        return results;
    }

    /**
     * 获取当前数据库中所有列信息，根据开关决定是否仅选择整数类型字段。
     *
     * @param connection               JDBC连接
     * @param useExactMatchForIntegers 是否仅选择整数类型字段
     * @return List<ColumnInfo> 所有列
     * @throws SQLException SQL异常
     */
    private List<ColumnInfo> getAllColumns(Connection connection, boolean useExactMatchForIntegers) throws SQLException {
        List<ColumnInfo> columns = new ArrayList<>();

        String query;
        if (useExactMatchForIntegers) {
            // 仅选择整数类型字段
            query = "SELECT TABLE_NAME, COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ? AND DATA_TYPE IN ('tinyint', 'smallint', 'mediumint', 'int', 'bigint')";
            logger.info("开启精准匹配，仅搜索整数类型字段。");
        } else {
            // 搜索所有字段
            query = "SELECT TABLE_NAME, COLUMN_NAME, DATA_TYPE FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = ?";
            logger.info("未开启精准匹配，搜索所有字段。");
        }

        try (PreparedStatement pstmt = connection.prepareStatement(query)) {
            pstmt.setString(1, connection.getCatalog());
            try (ResultSet rs = pstmt.executeQuery()) {
                while (rs.next()) {
                    String tableName = rs.getString("TABLE_NAME");
                    String columnName = rs.getString("COLUMN_NAME");
                    String dataType = rs.getString("DATA_TYPE");
                    columns.add(new ColumnInfo(tableName, columnName, dataType));
                }
            }
        }

        return columns;
    }

    /**
     * 在指定的列中搜索关键字，并将匹配的结果添加到结果列表中。
     *
     * @param column  ColumnInfo 包含表名和列名的信息
     * @param results List<MatchResult> 匹配结果列表
     */
    private void searchColumn(ColumnInfo column, List<MatchResult> results) {
        String table = column.getTableName();
        String columnName = column.getColumnName();
        String dataType = column.getDataType();

        String query;
        boolean isExactMatch = false;

        if (useExactMatchForIntegers && isIntegerType(dataType)) {
            // 尝试将关键字转换为Long
            Long exactValue;
            try {
                exactValue = Long.parseLong(keyword);
            } catch (NumberFormatException e) {
                logger.warn("搜索关键字 '{}' 不能转换为整数类型，用于字段 {}.{} 的精确匹配将被跳过。", keyword, table, columnName);
                return; // 跳过此列，因为无法进行精确匹配
            }

            query = String.format("SELECT 1 FROM `%s` WHERE `%s` = ? LIMIT 1", table, columnName);
            isExactMatch = true;
        } else {
            // 对非整数类型或不开启精确匹配，使用LIKE查询
            // 使用 CAST 将非字符串类型转换为 CHAR 以支持 LIKE 操作
            query = String.format("SELECT 1 FROM `%s` WHERE CAST(`%s` AS CHAR) LIKE ? LIMIT 1", table, columnName);
        }

        // 记录实际执行的SQL查询
        String executedQuery;
        if (isExactMatch) {
            executedQuery = query.replace("?", keyword);
        } else {
            executedQuery = query.replace("?", "'%" + keyword + "%'");
        }
        logger.debug("执行查询: {}", executedQuery);

        try (Connection connection = dataSource.getConnection();
             PreparedStatement pstmt = connection.prepareStatement(query)) {

            if (isExactMatch) {
                pstmt.setLong(1, Long.parseLong(keyword));
            } else {
                pstmt.setString(1, "%" + keyword + "%");
            }

            try (ResultSet rs = pstmt.executeQuery()) {
                if (rs.next()) {
                    MatchResult match = new MatchResult(table, columnName);
                    results.add(match);
                    logger.info("找到匹配 - 表: {}, 字段: {}", table, columnName);
                }
            }
        } catch (SQLException e) {
            logger.error("在表 {} 的字段 {} 中搜索时出错: {}", table, columnName, e.getMessage());
        } catch (NumberFormatException e) {
            logger.error("转换关键字 '{}' 为Long时出错，用于表 {} 的字段 {}: {}", keyword, table, columnName, e.getMessage());
        }
    }

    /**
     * 判断数据类型是否为整数类型。
     *
     * @param dataType 数据类型
     * @return boolean 是否为整数类型
     */
    private boolean isIntegerType(String dataType) {
        List<String> integerTypes = Arrays.asList(
                "tinyint", "smallint", "mediumint", "int", "bigint"
        );
        return integerTypes.contains(dataType.toLowerCase());
    }

    /**
     * 内部类，用于存储列的信息。
     */
    private static class ColumnInfo {
        private final String tableName;
        private final String columnName;
        private final String dataType;

        public ColumnInfo(String tableName, String columnName, String dataType) {
            this.tableName = tableName;
            this.columnName = columnName;
            this.dataType = dataType;
        }

        public String getTableName() {
            return tableName;
        }

        public String getColumnName() {
            return columnName;
        }

        public String getDataType() {
            return dataType;
        }
    }

    /**
     * 内部类，用于存储匹配结果。
     */
    public static class MatchResult {
        private final String tableName;
        private final String columnName;

        public MatchResult(String tableName, String columnName) {
            this.tableName = tableName;
            this.columnName = columnName;
        }

        public String getTableName() {
            return tableName;
        }

        public String getColumnName() {
            return columnName;
        }

        @Override
        public String toString() {
            return "MatchResult{" +
                    "tableName='" + tableName + '\'' +
                    ", columnName='" + columnName + '\'' +
                    '}';
        }
    }

    /**
     * 主方法，用于执行搜索操作。
     *
     * @param args 命令行参数，第一个参数为搜索关键字，第二个参数为可选的开关 "--exact-match"
     */
    public static void main(String[] args) {

        long start = System.currentTimeMillis();
        // 从命令行参数获取搜索关键字和开关
        String keyword = "0401";
        boolean useExactMatchForIntegers = false   ;

       /* if (args.length >= 1) {
            keyword = args[0];
        }

        if (args.length >= 2) {
            if (args[1].equalsIgnoreCase("--exact-match")) {
                useExactMatchForIntegers = true;
            }
        }*/

        if (keyword == null || keyword.trim().isEmpty()) {
            logger.error("搜索关键字不能为空。");
            System.out.println("搜索关键字不能为空。");
            System.out.println("Usage: java -cp \".:lib/*\" DatabaseSearcher <keyword> [--exact-match]");
            return;
        }

        // 如果启用了精准匹配，则记录相应信息
        if (useExactMatchForIntegers) {
            logger.info("已启用整数类型字段的精准匹配 (useExactMatchForIntegers=true)。");
        } else {
            logger.info("未启用整数类型字段的精准匹配 (useExactMatchForIntegers=false)。");
        }

        DatabaseSearcher searcher = new DatabaseSearcher(keyword.trim(), useExactMatchForIntegers);
        List<MatchResult> matches = searcher.search();

        if (matches.isEmpty()) {
            logger.info("没有找到包含关键字 '{}' 的任何匹配。", keyword);
            System.out.println("没有找到匹配的记录。");
        } else {
            logger.info("找到 {} 个匹配的记录。", matches.size());
            System.out.println("匹配的表和字段:");
            for (MatchResult match : matches) {
                System.out.println("表: " + match.getTableName() + ", 字段: " + match.getColumnName());
            }
        }
        long end = System.currentTimeMillis();
        System.out.println("耗时：" + (end - start) + "ms");
    }
}
