package org.my.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Excel转SQL工具使用示例
 * 演示如何使用ExcelToSqlConverter工具
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since 2025-06-18
 */
public class ExcelToSqlExample {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelToSqlExample.class);
    
    public static void main(String[] args) {
        logger.info("=== Excel转SQL工具使用示例 ===");
        
        // 示例1：基本使用
        basicUsageExample();
        
        // 示例2：自定义配置
        customConfigExample();
        
        // 示例3：错误处理示例
        errorHandlingExample();
        
        logger.info("=== 示例演示完成 ===");
    }
    
    /**
     * 基本使用示例
     */
    private static void basicUsageExample() {
        logger.info("--- 基本使用示例 ---");
        
        System.out.println("1. 配置Excel文件路径和输出目录");
        System.out.println("   Excel文件: D:\\Users\\ex_wuyh42.CN\\Desktop\\文件\\WDS环境搭建-配置\\物料类别属性配置.xlsx");
        System.out.println("   输出目录: e:\\sql");
        System.out.println();
        
        System.out.println("2. 运行转换工具");
        System.out.println("   java -cp \"lib/*:src/main/java\" org.my.util.ExcelToSqlConverter");
        System.out.println();
        
        System.out.println("3. 查看生成的SQL文件");
        System.out.println("   - material_custom_dict_header-MY4.sql");
        System.out.println("   - material_custom_dict-MY4.sql");
        System.out.println("   - material_custom_dict_header-MY5.sql");
        System.out.println("   - material_custom_dict-MY5.sql");
        System.out.println();
    }
    
    /**
     * 自定义配置示例
     */
    private static void customConfigExample() {
        logger.info("--- 自定义配置示例 ---");
        
        System.out.println("如果需要处理不同的Excel文件或输出到不同目录，可以修改以下配置：");
        System.out.println();
        
        System.out.println("// 修改Excel文件路径");
        System.out.println("private static final String EXCEL_FILE_PATH = \"你的Excel文件路径\";");
        System.out.println();
        
        System.out.println("// 修改输出目录");
        System.out.println("private static final String SQL_OUTPUT_DIR = \"你的输出目录\";");
        System.out.println();
        
        System.out.println("// 添加新的工作表映射");
        System.out.println("SHEET_TABLE_MAPPING.put(\"新工作表名\", \"目标数据库表名\");");
        System.out.println();
    }
    
    /**
     * 错误处理示例
     */
    private static void errorHandlingExample() {
        logger.info("--- 错误处理示例 ---");
        
        System.out.println("常见错误及解决方案：");
        System.out.println();
        
        System.out.println("1. 文件不存在错误：");
        System.out.println("   错误信息: Excel文件不存在: xxx");
        System.out.println("   解决方案: 检查文件路径是否正确，确保文件存在");
        System.out.println();
        
        System.out.println("2. 工作表不存在错误：");
        System.out.println("   错误信息: 未找到工作表: xxx");
        System.out.println("   解决方案: 检查Excel文件中是否包含所需的工作表");
        System.out.println();
        
        System.out.println("3. 数据验证错误：");
        System.out.println("   错误信息: 第X行必填字段 xxx 为空");
        System.out.println("   解决方案: 检查Excel中的数据完整性，补充必填字段");
        System.out.println();
        
        System.out.println("4. 权限错误：");
        System.out.println("   错误信息: SQL文件写入失败: xxx");
        System.out.println("   解决方案: 检查输出目录的写入权限");
        System.out.println();
    }
    
    /**
     * 生成的SQL文件示例
     */
    public static void showSqlFileExample() {
        System.out.println("=== 生成的SQL文件示例 ===");
        System.out.println();
        
        System.out.println("-- =====================================================");
        System.out.println("-- SQL INSERT statements generated from Excel sheet");
        System.out.println("-- =====================================================");
        System.out.println("-- Source sheet: material_custom_dict_header-MY4");
        System.out.println("-- Target table: pam_ctc.material_custom_dict_header");
        System.out.println("-- Generated at: 2025-06-18 14:30:00");
        System.out.println("-- Total statements: 486");
        System.out.println("-- Generated by: ExcelToSqlConverter v1.0");
        System.out.println("-- =====================================================");
        System.out.println();
        System.out.println("-- 开始事务");
        System.out.println("START TRANSACTION;");
        System.out.println();
        System.out.println("-- Batch 1 (statements 1 to 100)");
        System.out.println("INSERT INTO pam_ctc.material_custom_dict_header (");
        System.out.println("    id, coding_class, coding_class_code, coding_middleclass, coding_middleclass_code,");
        System.out.println("    coding_subclass, coding_subclass_code, organization_id, organization_name, remark,");
        System.out.println("    create_by, create_name, create_at, update_by, update_name, update_at, deleted_flag");
        System.out.println(") VALUES (");
        System.out.println("    '202506170001', '机械设计类标准物料', '01', '紧固件', '001',");
        System.out.println("    '挡圈', '01', 101011, 'INV_MY4_上海瑞仕格科技有限公司', NULL,");
        System.out.println("    -1, '初始化', NULL, NULL, NULL, NULL, 0");
        System.out.println(");");
        System.out.println();
        System.out.println("-- 更多INSERT语句...");
        System.out.println();
        System.out.println("-- 提交事务");
        System.out.println("COMMIT;");
        System.out.println();
        System.out.println("-- 文件结束");
        System.out.println();
    }
    
    /**
     * 数据类型处理示例
     */
    public static void showDataTypeHandling() {
        System.out.println("=== 数据类型处理示例 ===");
        System.out.println();
        
        System.out.println("1. 字符串类型：");
        System.out.println("   Excel: 机械设计类标准物料");
        System.out.println("   SQL:   '机械设计类标准物料'");
        System.out.println();
        
        System.out.println("2. 数值类型：");
        System.out.println("   Excel: 101011");
        System.out.println("   SQL:   101011");
        System.out.println();
        
        System.out.println("3. 日期类型：");
        System.out.println("   Excel: 2022-07-05 17:54:00");
        System.out.println("   SQL:   '2022-07-05 17:54:00'");
        System.out.println();
        
        System.out.println("4. NULL值：");
        System.out.println("   Excel: [NULL] 或 空单元格");
        System.out.println("   SQL:   NULL");
        System.out.println();
        
        System.out.println("5. 特殊字符转义：");
        System.out.println("   Excel: O'Connor's");
        System.out.println("   SQL:   'O''Connor''s'");
        System.out.println();
    }
}
