package org.my.util;
import java.io.*;
import java.nio.charset.StandardCharsets;
import java.nio.file.*;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Java项目代码评审包生成工具（全项目代码版本）
 * 分析Java项目结构并生成包含所有类代码的完整评审文档
 */
public class CodeReviewMarkdownGenerator {
    // 项目根目录
    private final Path projectRoot;
    // 输出文件
    private final Path outputFile;
    // Java文件列表
    private List<Path> javaFiles;
    // 配置文件列表
    private List<Path> configFiles;
    // Markdown构建器
    private final StringBuilder markdownContent = new StringBuilder();

    public CodeReviewMarkdownGenerator(String projectPath) {
        this.projectRoot = Paths.get(projectPath).toAbsolutePath();
        String timestamp = new SimpleDateFormat("yyyyMMdd_HHmmss").format(new Date());
        this.outputFile = this.projectRoot.resolve("Java项目代码评审_" + timestamp + ".md");
    }

    /**
     * 主方法 - 程序入口
     */
    public static void main(String[] args) {
        try {
            // 默认使用当前目录作为项目根目录
            String projectPath = "E:\\project\\mybatis-hotreload-agent";
            if (args.length > 0) {
                projectPath = args[0];
            }

            CodeReviewMarkdownGenerator generator = new CodeReviewMarkdownGenerator(projectPath);
            generator.run();

        } catch (Exception e) {
            System.err.println("生成代码评审文档时发生错误：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 运行整个评审文档生成流程
     */
    public void run() throws IOException {
        System.out.println("开始生成Java项目完整代码评审文档...");

        // 创建文档标题
        generateDocumentHeader();

        // 查找所有Java文件和配置文件
        findAllProjectFiles();

        // 生成项目路径结构
        generateProjectStructure();

        // 生成项目依赖信息
        generateDependencyInfo();

        // 生成所有类文件内容
        generateAllClassContents();

        // 生成所有配置文件内容
        generateAllConfigContents();

        // 写入文件
        Files.write(outputFile, markdownContent.toString().getBytes(StandardCharsets.UTF_8));

        System.out.println("代码评审文档生成完成！");
        System.out.println("评审文档路径: " + outputFile);
        System.out.println("包含 " + javaFiles.size() + " 个Java类文件和 " + configFiles.size() + " 个配置文件");
    }

    /**
     * 生成文档标题和简介
     */
    private void generateDocumentHeader() {
        markdownContent.append("# Java项目代码评审文档\n\n");
        markdownContent.append("*自动生成于 ").append(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(new Date())).append("*\n\n");
        markdownContent.append("本文档包含整个Java项目的完整代码评审内容，包括项目结构、依赖信息、所有类文件和配置文件的代码。\n\n");
        markdownContent.append("## 目录\n\n");
        markdownContent.append("1. [项目结构](#项目结构)\n");
        markdownContent.append("2. [项目依赖](#项目依赖)\n");
        markdownContent.append("3. [类文件](#类文件)\n");
        markdownContent.append("4. [配置文件](#配置文件)\n\n");
        markdownContent.append("---\n\n");
    }

    /**
     * 查找所有Java文件和配置文件
     */
    private void findAllProjectFiles() throws IOException {
        System.out.println("正在查找项目文件...");

        // 查找所有Java文件
        javaFiles = Files.walk(projectRoot)
                .filter(p -> !p.toString().contains("target") && !p.toString().contains("build")) // 排除构建目录
                .filter(p -> !p.toString().contains(".git")) // 排除git目录
                .filter(p -> p.toString().endsWith(".java"))
                .collect(Collectors.toList());

        System.out.println("找到 " + javaFiles.size() + " 个Java文件");

        // 查找所有配置文件
        configFiles = Files.walk(projectRoot)
                .filter(p -> !p.toString().contains("target") && !p.toString().contains("build"))
                .filter(p -> !p.toString().contains(".git"))
                .filter(p -> {
                    String fileName = p.getFileName().toString();
                    return (fileName.endsWith(".properties") ||
                            fileName.endsWith(".yml") ||
                            fileName.endsWith(".yaml") ||
                            fileName.endsWith(".xml") ||
                            fileName.endsWith(".json")) &&
                            !(fileName.contains("pom.xml"));  // pom.xml单独处理
                })
                .collect(Collectors.toList());

        System.out.println("找到 " + configFiles.size() + " 个配置文件");
    }

    /**
     * 生成项目路径结构（详细版）
     */
    private void generateProjectStructure() throws IOException {
        System.out.println("正在生成项目结构...");
        markdownContent.append("## 项目结构 {#项目结构}\n\n");

        // 项目根目录名称
        markdownContent.append("项目根目录: **").append(projectRoot.getFileName()).append("**\n\n");

        // 生成详细的目录结构
        markdownContent.append("```\n");
        markdownContent.append(projectRoot.getFileName().toString()).append("\n");

        // 调用递归函数生成目录树
        generateDirectoryTree(projectRoot, 0, markdownContent);

        markdownContent.append("```\n\n");

        // 项目统计信息
        markdownContent.append("### 项目统计\n\n");
        markdownContent.append("- **Java文件总数**: ").append(javaFiles.size()).append("\n");

        // 统计总代码行数
        long totalLines = 0;
        for (Path file : javaFiles) {
            totalLines += Files.lines(file).count();
        }
        markdownContent.append("- **Java代码总行数**: ").append(totalLines).append("\n");
        markdownContent.append("- **配置文件总数**: ").append(configFiles.size()).append("\n\n");

        // 统计包信息
        Map<String, Integer> packageStats = new HashMap<>();
        for (Path file : javaFiles) {
            String packageName = extractPackageName(file);
            if (packageName != null) {
                packageStats.put(packageName, packageStats.getOrDefault(packageName, 0) + 1);
            }
        }

        markdownContent.append("- **包总数**: ").append(packageStats.size()).append("\n\n");

        markdownContent.append("### 包统计\n\n");
        markdownContent.append("| 包名 | 类文件数 |\n");
        markdownContent.append("| ---- | ------ |\n");

        packageStats.entrySet().stream()
                .sorted((e1, e2) -> {
                    // 先按文件数降序，相同时按包名排序
                    int countCompare = e2.getValue().compareTo(e1.getValue());
                    if (countCompare != 0) return countCompare;
                    return e1.getKey().compareTo(e2.getKey());
                })
                .forEach(entry -> {
                    markdownContent.append("| ").append(entry.getKey()).append(" | ")
                            .append(entry.getValue()).append(" |\n");
                });

        markdownContent.append("\n---\n\n");
    }

    /**
     * 递归生成目录树
     */
    private void generateDirectoryTree(Path directory, int level, StringBuilder sb) {
        try {
            if (directory.equals(outputFile.getParent()) &&
                    outputFile.getFileName().toString().equals(directory.getFileName().toString())) {
                return; // 跳过输出文件
            }

            List<Path> paths = Files.list(directory)
                    .filter(p -> !p.equals(outputFile)) // 排除输出文件
                    .filter(p -> !p.toString().contains("target") && !p.toString().contains("build")) // 排除构建目录
                    .filter(p -> !p.toString().contains(".git")) // 排除git目录
                    .filter(p -> !p.getFileName().toString().startsWith(".")) // 排除隐藏文件
                    .sorted((a, b) -> {
                        // 先目录后文件，相同类型按名称排序
                        if (Files.isDirectory(a) && !Files.isDirectory(b)) return -1;
                        if (!Files.isDirectory(a) && Files.isDirectory(b)) return 1;
                        return a.getFileName().toString().compareTo(b.getFileName().toString());
                    })
                    .collect(Collectors.toList());

            for (int i = 0; i < paths.size(); i++) {
                Path path = paths.get(i);
                boolean isLast = (i == paths.size() - 1);

                // 生成缩进
                for (int j = 0; j < level; j++) {
                    sb.append(j == level - 1 ? (isLast ? "    " : "│   ") : "    ");
                }

                sb.append(isLast ? "└── " : "├── ").append(path.getFileName()).append("\n");

                if (Files.isDirectory(path)) {
                    generateDirectoryTree(path, level + 1, sb);
                }
            }
        } catch (IOException e) {
            sb.append("Error reading directory: ").append(e.getMessage()).append("\n");
        }
    }

    /**
     * 生成项目依赖信息
     */
    private void generateDependencyInfo() throws IOException {
        System.out.println("正在分析项目依赖...");
        markdownContent.append("## 项目依赖 {#项目依赖}\n\n");

        // 检查pom.xml
        Path pomFile = projectRoot.resolve("pom.xml");
        if (Files.exists(pomFile)) {
            markdownContent.append("### Maven项目依赖 (pom.xml)\n\n");
            List<String> pomContent = Files.readAllLines(pomFile);

            // 提取项目信息
            String groupId = extractXmlTag(pomContent, "groupId");
            String artifactId = extractXmlTag(pomContent, "artifactId");
            String version = extractXmlTag(pomContent, "version");

            markdownContent.append("- **GroupId**: ").append(groupId != null ? groupId : "未指定").append("\n");
            markdownContent.append("- **ArtifactId**: ").append(artifactId != null ? artifactId : "未指定").append("\n");
            markdownContent.append("- **Version**: ").append(version != null ? version : "未指定").append("\n\n");

            // 分析依赖
            List<String> dependencies = extractDependencies(pomContent);
            markdownContent.append("#### 所有Maven依赖\n\n");

            if (dependencies.isEmpty()) {
                markdownContent.append("未找到依赖项\n\n");
            } else {
                markdownContent.append("| 依赖项 | 分组 |\n");
                markdownContent.append("| ------ | ---- |\n");

                // 按照分组对依赖进行分类
                Map<String, List<String>> groupedDeps = new HashMap<>();
                for (String dep : dependencies) {
                    String[] parts = dep.split(":");
                    if (parts.length >= 2) {
                        String group = parts[0];
                        groupedDeps.computeIfAbsent(group, k -> new ArrayList<>()).add(dep);
                    } else {
                        groupedDeps.computeIfAbsent("其他", k -> new ArrayList<>()).add(dep);
                    }
                }

                // 按照分组显示依赖项
                groupedDeps.entrySet().stream()
                        .sorted(Map.Entry.comparingByKey())
                        .forEach(entry -> {
                            String group = entry.getKey();
                            List<String> deps = entry.getValue();
                            deps.sort(String::compareTo);

                            boolean first = true;
                            for (String dep : deps) {
                                markdownContent.append("| ").append(dep).append(" | ");
                                if (first) {
                                    markdownContent.append(group).append(" |\n");
                                    first = false;
                                } else {
                                    markdownContent.append(" |\n");
                                }
                            }
                        });

                markdownContent.append("\n");
            }

            // 添加完整的pom.xml
            markdownContent.append("#### 完整的pom.xml\n\n");
            markdownContent.append("<details>\n");
            markdownContent.append("<summary>点击展开完整pom.xml</summary>\n\n");
            markdownContent.append("```xml\n");
            for (String line : pomContent) {
                markdownContent.append(line).append("\n");
            }
            markdownContent.append("```\n\n");
            markdownContent.append("</details>\n\n");
        }

        // 检查build.gradle
        Path gradleFile = projectRoot.resolve("build.gradle");
        if (Files.exists(gradleFile)) {
            markdownContent.append("### Gradle项目依赖 (build.gradle)\n\n");
            List<String> gradleContent = Files.readAllLines(gradleFile);

            // 提取Gradle依赖
            List<String> dependencies = extractGradleDependencies(gradleContent);

            if (!dependencies.isEmpty()) {
                markdownContent.append("#### Gradle依赖\n\n");
                markdownContent.append("```gradle\n");
                for (String dep : dependencies) {
                    markdownContent.append(dep).append("\n");
                }
                markdownContent.append("```\n\n");
            }

            // 添加完整的build.gradle
            markdownContent.append("#### 完整的build.gradle\n\n");
            markdownContent.append("<details>\n");
            markdownContent.append("<summary>点击展开完整build.gradle</summary>\n\n");
            markdownContent.append("```gradle\n");
            for (String line : gradleContent) {
                markdownContent.append(line).append("\n");
            }
            markdownContent.append("```\n\n");
            markdownContent.append("</details>\n\n");
        }

        if (!Files.exists(pomFile) && !Files.exists(gradleFile)) {
            markdownContent.append("未找到标准构建文件(pom.xml或build.gradle)\n\n");
        }

        // 包级别依赖分析
        markdownContent.append("### 包级别依赖分析\n\n");

        // 分析所有类的导入关系
        Map<String, List<String>> imports = new HashMap<>();
        Map<String, Integer> mostDependedOn = new HashMap<>();

        for (Path file : javaFiles) {
            String packageName = extractPackageName(file);
            String className = file.getFileName().toString().replace(".java", "");

            if (packageName != null) {
                String fullClassName = packageName + "." + className;
                List<String> importList = extractImports(file);
                imports.put(fullClassName, importList);

                // 统计被依赖次数
                for (String imported : importList) {
                    // 忽略java标准库和javax
                    if (!imported.startsWith("java.") && !imported.startsWith("javax.")) {
                        mostDependedOn.put(imported, mostDependedOn.getOrDefault(imported, 0) + 1);
                    }
                }
            }
        }

        // 最常被依赖的类
        markdownContent.append("#### 最常被依赖的类\n\n");
        markdownContent.append("| 类名 | 被引用次数 |\n");
        markdownContent.append("| ---- | ---------- |\n");

        mostDependedOn.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .limit(30) // 显示前30个最常被依赖的类
                .forEach(entry -> {
                    markdownContent.append("| ").append(entry.getKey()).append(" | ")
                            .append(entry.getValue()).append(" |\n");
                });

        markdownContent.append("\n");

        markdownContent.append("---\n\n");
    }

    /**
     * 从build.gradle中提取依赖信息
     */
    private List<String> extractGradleDependencies(List<String> gradleContent) {
        List<String> dependencies = new ArrayList<>();
        boolean inDependenciesBlock = false;

        for (String line : gradleContent) {
            line = line.trim();

            if (line.startsWith("dependencies {")) {
                inDependenciesBlock = true;
                continue;
            }

            if (inDependenciesBlock) {
                if (line.equals("}")) {
                    inDependenciesBlock = false;
                    continue;
                }

                // 检测依赖行
                if (line.contains("implementation") || line.contains("compile") ||
                        line.contains("testImplementation") || line.contains("testCompile") ||
                        line.contains("api") || line.contains("runtimeOnly")) {
                    dependencies.add(line);
                }
            }
        }

        return dependencies;
    }

    /**
     * 提取XML标签内容
     */
    private String extractXmlTag(List<String> content, String tagName) {
        Pattern pattern = Pattern.compile("<" + tagName + ">(.*?)</" + tagName + ">");
        for (String line : content) {
            Matcher matcher = pattern.matcher(line);
            if (matcher.find()) {
                return matcher.group(1).trim();
            }
        }
        return null;
    }

    /**
     * 提取Maven依赖
     */
    private List<String> extractDependencies(List<String> pomContent) {
        List<String> dependencies = new ArrayList<>();
        boolean inDependencies = false;
        String currentDep = "";
        String groupId = null;
        String artifactId = null;
        String version = null;

        for (String line : pomContent) {
            line = line.trim();
            if (line.contains("<dependencies>")) {
                inDependencies = true;
                continue;
            }
            if (line.contains("</dependencies>")) {
                inDependencies = false;
                continue;
            }

            if (inDependencies) {
                if (line.contains("<dependency>")) {
                    groupId = null;
                    artifactId = null;
                    version = null;
                } else if (line.contains("</dependency>")) {
                    if (groupId != null && artifactId != null) {
                        currentDep = groupId + ":" + artifactId;
                        if (version != null) {
                            currentDep += ":" + version;
                        }
                        dependencies.add(currentDep);
                    }
                } else if (line.contains("<groupId>")) {
                    groupId = line.replaceAll("</?groupId>", "").trim();
                } else if (line.contains("<artifactId>")) {
                    artifactId = line.replaceAll("</?artifactId>", "").trim();
                } else if (line.contains("<version>")) {
                    version = line.replaceAll("</?version>", "").trim();
                }
            }
        }

        return dependencies;
    }

    /**
     * 生成所有类文件内容
     */
    private void generateAllClassContents() throws IOException {
        System.out.println("正在生成所有类内容...");
        markdownContent.append("## 类文件 {#类文件}\n\n");

        // 按包组织类文件
        Map<String, List<Path>> packageToFiles = new HashMap<>();
        for (Path file : javaFiles) {
            String packageName = extractPackageName(file);
            if (packageName == null) {
                packageName = "(默认包)";
            }
            packageToFiles.computeIfAbsent(packageName, k -> new ArrayList<>()).add(file);
        }

        // 创建包索引
        markdownContent.append("### 包索引\n\n");
        List<String> packageNames = new ArrayList<>(packageToFiles.keySet());
        Collections.sort(packageNames);

        for (int i = 0; i < packageNames.size(); i++) {
            String packageName = packageNames.get(i);
            markdownContent.append(i + 1).append(". [").append(packageName).append("](#pkg-").append(sanitizeAnchor(packageName)).append(")\n");
        }
        markdownContent.append("\n");

        // 处理每个包
        for (String packageName : packageNames) {
            List<Path> files = packageToFiles.get(packageName);

            // 按文件名排序
            files.sort(Comparator.comparing(p -> p.getFileName().toString()));

            markdownContent.append("### ").append(packageName).append(" {#pkg-").append(sanitizeAnchor(packageName)).append("}\n\n");
            markdownContent.append("包含 ").append(files.size()).append(" 个类文件\n\n");

            // 生成类索引
            markdownContent.append("#### 类索引\n\n");
            for (int i = 0; i < files.size(); i++) {
                Path file = files.get(i);
                String className = file.getFileName().toString().replace(".java", "");
                markdownContent.append(i + 1).append(". [").append(className).append("](#cls-").append(sanitizeAnchor(packageName + "-" + className)).append(")\n");
            }
            markdownContent.append("\n");

            // 生成每个类的详细内容
            for (Path file : files) {
                String className = file.getFileName().toString().replace(".java", "");
                markdownContent.append("#### ").append(className).append(" {#cls-").append(sanitizeAnchor(packageName + "-" + className)).append("}\n\n");

                // 尝试分析类的作用（基于类注释或名称）
                String classUsage = analyzeClassUsage(file);
                markdownContent.append("**类的作用**: ").append(classUsage).append("\n\n");

                // 显示类文件路径
                markdownContent.append("**路径**: `").append(projectRoot.relativize(file)).append("`\n\n");

                // 提取类的导入语句
                List<String> imports = extractImportLines(file);
                if (!imports.isEmpty()) {
                    markdownContent.append("**导入**:\n");
                    markdownContent.append("<details>\n");
                    markdownContent.append("<summary>展开查看导入语句</summary>\n\n");
                    markdownContent.append("```java\n");
                    for (String imp : imports) {
                        markdownContent.append(imp).append("\n");
                    }
                    markdownContent.append("```\n");
                    markdownContent.append("</details>\n\n");
                }

                // 读取类的完整代码
                List<String> classContent = Files.readAllLines(file);
                long lineCount = classContent.size();

                markdownContent.append("**代码行数**: ").append(lineCount).append("\n\n");

                markdownContent.append("**完整代码**:\n");
                markdownContent.append("<details>\n");
                markdownContent.append("<summary>展开查看完整代码</summary>\n\n");
                markdownContent.append("```java\n");
                for (String line : classContent) {
                    markdownContent.append(line).append("\n");
                }
                markdownContent.append("```\n");
                markdownContent.append("</details>\n\n");

                // 分析类中的方法
                List<Method> methods = extractMethods(classContent);
                if (!methods.isEmpty()) {
                    markdownContent.append("**方法列表** (").append(methods.size()).append("个方法):\n\n");
                    markdownContent.append("| 方法名 | 行数 | 起始行 |\n");
                    markdownContent.append("| ------ | ---- | ------ |\n");

                    for (Method method : methods) {
                        markdownContent.append("| ").append(method.getName()).append(" | ")
                                .append(method.getLength()).append(" | ")
                                .append(method.getStartLine()).append(" |\n");
                    }
                    markdownContent.append("\n");
                } else {
                    markdownContent.append("**未检测到方法**\n\n");
                }
            }
        }

        markdownContent.append("---\n\n");
    }

    /**
     * 分析类的作用（基于类注释或名称）
     */
    private String analyzeClassUsage(Path file) {
        try {
            List<String> lines = Files.readAllLines(file);

            // 查找类注释
            boolean inComment = false;
            StringBuilder commentBuilder = new StringBuilder();

            for (String line : lines) {
                line = line.trim();

                // 查找JavaDoc注释
                if (line.startsWith("/**")) {
                    inComment = true;
                    continue;
                }

                if (inComment) {
                    if (line.endsWith("*/")) {
                        inComment = false;
                        String comment = commentBuilder.toString().trim();
                        if (!comment.isEmpty()) {
                            // 返回第一行非空注释
                            String[] parts = comment.split("\n");
                            for (String part : parts) {
                                part = part.trim();
                                if (!part.isEmpty() && !part.startsWith("@")) {
                                    return part;
                                }
                            }
                            return comment;
                        }
                    } else {
                        // 处理注释内容
                        String content = line;
                        if (content.startsWith("*")) {
                            content = content.substring(1).trim();
                        }
                        if (!content.isEmpty() && !content.startsWith("@")) {
                            commentBuilder.append(content).append("\n");
                        }
                    }
                }

                // 如果找到类定义但没找到有效注释，则基于类名和实现接口猜测作用
                if (line.contains("class ") || line.contains("interface ") || line.contains("enum ")) {
                    String className = file.getFileName().toString().replace(".java", "");

                    // 基于命名模式猜测
                    if (className.endsWith("Controller")) {
                        return "控制器类，处理HTTP请求";
                    } else if (className.endsWith("Service")) {
                        return "服务类，提供业务逻辑";
                    } else if (className.endsWith("Repository") || className.endsWith("Dao")) {
                        return "数据访问类，处理数据持久化";
                    } else if (className.endsWith("Entity") || className.endsWith("Model")) {
                        return "实体类，表示业务数据";
                    } else if (className.endsWith("Util") || className.endsWith("Utils")) {
                        return "工具类，提供通用功能";
                    } else if (className.endsWith("Config") || className.endsWith("Configuration")) {
                        return "配置类，提供应用配置";
                    } else if (className.endsWith("Exception")) {
                        return "异常类，处理错误情况";
                    } else if (className.endsWith("Factory")) {
                        return "工厂类，创建其他对象";
                    } else if (className.endsWith("Builder")) {
                        return "构建器类，用于构建复杂对象";
                    } else if (line.contains("interface")) {
                        return "接口定义";
                    } else if (line.contains("enum")) {
                        return "枚举类型";
                    }

                    return "功能未明确的类，可能是核心业务类";
                }
            }

            return "未能确定类的作用，需要人工分析";

        } catch (IOException e) {
            return "无法读取文件内容";
        }
    }

    /**
     * 生成所有配置文件内容
     */
    private void generateAllConfigContents() throws IOException {
        System.out.println("正在生成所有配置文件内容...");
        markdownContent.append("## 配置文件 {#配置文件}\n\n");

        if (configFiles.isEmpty()) {
            markdownContent.append("未找到配置文件\n\n");
            return;
        }

        // 按文件类型分组
        Map<String, List<Path>> fileTypeToFiles = new HashMap<>();
        for (Path file : configFiles) {
            String fileName = file.getFileName().toString();
            String extension = fileName.substring(fileName.lastIndexOf('.') + 1);
            fileTypeToFiles.computeIfAbsent(extension, k -> new ArrayList<>()).add(file);
        }

        // 处理每种文件类型
        for (Map.Entry<String, List<Path>> entry : fileTypeToFiles.entrySet()) {
            String fileType = entry.getKey();
            List<Path> files = entry.getValue();

            // 按文件名排序
            files.sort(Comparator.comparing(p -> p.getFileName().toString()));

            markdownContent.append("### ").append(fileType.toUpperCase()).append(" 配置文件\n\n");
            markdownContent.append("| 文件名 | 路径 |\n");
            markdownContent.append("| ------ | ---- |\n");

            for (Path file : files) {
                String fileName = file.getFileName().toString();
                String relativePath = projectRoot.relativize(file).toString();
                markdownContent.append("| [").append(fileName).append("](#cfg-").append(sanitizeAnchor(fileName)).append(") | ")
                        .append(relativePath).append(" |\n");
            }
            markdownContent.append("\n");

            // 生成每个配置文件的详细内容
            for (Path file : files) {
                String fileName = file.getFileName().toString();
                markdownContent.append("#### ").append(fileName).append(" {#cfg-").append(sanitizeAnchor(fileName)).append("}\n\n");

                // 显示文件路径
                markdownContent.append("**路径**: `").append(projectRoot.relativize(file)).append("`\n\n");

                // 读取文件内容
                try {
                    List<String> content = Files.readAllLines(file);

                    // 根据文件类型选择适当的语法高亮
                    String syntaxHighlight;
                    if (fileName.endsWith(".properties")) {
                        syntaxHighlight = "properties";
                    } else if (fileName.endsWith(".yml") || fileName.endsWith(".yaml")) {
                        syntaxHighlight = "yaml";
                    } else if (fileName.endsWith(".xml")) {
                        syntaxHighlight = "xml";
                    } else if (fileName.endsWith(".json")) {
                        syntaxHighlight = "json";
                    } else {
                        syntaxHighlight = "plaintext";
                    }

                    markdownContent.append("**文件内容**:\n");
                    markdownContent.append("<details>\n");
                    markdownContent.append("<summary>展开查看内容</summary>\n\n");
                    markdownContent.append("```").append(syntaxHighlight).append("\n");
                    for (String line : content) {
                        markdownContent.append(line).append("\n");
                    }
                    markdownContent.append("```\n");
                    markdownContent.append("</details>\n\n");

                } catch (IOException e) {
                    markdownContent.append("**无法读取文件内容**: ").append(e.getMessage()).append("\n\n");
                }
            }
        }
    }

    /**
     * 从Java文件中提取包名
     */
    private String extractPackageName(Path file) {
        try (BufferedReader reader = Files.newBufferedReader(file)) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.startsWith("package ")) {
                    return line.substring(8, line.indexOf(';')).trim();
                }
            }
        } catch (IOException e) {
            // 忽略读取错误
        }
        return null;
    }

    /**
     * 从Java文件中提取导入语句
     */
    private List<String> extractImports(Path file) {
        List<String> imports = new ArrayList<>();
        try (BufferedReader reader = Files.newBufferedReader(file)) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.startsWith("import ")) {
                    String imported = line.substring(7, line.indexOf(';')).trim();
                    imports.add(imported);
                }
            }
        } catch (IOException e) {
            // 忽略读取错误
        }
        return imports;
    }

    /**
     * 从Java文件中提取导入语句行
     */
    private List<String> extractImportLines(Path file) {
        List<String> imports = new ArrayList<>();
        try (BufferedReader reader = Files.newBufferedReader(file)) {
            String line;
            while ((line = reader.readLine()) != null) {
                if (line.startsWith("import ")) {
                    imports.add(line);
                }
            }
        } catch (IOException e) {
            // 忽略读取错误
        }
        return imports;
    }

    /**
     * 从类内容中提取方法信息
     */
    private List<Method> extractMethods(List<String> content) {
        List<Method> methods = new ArrayList<>();
        Pattern methodPattern = Pattern.compile("^\\s*(public|private|protected)\\s+[\\w\\<\\>\\[\\]]+\\s+(\\w+)\\s*\\(");

        boolean inMethod = false;
        String currentMethod = "";
        int methodStartLine = 0;
        int methodEndLine = 0;
        int braceCount = 0;

        for (int i = 0; i < content.size(); i++) {
            String line = content.get(i);
            Matcher matcher = methodPattern.matcher(line);

            if (matcher.find()) {
                if (inMethod) {
                    // 结束前一个方法
                    int methodLength = methodEndLine - methodStartLine + 1;
                    methods.add(new Method(currentMethod, methodStartLine, methodEndLine, methodLength));
                }

                inMethod = true;
                currentMethod = matcher.group(2);
                methodStartLine = i + 1; // 行号从1开始
                braceCount = 0;
            }

            // 计算花括号
            if (inMethod) {
                for (char c : line.toCharArray()) {
                    if (c == '{') braceCount++;
                    if (c == '}') braceCount--;
                }

                // 如果花括号平衡为0，且至少出现过一对，则方法结束
                if (braceCount == 0 && line.contains("}")) {
                    methodEndLine = i + 1;
                }
            }
        }

        // 添加最后一个方法
        if (inMethod) {
            int methodLength = methodEndLine - methodStartLine + 1;
            methods.add(new Method(currentMethod, methodStartLine, methodEndLine, methodLength));
        }

        return methods;
    }

    /**
     * 清理锚点标识符
     */
    private String sanitizeAnchor(String input) {
        return input.replaceAll("[^a-zA-Z0-9\\-_]", "-").toLowerCase();
    }

    /**
     * 方法信息类
     */
    private static class Method {
        private final String name;
        private final int startLine;
        private final int endLine;
        private final int length;

        public Method(String name, int startLine, int endLine, int length) {
            this.name = name;
            this.startLine = startLine;
            this.endLine = endLine;
            this.length = length;
        }

        public String getName() {
            return name;
        }

        public int getStartLine() {
            return startLine;
        }

        public int getEndLine() {
            return endLine;
        }

        public int getLength() {
            return length;
        }
    }
}