package org.my.util;

import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 用于我方签约单位印章管理员数据同步SQL生成器
 * 基于已知的Excel数据生成INSERT语句
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since 2025-06-23
 */
public class ApplyContractUnitConfigSqlGenerator {

    /**
     * 生成用于我方签约单位印章管理员数据同步的INSERT语句
     */
    public static void generateApplyContractUnitConfigInserts() throws IOException {
        String outputPath = "E:\\output\\apply_contract_unit_config_insert.sql";
        String tableName = "pam_ctc.apply_contract_unit_config";
        
        // 基于Excel数据手动构建数据
        List<ApplyContractUnitConfigData> dataList = buildApplyContractUnitConfigData();
        
        // 生成SQL语句
        List<String> sqlStatements = generateInsertStatements(dataList, tableName);
        
        // 保存到文件
        saveSqlToFile(sqlStatements, outputPath);
        
        System.out.println("SQL文件已生成: " + outputPath);
        System.out.println("共生成 " + sqlStatements.size() + " 条INSERT语句");
    }

    /**
     * 构建用于我方签约单位印章管理员数据同步数据
     */
    private static List<ApplyContractUnitConfigData> buildApplyContractUnitConfigData() {
        List<ApplyContractUnitConfigData> dataList = new ArrayList<>();
        
        // 基于Excel数据添加所有记录
        dataList.add(new ApplyContractUnitConfigData(
            "20250623001", 
            "LE1537", 
            "上海瑞仕格物流科技有限公司", 
            "上海瑞仕格物流科技有限公司", 
            null, 
            null, 
            null, 
            null, 
            null, 
            "1", 
            null, 
            "0", 
            "-1", 
            "2025-06-23 00:00:00", 
            "-1", 
            "2025-06-23 00:00:00", 
            null, 
            null, 
            null
        ));
        
        dataList.add(new ApplyContractUnitConfigData(
            "20250623002", 
            "LE1342", 
            "上海瑞仕格科技有限公司", 
            "上海瑞仕格科技有限公司", 
            null, 
            null, 
            null, 
            null, 
            null, 
            "1", 
            null, 
            "0", 
            "-1", 
            "2025-06-23 00:00:00", 
            "-1", 
            "2025-06-23 00:00:00", 
            null, 
            null, 
            null
        ));
        
        return dataList;
    }

    /**
     * 生成INSERT语句列表
     */
    private static List<String> generateInsertStatements(List<ApplyContractUnitConfigData> dataList, String tableName) {
        List<String> sqlStatements = new ArrayList<>();
        
        String[] fields = {
            "id", "legal_person_number", "legal_name", "name_ch", "company_name_en", 
            "social_credit_code", "legal_user_name", "responsible_user_name", "executive_partner_name", 
            "is_open", "status", "deleted_flag", "create_by", "create_at", "update_by", 
            "update_at", "attribute1", "attribute2", "attribute3"
        };
        
        String fieldsPart = String.join(", ", fields);
        String insertPrefix = "INSERT INTO " + tableName + " (" + fieldsPart + ") VALUES ";
        
        for (ApplyContractUnitConfigData data : dataList) {
            StringBuilder valuesPart = new StringBuilder("(");
            
            valuesPart.append("'").append(data.id).append("', ");
            valuesPart.append("'").append(escapeSqlString(data.legalPersonNumber)).append("', ");
            valuesPart.append("'").append(escapeSqlString(data.legalName)).append("', ");
            valuesPart.append("'").append(escapeSqlString(data.nameCh)).append("', ");
            valuesPart.append(data.companyNameEn != null ? "'" + escapeSqlString(data.companyNameEn) + "'" : "NULL").append(", ");
            valuesPart.append(data.socialCreditCode != null ? "'" + escapeSqlString(data.socialCreditCode) + "'" : "NULL").append(", ");
            valuesPart.append(data.legalUserName != null ? "'" + escapeSqlString(data.legalUserName) + "'" : "NULL").append(", ");
            valuesPart.append(data.responsibleUserName != null ? "'" + escapeSqlString(data.responsibleUserName) + "'" : "NULL").append(", ");
            valuesPart.append(data.executivePartnerName != null ? "'" + escapeSqlString(data.executivePartnerName) + "'" : "NULL").append(", ");
            valuesPart.append("'").append(data.isOpen).append("', ");
            valuesPart.append(data.status != null ? data.status : "NULL").append(", ");
            valuesPart.append(data.deletedFlag).append(", ");
            valuesPart.append(data.createBy).append(", ");
            valuesPart.append(data.createAt != null ? "'" + data.createAt + "'" : "NULL").append(", ");
            valuesPart.append(data.updateBy).append(", ");
            valuesPart.append(data.updateAt != null ? "'" + data.updateAt + "'" : "NULL").append(", ");
            valuesPart.append(data.attribute1 != null ? "'" + escapeSqlString(data.attribute1) + "'" : "NULL").append(", ");
            valuesPart.append(data.attribute2 != null ? "'" + escapeSqlString(data.attribute2) + "'" : "NULL").append(", ");
            valuesPart.append(data.attribute3 != null ? "'" + escapeSqlString(data.attribute3) + "'" : "NULL");
            
            valuesPart.append(")");
            sqlStatements.add(insertPrefix + valuesPart.toString() + ";");
        }
        
        return sqlStatements;
    }

    /**
     * SQL字符串转义
     */
    private static String escapeSqlString(String value) {
        if (value == null) {
            return "";
        }
        return value
            .replace("\\", "\\\\")
            .replace("'", "''")
            .replace("\"", "\\\"")
            .replace("\n", "\\n")
            .replace("\r", "\\r")
            .replace("\t", "\\t");
    }

    /**
     * 将SQL语句保存到文件（无注释版本）
     */
    private static void saveSqlToFile(List<String> sqlStatements, String outputPath) throws IOException {
        String parentDir = Paths.get(outputPath).getParent().toString();
        Files.createDirectories(Paths.get(parentDir));
        
        try (OutputStreamWriter writer = new OutputStreamWriter(new FileOutputStream(outputPath), "UTF-8")) {
            for (String sql : sqlStatements) {
                writer.write(sql + "\n");
            }
            writer.flush();
        }
    }

    /**
     * 用于我方签约单位印章管理员数据同步数据类
     */
    static class ApplyContractUnitConfigData {
        String id;
        String legalPersonNumber;
        String legalName;
        String nameCh;
        String companyNameEn;
        String socialCreditCode;
        String legalUserName;
        String responsibleUserName;
        String executivePartnerName;
        String isOpen;
        String status;
        String deletedFlag;
        String createBy;
        String createAt;
        String updateBy;
        String updateAt;
        String attribute1;
        String attribute2;
        String attribute3;

        public ApplyContractUnitConfigData(String id, String legalPersonNumber, String legalName, String nameCh, 
                                         String companyNameEn, String socialCreditCode, String legalUserName, 
                                         String responsibleUserName, String executivePartnerName, String isOpen, 
                                         String status, String deletedFlag, String createBy, String createAt, 
                                         String updateBy, String updateAt, String attribute1, String attribute2, 
                                         String attribute3) {
            this.id = id;
            this.legalPersonNumber = legalPersonNumber;
            this.legalName = legalName;
            this.nameCh = nameCh;
            this.companyNameEn = companyNameEn;
            this.socialCreditCode = socialCreditCode;
            this.legalUserName = legalUserName;
            this.responsibleUserName = responsibleUserName;
            this.executivePartnerName = executivePartnerName;
            this.isOpen = isOpen;
            this.status = status;
            this.deletedFlag = deletedFlag;
            this.createBy = createBy;
            this.createAt = createAt;
            this.updateBy = updateBy;
            this.updateAt = updateAt;
            this.attribute1 = attribute1;
            this.attribute2 = attribute2;
            this.attribute3 = attribute3;
        }
    }

    /**
     * 主方法
     */
    public static void main(String[] args) {
        try {
            generateApplyContractUnitConfigInserts();
        } catch (Exception e) {
            System.err.println("生成SQL过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
