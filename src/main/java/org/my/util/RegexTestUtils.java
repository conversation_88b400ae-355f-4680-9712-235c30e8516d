package org.my.util;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 用于测试匹配4字节UTF-8字符（如Emoji）的正则表达式的工具类。
 * <p>
 * 这个版本使用了Unicode转义序列来定义Emoji，可以完全避免源文件编码问题。
 * </p>
 */
public class RegexTestUtils {

    private static final Pattern XML_INVALID_CHARS = Pattern.compile(
            "[\\u0000-\\u0008\\u000B\\u000C\\u000E-\\u001F\\u007F-\\u009F\\uFFFE\\uFFFF]"
    );

    public static void main(String[] args) {
        String testString = "测试特殊字符" + "\u0002";
        for (int i = 0; i < testString.length(); i++) {
            char c = testString.charAt(i);
            System.out.println("字符[" + i + "]: " + c + " Unicode: " + (int)c);
        }

        System.out.println("匹配结果: " + XML_INVALID_CHARS.matcher(testString).find());
    }
}