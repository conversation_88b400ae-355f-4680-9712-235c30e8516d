package org.my.util;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
/**
 * 时间序列ID生成器
 * 用于生成基于当前时间的连续ID序列
 */
public class TimeSeriesIdGenerator {

    // 定义日期时间格式化模式
    private static final String DATE_PATTERN = "yyyyMMddHHmmssSSS";
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern(DATE_PATTERN);

    /**
     * 生成指定数量的时间序列ID
     * @param count 需要生成的ID数量
     * @return 包含生成的ID的列表
     */
    public static List<String> generateIds(int count) {
        // 参数验证
        if (count < 0) {
            throw new IllegalArgumentException("生成数量不能为负数");
        }

        List<String> ids = new ArrayList<>(count);

        // 获取当前时间作为基准时间
        LocalDateTime now = LocalDateTime.now();

        // 生成指定数量的ID
        for (int i = 0; i < count; i++) {
            // 格式化时间为指定格式的字符串
            String id = now.format(formatter);
            ids.add(id);

            // 增加1毫秒，为下一个ID准备
            now = now.plusNanos(1_000_000); // 1毫秒 = 1,000,000纳秒
        }

        return ids;
    }

    /**
     * 主方法，用于演示和测试  ID: 20231025123456789
     */
    public static void main(String[] args) {
        // 示例：生成5个ID
        List<String> generatedIds = generateIds(12);

        // 打印生成的ID
        System.out.println("生成的ID列表：");
        for (int i = 0; i < generatedIds.size(); i++) {
          //  System.out.printf("第%d个ID: %s%n", i, generatedIds.get(i));
            System.out.println(generatedIds.get(i));
        }
    }
}
