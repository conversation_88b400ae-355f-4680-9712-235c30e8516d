package org.my.util;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * SQL语句验证测试类
 * 用于验证生成的SQL语句格式是否正确
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since 2025-06-18
 */
public class SqlValidatorTest {
    
    private static final Logger logger = LoggerFactory.getLogger(SqlValidatorTest.class);
    
    public static void main(String[] args) {
        logger.info("开始SQL语句格式验证测试...");
        
        try {
            // 测试material_custom_dict_header表的INSERT语句
            testHeaderTableInsert();
            
            // 测试material_custom_dict表的INSERT语句
            testDictTableInsert();
            
            // 测试特殊字符转义
            testSpecialCharacterEscaping();
            
            logger.info("SQL语句格式验证测试完成！");
            
        } catch (Exception e) {
            logger.error("测试过程中发生错误: {}", e.getMessage(), e);
        }
    }
    
    /**
     * 测试material_custom_dict_header表的INSERT语句
     */
    private static void testHeaderTableInsert() {
        logger.info("测试material_custom_dict_header表INSERT语句...");
        
        String sql = "INSERT INTO pam_ctc.material_custom_dict_header (" +
            "id, coding_class, coding_class_code, coding_middleclass, coding_middleclass_code, " +
            "coding_subclass, coding_subclass_code, organization_id, organization_name, remark, " +
            "create_by, create_name, create_at, update_by, update_name, update_at, deleted_flag" +
            ") VALUES (" +
            "'202506170001', '机械设计类标准物料', '01', '紧固件', '001', " +
            "'挡圈', '01', 101011, 'INV_MY4_上海瑞仕格科技有限公司', NULL, " +
            "-1, '初始化', NULL, NULL, NULL, NULL, 0" +
            ");";
        
        logger.info("生成的SQL语句: {}", sql);
        
        // 验证SQL语句基本格式
        if (sql.startsWith("INSERT INTO") && sql.endsWith(");")) {
            logger.info("✓ SQL语句格式正确");
        } else {
            logger.error("✗ SQL语句格式错误");
        }
        
        // 验证表名
        if (sql.contains("pam_ctc.material_custom_dict_header")) {
            logger.info("✓ 表名正确");
        } else {
            logger.error("✗ 表名错误");
        }
        
        // 验证字段数量
        String fieldsSection = sql.substring(sql.indexOf("(") + 1, sql.indexOf(") VALUES"));
        String[] fields = fieldsSection.split(",");
        if (fields.length == 17) {
            logger.info("✓ 字段数量正确: {}", fields.length);
        } else {
            logger.error("✗ 字段数量错误: {}", fields.length);
        }
    }
    
    /**
     * 测试material_custom_dict表的INSERT语句
     */
    private static void testDictTableInsert() {
        logger.info("测试material_custom_dict表INSERT语句...");
        
        String sql = "INSERT INTO pam_ctc.material_custom_dict (" +
            "id, header_id, code, name, value, order_num, description, " +
            "valid_at, expiry_at, create_by, create_at, update_by, update_at, deleted_flag" +
            ") VALUES (" +
            "'1202506170001', '202506170001', 'MA20250617001', '活动事项编码', '1.05', " +
            "1, NULL, '2022-07-05 17:54:00', NULL, -1, '2022-07-05 17:53:00', NULL, NULL, 0" +
            ");";
        
        logger.info("生成的SQL语句: {}", sql);
        
        // 验证SQL语句基本格式
        if (sql.startsWith("INSERT INTO") && sql.endsWith(");")) {
            logger.info("✓ SQL语句格式正确");
        } else {
            logger.error("✗ SQL语句格式错误");
        }
        
        // 验证表名
        if (sql.contains("pam_ctc.material_custom_dict")) {
            logger.info("✓ 表名正确");
        } else {
            logger.error("✗ 表名错误");
        }
        
        // 验证字段数量
        String fieldsSection = sql.substring(sql.indexOf("(") + 1, sql.indexOf(") VALUES"));
        String[] fields = fieldsSection.split(",");
        if (fields.length == 14) {
            logger.info("✓ 字段数量正确: {}", fields.length);
        } else {
            logger.error("✗ 字段数量错误: {}", fields.length);
        }
    }
    
    /**
     * 测试特殊字符转义
     */
    private static void testSpecialCharacterEscaping() {
        logger.info("测试特殊字符转义...");
        
        // 测试单引号转义
        String testValue1 = "O'Connor's";
        String escaped1 = escapeSqlString(testValue1);
        String expected1 = "O''Connor''s";
        if (escaped1.equals(expected1)) {
            logger.info("✓ 单引号转义正确: {} -> {}", testValue1, escaped1);
        } else {
            logger.error("✗ 单引号转义错误: {} -> {} (期望: {})", testValue1, escaped1, expected1);
        }
        
        // 测试反斜杠转义
        String testValue2 = "C:\\Program Files\\";
        String escaped2 = escapeSqlString(testValue2);
        String expected2 = "C:\\\\Program Files\\\\";
        if (escaped2.equals(expected2)) {
            logger.info("✓ 反斜杠转义正确: {} -> {}", testValue2, escaped2);
        } else {
            logger.error("✗ 反斜杠转义错误: {} -> {} (期望: {})", testValue2, escaped2, expected2);
        }
        
        // 测试换行符转义
        String testValue3 = "Line1\nLine2";
        String escaped3 = escapeSqlString(testValue3);
        String expected3 = "Line1\\nLine2";
        if (escaped3.equals(expected3)) {
            logger.info("✓ 换行符转义正确: {} -> {}", testValue3.replace("\n", "\\n"), escaped3);
        } else {
            logger.error("✗ 换行符转义错误: {} -> {} (期望: {})", testValue3.replace("\n", "\\n"), escaped3, expected3);
        }
    }
    
    /**
     * SQL字符串转义（复制自ExcelToSqlConverter）
     */
    private static String escapeSqlString(String value) {
        if (value == null) {
            return "";
        }
        
        // 基本的SQL转义
        String escaped = value
            .replace("\\", "\\\\")  // 反斜杠转义
            .replace("'", "''")     // 单引号转义
            .replace("\"", "\\\"")  // 双引号转义
            .replace("\n", "\\n")   // 换行符转义
            .replace("\r", "\\r")   // 回车符转义
            .replace("\t", "\\t");  // 制表符转义
        
        // 移除控制字符
        escaped = escaped.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", "");
        
        return escaped;
    }
}
