package org.my.util;

import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Stream;

/**
 * 文件内容收集工具类
 * 用于遍历指定路径下所有文件夹，收集所有Java、XML、YML文件的内容，
 * 并将它们写入到一个TXT文件中，每个文件内容前会插入文件的绝对路径。
 * 
 * <AUTHOR>
 * @since 2025-06-20
 */
public class FileContentCollector {

    /**
     * 支持的文件扩展名
     */
    private static final String[] SUPPORTED_EXTENSIONS = {".java", ".xml", ".yml", ".yaml"};

    /**
     * 收集指定路径下所有支持类型的文件内容到TXT文件中
     * 
     * @param sourcePath 要扫描的源路径
     * @param outputFilePath 输出TXT文件的路径
     * @throws IOException 如果读写文件时发生错误
     */
    public void collectFileContents(String sourcePath, String outputFilePath) throws IOException {
        collectFileContents(Paths.get(sourcePath), Paths.get(outputFilePath));
    }

    /**
     * 收集指定路径下所有支持类型的文件内容到TXT文件中
     * 
     * @param sourcePath 要扫描的源路径
     * @param outputFilePath 输出TXT文件的路径
     * @throws IOException 如果读写文件时发生错误
     */
    public void collectFileContents(Path sourcePath, Path outputFilePath) throws IOException {
        // 验证源路径
        if (!Files.exists(sourcePath)) {
            throw new IllegalArgumentException("源路径不存在: " + sourcePath.toAbsolutePath());
        }
        if (!Files.isDirectory(sourcePath)) {
            throw new IllegalArgumentException("源路径不是目录: " + sourcePath.toAbsolutePath());
        }

        System.out.println("开始扫描目录: " + sourcePath.toAbsolutePath());
        System.out.println("输出文件: " + outputFilePath.toAbsolutePath());
        System.out.println("支持的文件类型: " + String.join(", ", SUPPORTED_EXTENSIONS));
        System.out.println("---");

        // 收集所有符合条件的文件
        List<Path> targetFiles = new ArrayList<>();
        try (Stream<Path> paths = Files.walk(sourcePath)) {
            paths.filter(Files::isRegularFile)
                 .filter(this::isSupportedFile)
                 .forEach(targetFiles::add);
        }

        System.out.println("找到 " + targetFiles.size() + " 个符合条件的文件");

        // 写入输出文件
        try (BufferedWriter writer = Files.newBufferedWriter(outputFilePath, StandardCharsets.UTF_8,
                StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING)) {
            
            // 写入文件头信息
            writeFileHeader(writer, sourcePath, targetFiles.size());
            
            // 处理每个文件
            int processedCount = 0;
            for (Path file : targetFiles) {
                processedCount++;
                writeFileContent(writer, file, processedCount, targetFiles.size());
            }
            
            // 写入文件尾信息
            writeFileFooter(writer, processedCount);
        }

        System.out.println("收集完成！共处理 " + targetFiles.size() + " 个文件");
        System.out.println("结果已保存到: " + outputFilePath.toAbsolutePath());
    }

    /**
     * 检查文件是否为支持的类型
     * 
     * @param file 文件路径
     * @return 如果是支持的文件类型返回true，否则返回false
     */
    private boolean isSupportedFile(Path file) {
        String fileName = file.getFileName().toString().toLowerCase();
        for (String extension : SUPPORTED_EXTENSIONS) {
            if (fileName.endsWith(extension)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 写入文件头信息
     * 
     * @param writer 输出流
     * @param sourcePath 源路径
     * @param totalFiles 总文件数
     * @throws IOException 写入异常
     */
    private void writeFileHeader(BufferedWriter writer, Path sourcePath, int totalFiles) throws IOException {
        writer.write("================================================================================");
        writer.newLine();
        writer.write("                          文件内容收集报告");
        writer.newLine();
        writer.write("================================================================================");
        writer.newLine();
        writer.write("扫描路径: " + sourcePath.toAbsolutePath());
        writer.newLine();
        writer.write("生成时间: " + java.time.LocalDateTime.now());
        writer.newLine();
        writer.write("文件总数: " + totalFiles);
        writer.newLine();
        writer.write("支持类型: " + String.join(", ", SUPPORTED_EXTENSIONS));
        writer.newLine();
        writer.write("================================================================================");
        writer.newLine();
        writer.newLine();
    }

    /**
     * 写入单个文件的内容
     * 
     * @param writer 输出流
     * @param file 文件路径
     * @param currentIndex 当前文件索引
     * @param totalFiles 总文件数
     * @throws IOException 写入异常
     */
    private void writeFileContent(BufferedWriter writer, Path file, int currentIndex, int totalFiles) throws IOException {
        // 写入文件分隔符和路径信息
        writer.write("################################################################################");
        writer.newLine();
        writer.write("# 文件 " + currentIndex + "/" + totalFiles);
        writer.newLine();
        writer.write("# 绝对路径: " + file.toAbsolutePath());
        writer.newLine();
        writer.write("# 文件大小: " + getFileSize(file));
        writer.newLine();
        writer.write("################################################################################");
        writer.newLine();
        writer.newLine();

        try {
            // 读取并写入文件内容
            String content = new String(Files.readAllBytes(file), StandardCharsets.UTF_8);
            writer.write(content);
            
            // 确保文件内容后有换行
            if (!content.endsWith("\n") && !content.endsWith("\r\n")) {
                writer.newLine();
            }
            
            System.out.println("已处理 [" + currentIndex + "/" + totalFiles + "]: " + file.getFileName());
            
        } catch (IOException e) {
            String errorMessage = "!!! 读取文件失败: " + e.getMessage();
            writer.write(errorMessage);
            writer.newLine();
            System.err.println("读取文件失败: " + file.toAbsolutePath() + " - " + e.getMessage());
        }

        writer.newLine();
        writer.write("################################################################################");
        writer.newLine();
        writer.write("# 文件结束: " + file.getFileName());
        writer.newLine();
        writer.write("################################################################################");
        writer.newLine();
        writer.newLine();
    }

    /**
     * 获取文件大小的友好显示
     * 
     * @param file 文件路径
     * @return 文件大小字符串
     */
    private String getFileSize(Path file) {
        try {
            long bytes = Files.size(file);
            if (bytes < 1024) {
                return bytes + " bytes";
            } else if (bytes < 1024 * 1024) {
                return String.format("%.1f KB", bytes / 1024.0);
            } else {
                return String.format("%.1f MB", bytes / (1024.0 * 1024.0));
            }
        } catch (IOException e) {
            return "未知大小";
        }
    }

    /**
     * 写入文件尾信息
     * 
     * @param writer 输出流
     * @param processedCount 处理的文件数量
     * @throws IOException 写入异常
     */
    private void writeFileFooter(BufferedWriter writer, int processedCount) throws IOException {
        writer.write("================================================================================");
        writer.newLine();
        writer.write("                              收集完成");
        writer.newLine();
        writer.write("================================================================================");
        writer.newLine();
        writer.write("总共处理文件数: " + processedCount);
        writer.newLine();
        writer.write("完成时间: " + java.time.LocalDateTime.now());
        writer.newLine();
        writer.write("================================================================================");
        writer.newLine();
    }

    /**
     * 主方法 - 用于测试和直接运行
     * 
     * @param args 命令行参数
     */
    public static void main(String[] args) {
        // ==================================================================
        // ===                      用户配置区域                           ===
        // ===   请在下方修改为您本地的实际路径                             ===
        // ==================================================================

        // 1. 请将这里替换为您要扫描的目录的绝对路径
        //    示例 (Windows): "C:\\Users\\<USER>\\Projects\\MyProject\\src"
        //    示例 (Linux/macOS): "/home/<USER>/projects/my-project/src"
        String sourceDirectoryPath = "E:\\project\\pam\\ctc";

        // 2. 输出文件路径 (可以是相对路径或绝对路径)
        String outputFilePath = "e:\\collected_files_content.txt";

        // ==================================================================
        // ===                      配置结束                              ===
        // ==================================================================

        try {
            // 检查用户是否已修改默认路径
            if (sourceDirectoryPath.contains("C:\\path\\to\\your\\") || sourceDirectoryPath.contains("/path/to/your/")) {
                System.err.println("错误：请先修改 main 方法中的 'sourceDirectoryPath' 变量，指向您要扫描的实际目录路径。");
                return;
            }

            FileContentCollector collector = new FileContentCollector();
            collector.collectFileContents(sourceDirectoryPath, outputFilePath);

        } catch (Exception e) {
            System.err.println("处理文件时发生错误：");
            e.printStackTrace();
        }
    }
}
