package org.my.util;

import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 项目终止检查条件配置SQL生成器
 * 基于已知的Excel数据生成INSERT语句
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since 2025-06-23
 */
public class ProjectTerminationCheckSqlGenerator {

    /**
     * 生成项目终止检查条件配置的INSERT语句
     */
    public static void generateProjectTerminationCheckInserts() throws IOException {
        String outputPath = "E:\\output\\project_termination_check_rel_insert.sql";
        String tableName = "pam_ctc.project_termination_check_rel";
        
        // 基于Excel数据手动构建数据
        List<ProjectTerminationCheckData> dataList = buildProjectTerminationCheckData();
        
        // 生成SQL语句
        List<String> sqlStatements = generateInsertStatements(dataList, tableName);
        
        // 保存到文件
        saveSqlToFile(sqlStatements, outputPath);
        
        System.out.println("SQL文件已生成: " + outputPath);
        System.out.println("共生成 " + sqlStatements.size() + " 条INSERT语句");
    }

    /**
     * 构建项目终止检查条件数据
     */
    private static List<ProjectTerminationCheckData> buildProjectTerminationCheckData() {
        List<ProjectTerminationCheckData> dataList = new ArrayList<>();
        
        // 基于Excel数据添加所有记录
        dataList.add(new ProjectTerminationCheckData("20250621001", "1384556177898926080", "202506210002", "649304731511947264", "1", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621002", "1384556177898926080", "202506210002", "649304887489724416", "1", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621003", "1384556177898926080", "202506210002", "649304993630781440", "2", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621004", "1384556177898926080", "202506210002", "649305101520863232", "2", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621005", "1384556177898926080", "202506210002", "649309165117767680", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621006", "1384556177898926080", "202506210002", "649305311974260736", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621007", "1384556177898926080", "202506210002", "649305433227395072", "1", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621008", "1384556177898926080", "202506210002", "649306554524237824", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621009", "1384556177898926080", "202506210002", "649306782040064000", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621010", "1384556177898926080", "202506210002", "649309282017214464", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621011", "1384556177898926080", "202506210002", "649307029759852544", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621012", "1384556177898926080", "202506210002", "649307151956705280", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621013", "1384556177898926080", "202506210002", "649307259511242752", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621014", "1384556177898926080", "202506210002", "649307377778032640", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621015", "1384556177898926080", "202506210002", "649307472296673280", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621016", "1384556177898926080", "202506210002", "657372972981420032", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621017", "1384556177898926080", "202506210002", "721027090643681280", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621018", "1384556177898926080", "202506210002", "721027239528890368", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621019", "1384556177898926080", "202506210002", "721027446660399104", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621020", "1384556177898926080", "202506210002", "750676257166852096", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621021", "1384556177898926080", "202506210002", "750676306663833600", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621022", "1384556177898926080", "202506210002", "750676729500008448", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        
        // 202506210001 类型的数据
        dataList.add(new ProjectTerminationCheckData("20250621023", "1384556177898926080", "202506210001", "649305101520863232", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621024", "1384556177898926080", "202506210001", "649309165117767680", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621025", "1384556177898926080", "202506210001", "649305311974260736", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621026", "1384556177898926080", "202506210001", "649305433227395072", "1", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621027", "1384556177898926080", "202506210001", "649306554524237824", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621028", "1384556177898926080", "202506210001", "649306782040064000", "1", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621029", "1384556177898926080", "202506210001", "649309282017214464", "1", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621030", "1384556177898926080", "202506210001", "649307029759852544", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621031", "1384556177898926080", "202506210001", "649307151956705280", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621032", "1384556177898926080", "202506210001", "649307259511242752", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621033", "1384556177898926080", "202506210001", "649307377778032640", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621034", "1384556177898926080", "202506210001", "649307472296673280", "1", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621035", "1384556177898926080", "202506210001", "657372972981420032", "1", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621036", "1384556177898926080", "202506210001", "721027090643681280", "1", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621037", "1384556177898926080", "202506210001", "721027239528890368", "1", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621038", "1384556177898926080", "202506210001", "721027446660399104", "0", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621039", "1384556177898926080", "202506210001", "750676257166852096", "1", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621040", "1384556177898926080", "202506210001", "750676306663833600", "1", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621041", "1384556177898926080", "202506210001", "750676729500008448", "1", "-1", "2023-10-27 20:27:23", null, null, null));
        dataList.add(new ProjectTerminationCheckData("20250621042", "1384556177898926080", "202506210001", "750678571709628416", "2", "-1", "2023-10-27 20:27:23", null, null, null));
        
        return dataList;
    }

    /**
     * 生成INSERT语句列表
     */
    private static List<String> generateInsertStatements(List<ProjectTerminationCheckData> dataList, String tableName) {
        List<String> sqlStatements = new ArrayList<>();
        
        String[] fields = {
            "id", "unit_id", "termination_type_id", "check_item_id", "yes_or_no", 
            "create_by", "create_at", "update_by", "update_at", "version"
        };
        
        String fieldsPart = String.join(", ", fields);
        String insertPrefix = "INSERT INTO " + tableName + " (" + fieldsPart + ") VALUES ";
        
        for (ProjectTerminationCheckData data : dataList) {
            StringBuilder valuesPart = new StringBuilder("(");
            
            valuesPart.append("'").append(data.id).append("', ");
            valuesPart.append(data.unitId).append(", ");
            valuesPart.append("'").append(data.terminationTypeId).append("', ");
            valuesPart.append(data.checkItemId).append(", ");
            valuesPart.append(data.yesOrNo).append(", ");
            valuesPart.append(data.createBy).append(", ");
            valuesPart.append(data.createAt != null ? "'" + data.createAt + "'" : "NULL").append(", ");
            valuesPart.append(data.updateBy != null ? data.updateBy : "NULL").append(", ");
            valuesPart.append(data.updateAt != null ? "'" + data.updateAt + "'" : "NULL").append(", ");
            valuesPart.append(data.version != null ? data.version : "NULL");
            
            valuesPart.append(")");
            sqlStatements.add(insertPrefix + valuesPart.toString() + ";");
        }
        
        return sqlStatements;
    }

    /**
     * 将SQL语句保存到文件（无注释版本）
     */
    private static void saveSqlToFile(List<String> sqlStatements, String outputPath) throws IOException {
        String parentDir = Paths.get(outputPath).getParent().toString();
        Files.createDirectories(Paths.get(parentDir));
        
        try (FileWriter writer = new FileWriter(outputPath, false)) {
            for (String sql : sqlStatements) {
                writer.write(sql + "\n");
            }
            writer.flush();
        }
    }

    /**
     * 项目终止检查条件数据类
     */
    static class ProjectTerminationCheckData {
        String id;
        String unitId;
        String terminationTypeId;
        String checkItemId;
        String yesOrNo;
        String createBy;
        String createAt;
        String updateBy;
        String updateAt;
        String version;

        public ProjectTerminationCheckData(String id, String unitId, String terminationTypeId, String checkItemId, 
                                         String yesOrNo, String createBy, String createAt, String updateBy, 
                                         String updateAt, String version) {
            this.id = id;
            this.unitId = unitId;
            this.terminationTypeId = terminationTypeId;
            this.checkItemId = checkItemId;
            this.yesOrNo = yesOrNo;
            this.createBy = createBy;
            this.createAt = createAt;
            this.updateBy = updateBy;
            this.updateAt = updateAt;
            this.version = version;
        }
    }

    /**
     * 主方法
     */
    public static void main(String[] args) {
        try {
            generateProjectTerminationCheckInserts();
        } catch (Exception e) {
            System.err.println("生成SQL过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
