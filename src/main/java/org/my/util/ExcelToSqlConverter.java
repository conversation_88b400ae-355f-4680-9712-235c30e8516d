package org.my.util;

import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.*;
import java.nio.charset.StandardCharsets;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Excel文件转SQL INSERT语句工具类
 * 用于将Excel文件中的数据转换为PAM项目数据库的INSERT语句
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since 2025-06-18
 */
public class ExcelToSqlConverter {
    
    private static final Logger logger = LoggerFactory.getLogger(ExcelToSqlConverter.class);
    
    // Excel文件路径
    private static final String EXCEL_FILE_PATH = "D:\\Users\\ex_wuyh42.CN\\Desktop\\文件\\WDS环境搭建-配置\\物料类别属性配置.xlsx";
    
    // SQL文件输出目录
    private static final String SQL_OUTPUT_DIR = "e:\\sql";
    
    // 日期格式化器
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    
    // ID生成器
    private static final AtomicInteger counter = new AtomicInteger(0);
    private static String lastGeneratedId = "";
    private static final Object lock = new Object();
    
    // Sheet与表的映射关系
    private static final Map<String, String> SHEET_TABLE_MAPPING = new HashMap<>();
    static {
        SHEET_TABLE_MAPPING.put("material_custom_dict_header-MY4", "pam_ctc.material_custom_dict_header");
        SHEET_TABLE_MAPPING.put("material_custom_dict-MY4", "pam_ctc.material_custom_dict");
        SHEET_TABLE_MAPPING.put("material_custom_dict_header-MY5", "pam_ctc.material_custom_dict_header");
        SHEET_TABLE_MAPPING.put("material_custom_dict-MY5", "pam_ctc.material_custom_dict");
    }
    
    // material_custom_dict_header表字段
    private static final String[] HEADER_COLUMNS = {
        "id", "coding_class", "coding_class_code", "coding_middleclass", "coding_middleclass_code",
        "coding_subclass", "coding_subclass_code", "organization_id", "organization_name", "remark",
        "create_by", "create_name", "create_at", "update_by", "update_name", "update_at", "deleted_flag"
    };
    
    // material_custom_dict表字段
    private static final String[] DICT_COLUMNS = {
        "id", "header_id", "code", "name", "value", "order_num", "description",
        "valid_at", "expiry_at", "create_by", "create_at", "update_by", "update_at", "deleted_flag"
    };
    
    public static void main(String[] args) {
        logger.info("开始执行Excel转SQL工具...");
        logger.info("Excel文件路径: {}", EXCEL_FILE_PATH);
        logger.info("SQL输出目录: {}", SQL_OUTPUT_DIR);

        try {
            // 验证Excel文件是否存在
            validateExcelFile();

            // 创建输出目录
            createOutputDirectory();

            // 处理Excel文件
            processExcelFile();

            logger.info("Excel转SQL工具执行完成！");
            System.out.println("转换完成！请查看输出目录: " + SQL_OUTPUT_DIR);

        } catch (Exception e) {
            logger.error("执行过程中发生错误: {}", e.getMessage(), e);
            System.err.println("执行失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 验证Excel文件是否存在
     */
    private static void validateExcelFile() throws FileNotFoundException {
        File excelFile = new File(EXCEL_FILE_PATH);
        if (!excelFile.exists()) {
            throw new FileNotFoundException("Excel文件不存在: " + EXCEL_FILE_PATH);
        }
        if (!excelFile.canRead()) {
            throw new IllegalStateException("Excel文件无法读取: " + EXCEL_FILE_PATH);
        }
        logger.info("Excel文件验证通过: {}", EXCEL_FILE_PATH);
    }
    
    /**
     * 创建输出目录
     */
    private static void createOutputDirectory() {
        File outputDir = new File(SQL_OUTPUT_DIR);
        if (!outputDir.exists()) {
            boolean created = outputDir.mkdirs();
            if (created) {
                logger.info("创建输出目录: {}", SQL_OUTPUT_DIR);
            } else {
                logger.warn("输出目录创建失败: {}", SQL_OUTPUT_DIR);
            }
        }
    }
    
    /**
     * 处理Excel文件
     */
    private static void processExcelFile() throws IOException {
        logger.info("开始读取Excel文件: {}", EXCEL_FILE_PATH);
        
        try (FileInputStream fis = new FileInputStream(EXCEL_FILE_PATH);
             XSSFWorkbook workbook = new XSSFWorkbook(fis)) {
            
            // 遍历所有需要处理的sheet
            for (String sheetName : SHEET_TABLE_MAPPING.keySet()) {
                Sheet sheet = workbook.getSheet(sheetName);
                if (sheet != null) {
                    logger.info("处理工作表: {}", sheetName);
                    processSheet(sheet, sheetName);
                } else {
                    logger.warn("未找到工作表: {}", sheetName);
                }
            }
        }
    }
    
    /**
     * 处理单个工作表
     */
    private static void processSheet(Sheet sheet, String sheetName) throws IOException {
        String tableName = SHEET_TABLE_MAPPING.get(sheetName);
        String[] columns = getColumnsForTable(tableName);

        logger.info("开始处理工作表 {} -> 表 {}", sheetName, tableName);
        logger.info("表字段数量: {}", columns.length);

        List<String> sqlStatements = new ArrayList<>();
        Iterator<Row> rowIterator = sheet.iterator();

        // 验证并跳过标题行
        if (rowIterator.hasNext()) {
            Row headerRow = rowIterator.next();
            validateHeaderRow(headerRow, columns, sheetName);
            logger.debug("跳过标题行");
        }

        int rowCount = 0;
        int errorCount = 0;
        int totalRows = sheet.getLastRowNum();

        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();

            // 检查行是否为空
            if (isRowEmpty(row)) {
                logger.debug("跳过空行: {}", row.getRowNum() + 1);
                continue;
            }

            try {
                String sql = generateInsertStatement(row, tableName, columns);
                if (sql != null && !sql.trim().isEmpty()) {
                    sqlStatements.add(sql);
                    rowCount++;

                    // 每处理100行输出一次进度
                    if (rowCount % 100 == 0) {
                        logger.info("已处理 {} 行数据...", rowCount);
                    }
                }
            } catch (Exception e) {
                errorCount++;
                logger.error("处理第{}行数据时发生错误: {}", row.getRowNum() + 1, e.getMessage());

                // 如果错误太多，停止处理
                if (errorCount > 50) {
                    logger.error("错误数量过多，停止处理工作表: {}", sheetName);
                    break;
                }
            }
        }

        // 写入SQL文件
        if (!sqlStatements.isEmpty()) {
            writeSqlFile(sheetName, sqlStatements);
            logger.info("工作表 {} 处理完成，共生成 {} 条SQL语句，错误 {} 条", sheetName, rowCount, errorCount);
        } else {
            logger.warn("工作表 {} 没有生成任何SQL语句", sheetName);
        }
    }

    /**
     * 验证标题行
     */
    private static void validateHeaderRow(Row headerRow, String[] expectedColumns, String sheetName) {
        if (headerRow == null) {
            logger.warn("工作表 {} 的标题行为空", sheetName);
            return;
        }

        int actualColumnCount = headerRow.getLastCellNum();
        logger.debug("工作表 {} 实际列数: {}, 期望列数: {}", sheetName, actualColumnCount, expectedColumns.length);

        // 检查列数是否匹配
        if (actualColumnCount < expectedColumns.length) {
            logger.warn("工作表 {} 的列数不足，实际: {}, 期望: {}", sheetName, actualColumnCount, expectedColumns.length);
        }

        // 验证前几个关键列的标题
        for (int i = 0; i < Math.min(5, expectedColumns.length) && i < actualColumnCount; i++) {
            Cell cell = headerRow.getCell(i);
            String actualHeader = getCellValueAsString(cell);
            String expectedHeader = expectedColumns[i];

            if (actualHeader != null && !actualHeader.trim().equals(expectedHeader)) {
                logger.warn("工作表 {} 第{}列标题不匹配，实际: '{}', 期望: '{}'",
                    sheetName, i + 1, actualHeader.trim(), expectedHeader);
            }
        }
    }
    
    /**
     * 根据表名获取对应的字段数组
     */
    private static String[] getColumnsForTable(String tableName) {
        if (tableName.contains("material_custom_dict_header")) {
            return HEADER_COLUMNS;
        } else if (tableName.contains("material_custom_dict")) {
            return DICT_COLUMNS;
        } else {
            throw new IllegalArgumentException("未知的表名: " + tableName);
        }
    }
    
    /**
     * 检查行是否为空
     */
    private static boolean isRowEmpty(Row row) {
        if (row == null) {
            return true;
        }
        
        for (int i = 0; i < row.getLastCellNum(); i++) {
            Cell cell = row.getCell(i);
            if (cell != null && cell.getCellType() != CellType.BLANK) {
                String cellValue = getCellValueAsString(cell);
                if (cellValue != null && !cellValue.trim().isEmpty() && !"[NULL]".equals(cellValue.trim())) {
                    return false;
                }
            }
        }
        return true;
    }
    
    /**
     * 生成INSERT语句
     */
    private static String generateInsertStatement(Row row, String tableName, String[] columns) {
        StringBuilder sql = new StringBuilder();
        sql.append("INSERT INTO ").append(tableName).append(" (");

        // 添加字段名
        for (int i = 0; i < columns.length; i++) {
            if (i > 0) sql.append(", ");
            sql.append(columns[i]);
        }
        sql.append(") VALUES (");

        // 添加值
        List<String> values = new ArrayList<>();
        for (int i = 0; i < columns.length; i++) {
            Cell cell = row.getCell(i);
            String value = formatCellValue(cell, columns[i]);
            values.add(value);
        }

        // 验证必填字段
        if (!validateRequiredFields(values, columns, row.getRowNum() + 1)) {
            logger.warn("第{}行数据验证失败，跳过生成SQL", row.getRowNum() + 1);
            return null;
        }

        // 拼接值
        for (int i = 0; i < values.size(); i++) {
            if (i > 0) sql.append(", ");
            sql.append(values.get(i));
        }

        sql.append(");");

        // 验证生成的SQL语句长度
        String sqlString = sql.toString();
        if (sqlString.length() > 10000) {
            logger.warn("第{}行生成的SQL语句过长({} 字符)，可能存在问题", row.getRowNum() + 1, sqlString.length());
        }

        return sqlString;
    }

    /**
     * 验证必填字段
     */
    private static boolean validateRequiredFields(List<String> values, String[] columns, int rowNum) {
        for (int i = 0; i < columns.length && i < values.size(); i++) {
            String column = columns[i];
            String value = values.get(i);

            // 检查必填字段
            if (isRequiredField(column) && ("NULL".equals(value) || value == null || value.trim().isEmpty())) {
                logger.error("第{}行必填字段 {} 为空", rowNum, column);
                return false;
            }
        }
        return true;
    }

    /**
     * 判断是否为必填字段
     */
    private static boolean isRequiredField(String columnName) {
        return "id".equals(columnName) || "organization_id".equals(columnName);
    }
    
    /**
     * 格式化单元格值为SQL格式
     */
    private static String formatCellValue(Cell cell, String columnName) {
        String cellValue = getCellValueAsString(cell);

        // 处理NULL值
        if (cellValue == null || cellValue.trim().isEmpty() || "[NULL]".equals(cellValue.trim())) {
            // 对于必填字段，生成默认值
            if ("id".equals(columnName)) {
                return "'" + generateId() + "'";
            } else if ("organization_id".equals(columnName)) {
                return "101011"; // 默认组织ID
            } else if ("deleted_flag".equals(columnName)) {
                return "0"; // 默认未删除
            }
            return "NULL";
        }

        cellValue = cellValue.trim();

        // 处理ID字段
        if ("id".equals(columnName)) {
            return "'" + escapeSqlString(cellValue) + "'";
        }

        // 处理数值字段
        if (isNumericColumn(columnName)) {
            return formatNumericValue(cellValue, columnName);
        }

        // 处理日期字段
        if (isDateColumn(columnName)) {
            return formatDateValue(cellValue);
        }

        // 处理字符串字段，限制长度
        String escapedValue = escapeSqlString(cellValue);
        if (escapedValue.length() > 500) {
            logger.warn("字段 {} 的值过长，截断到500字符: {}", columnName, escapedValue.substring(0, 50) + "...");
            escapedValue = escapedValue.substring(0, 500);
        }

        return "'" + escapedValue + "'";
    }

    /**
     * 格式化数值
     */
    private static String formatNumericValue(String value, String columnName) {
        try {
            // 处理小数
            if (value.contains(".")) {
                double doubleValue = Double.parseDouble(value);
                // 对于某些字段，保留小数
                if ("value".equals(columnName)) {
                    return String.valueOf(doubleValue);
                } else {
                    // 其他数值字段转为整数
                    return String.valueOf((long) doubleValue);
                }
            } else {
                // 整数处理
                long longValue = Long.parseLong(value);
                return String.valueOf(longValue);
            }
        } catch (NumberFormatException e) {
            logger.warn("无法解析数值: {} (字段: {})", value, columnName);
            // 返回默认值
            if ("organization_id".equals(columnName)) {
                return "101011";
            } else if ("deleted_flag".equals(columnName)) {
                return "0";
            } else if ("order_num".equals(columnName)) {
                return "1";
            }
            return "NULL";
        }
    }

    /**
     * 格式化日期值
     */
    private static String formatDateValue(String value) {
        // 标准日期时间格式
        if (value.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}")) {
            return "'" + value + "'";
        }

        // 只有日期的格式
        if (value.matches("\\d{4}-\\d{2}-\\d{2}")) {
            return "'" + value + " 00:00:00'";
        }

        // 其他格式尝试解析
        try {
            // 可以在这里添加更多日期格式的解析
            logger.debug("无法识别的日期格式: {}", value);
        } catch (Exception e) {
            logger.warn("日期解析失败: {}", value);
        }

        return "NULL";
    }
    
    /**
     * 判断是否为数值字段
     */
    private static boolean isNumericColumn(String columnName) {
        return "organization_id".equals(columnName) || 
               "create_by".equals(columnName) || 
               "update_by".equals(columnName) || 
               "deleted_flag".equals(columnName) ||
               "header_id".equals(columnName) ||
               "order_num".equals(columnName);
    }
    
    /**
     * 判断是否为日期字段
     */
    private static boolean isDateColumn(String columnName) {
        return "create_at".equals(columnName) || 
               "update_at".equals(columnName) ||
               "valid_at".equals(columnName) ||
               "expiry_at".equals(columnName);
    }
    
    /**
     * 获取单元格值作为字符串
     */
    private static String getCellValueAsString(Cell cell) {
        if (cell == null) {
            return null;
        }
        
        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    Date date = cell.getDateCellValue();
                    return DATE_FORMAT.format(date);
                } else {
                    double numericValue = cell.getNumericCellValue();
                    if (numericValue == Math.floor(numericValue)) {
                        return String.valueOf((long) numericValue);
                    } else {
                        return String.valueOf(numericValue);
                    }
                }
            case BOOLEAN:
                return String.valueOf(cell.getBooleanCellValue());
            case FORMULA:
                try {
                    return cell.getStringCellValue();
                } catch (Exception e) {
                    return String.valueOf(cell.getNumericCellValue());
                }
            case BLANK:
            case _NONE:
            default:
                return null;
        }
    }
    
    /**
     * SQL字符串转义
     */
    private static String escapeSqlString(String value) {
        if (value == null) {
            return "";
        }

        // 基本的SQL转义
        String escaped = value
            .replace("\\", "\\\\")  // 反斜杠转义
            .replace("'", "''")     // 单引号转义
            .replace("\"", "\\\"")  // 双引号转义
            .replace("\n", "\\n")   // 换行符转义
            .replace("\r", "\\r")   // 回车符转义
            .replace("\t", "\\t");  // 制表符转义

        // 移除控制字符
        escaped = escaped.replaceAll("[\\x00-\\x08\\x0B\\x0C\\x0E-\\x1F\\x7F]", "");

        return escaped;
    }
    
    /**
     * 生成唯一ID
     */
    private static String generateId() {
        synchronized (lock) {
            String newId;
            do {
                LocalDateTime now = LocalDateTime.now();
                String timeStr = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
                int count = counter.getAndUpdate(n -> (n + 1) % 1000);
                newId = timeStr + String.format("%03d", count);
            } while (newId.equals(lastGeneratedId));
            
            lastGeneratedId = newId;
            return newId;
        }
    }
    
    /**
     * 写入SQL文件
     */
    private static void writeSqlFile(String sheetName, List<String> sqlStatements) throws IOException {
        String fileName = sheetName + ".sql";
        File sqlFile = new File(SQL_OUTPUT_DIR, fileName);

        logger.info("写入SQL文件: {}", sqlFile.getAbsolutePath());

        try (BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream(sqlFile), StandardCharsets.UTF_8))) {

            // 写入文件头注释
            writer.write("-- =====================================================");
            writer.newLine();
            writer.write("-- SQL INSERT statements generated from Excel sheet");
            writer.newLine();
            writer.write("-- =====================================================");
            writer.newLine();
            writer.write("-- Source sheet: " + sheetName);
            writer.newLine();
            writer.write("-- Target table: " + SHEET_TABLE_MAPPING.get(sheetName));
            writer.newLine();
            writer.write("-- Generated at: " + LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
            writer.newLine();
            writer.write("-- Total statements: " + sqlStatements.size());
            writer.newLine();
            writer.write("-- Generated by: ExcelToSqlConverter v1.0");
            writer.newLine();
            writer.write("-- =====================================================");
            writer.newLine();
            writer.newLine();

            // 添加事务控制
            writer.write("-- 开始事务");
            writer.newLine();
            writer.write("START TRANSACTION;");
            writer.newLine();
            writer.newLine();

            // 写入SQL语句
            int batchSize = 100;
            for (int i = 0; i < sqlStatements.size(); i++) {
                // 每100条语句添加一个注释
                if (i % batchSize == 0) {
                    writer.write("-- Batch " + (i / batchSize + 1) + " (statements " + (i + 1) + " to " +
                        Math.min(i + batchSize, sqlStatements.size()) + ")");
                    writer.newLine();
                }

                writer.write(sqlStatements.get(i));
                writer.newLine();

                // 每100条语句后添加空行
                if ((i + 1) % batchSize == 0 && i < sqlStatements.size() - 1) {
                    writer.newLine();
                }
            }

            writer.newLine();
            writer.write("-- 提交事务");
            writer.newLine();
            writer.write("COMMIT;");
            writer.newLine();
            writer.newLine();
            writer.write("-- 文件结束");
            writer.newLine();

            writer.flush();
        }

        // 验证文件是否写入成功
        if (sqlFile.exists() && sqlFile.length() > 0) {
            logger.info("SQL文件写入完成: {} (共{}条语句，文件大小: {} KB)",
                fileName, sqlStatements.size(), sqlFile.length() / 1024);
        } else {
            logger.error("SQL文件写入失败: {}", fileName);
            throw new IOException("SQL文件写入失败: " + fileName);
        }
    }
}
