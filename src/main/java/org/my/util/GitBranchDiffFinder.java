package org.my.util;
import java.io.BufferedReader;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.logging.ConsoleHandler;
import java.util.logging.FileHandler;
import java.util.logging.Level;
import java.util.logging.Logger;
import java.util.logging.SimpleFormatter;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * GitBranchDiffFinder - 查找与参考分支存在差异的Git分支
 *
 * 该工具用于识别与参考分支(默认prod)存在差异且匹配指定关键字的Git分支。
 * 支持远程分支获取和比较。
 */
public class GitBranchDiffFinder {
    private static final Logger LOGGER = Logger.getLogger(GitBranchDiffFinder.class.getName());

    // 默认配置
    private static final String DEFAULT_REPO_PATH = "E:\\project\\pam";
    private static final List<String> DEFAULT_REFERENCE_BRANCHES = Collections.singletonList("release/prod");
    private static final List<String> DEFAULT_KEYWORDS = Collections.singletonList("ex_wuyh42");
    private static final String DEFAULT_OUTPUT_DIR = ".";
    private static final String DEFAULT_SORT_BY = "diff";
    private static final boolean DEFAULT_INCLUDE_REMOTE = true;
    private static final String DEFAULT_REMOTE_NAME = "origin";

    // 排序类型
    private enum SortType {
        DIFF,       // 差异大小
        CREATED,    // 创建时间
        NAME        // 分支名称
    }

    // 应用配置
    private final String repoPath;
    private final List<String> refBranches;
    private final List<Pattern> keywordPatterns;
    private final Path outputFilePath;
    private final SortType sortType;
    private final boolean includeRemote;
    private final String remoteName;
    private final boolean verbose;

    /**
     * 构造函数
     */
    public GitBranchDiffFinder(String repoPath, List<String> refBranches, List<String> keywords,
                               String outputDir, String sortBy, boolean includeRemote,
                               String remoteName, boolean verbose) {
        this.repoPath = repoPath;
        this.refBranches = refBranches;
        this.includeRemote = includeRemote;
        this.remoteName = remoteName;
        this.verbose = verbose;

        // 编译关键字为正则表达式模式（不区分大小写）
        this.keywordPatterns = keywords.stream()
                .map(k -> Pattern.compile(".*" + Pattern.quote(k) + ".*", Pattern.CASE_INSENSITIVE))
                .collect(Collectors.toList());

        // 创建输出文件路径
        String timestamp = new SimpleDateFormat("yyyyMMdd-HHmmss").format(new Date());
        this.outputFilePath = Paths.get(outputDir, "branch_diff_report_" + timestamp + ".txt");

        // 设置排序方式
        switch (sortBy.toLowerCase()) {
            case "created":
                this.sortType = SortType.CREATED;
                break;
            case "name":
                this.sortType = SortType.NAME;
                break;
            case "diff":
            default:
                this.sortType = SortType.DIFF;
                break;
        }
    }

    /**
     * 主方法
     */
    public static void main(String[] args) {
        // 解析命令行参数
        Map<String, Object> config = parseCommandLineArgs(args);

        // 设置日志
        setupLogging((String)config.get("outputDir"), (Boolean)config.get("verbose"));

        try {
            // 创建GitBranchDiffFinder实例
            GitBranchDiffFinder finder = new GitBranchDiffFinder(
                    (String)config.get("repoPath"),
                    (List<String>)config.get("refBranches"),
                    (List<String>)config.get("keywords"),
                    (String)config.get("outputDir"),
                    (String)config.get("sortBy"),
                    (Boolean)config.get("includeRemote"),
                    (String)config.get("remoteName"),
                    (Boolean)config.get("verbose"));

            // 执行分支差异查找
            finder.findDifferences();

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "执行过程中发生错误", e);
            System.err.println("执行失败：" + e.getMessage());
        }
    }

    /**
     * 解析命令行参数
     */
    private static Map<String, Object> parseCommandLineArgs(String[] args) {
        Map<String, Object> config = new HashMap<>();

        // 设置默认值
        config.put("repoPath", DEFAULT_REPO_PATH);
        config.put("refBranches", DEFAULT_REFERENCE_BRANCHES);
        config.put("keywords", DEFAULT_KEYWORDS);
        config.put("outputDir", DEFAULT_OUTPUT_DIR);
        config.put("sortBy", DEFAULT_SORT_BY);
        config.put("includeRemote", DEFAULT_INCLUDE_REMOTE);
        config.put("remoteName", DEFAULT_REMOTE_NAME);
        config.put("verbose", false);

        // 解析命令行参数
        for (int i = 0; i < args.length; i++) {
            switch (args[i]) {
                case "-r":
                case "--repo":
                    if (i + 1 < args.length) config.put("repoPath", args[++i]);
                    break;
                case "-b":
                case "--branches":
                    if (i + 1 < args.length) {
                        String[] branches = args[++i].split(",");
                        config.put("refBranches", Arrays.asList(branches));
                    }
                    break;
                case "-k":
                case "--keywords":
                    if (i + 1 < args.length) {
                        String[] keywords = args[++i].split(",");
                        config.put("keywords", Arrays.asList(keywords));
                    }
                    break;
                case "-o":
                case "--output":
                    if (i + 1 < args.length) config.put("outputDir", args[++i]);
                    break;
                case "-s":
                case "--sort":
                    if (i + 1 < args.length) config.put("sortBy", args[++i]);
                    break;
                case "--local-only":
                    config.put("includeRemote", false);
                    break;
                case "--remote":
                    if (i + 1 < args.length) config.put("remoteName", args[++i]);
                    break;
                case "-v":
                case "--verbose":
                    config.put("verbose", true);
                    break;
                case "-h":
                case "--help":
                    printHelp();
                    System.exit(0);
                    break;
            }
        }

        return config;
    }

    /**
     * 打印帮助信息
     */
    private static void printHelp() {
        System.out.println("GitBranchDiffFinder - 查找与参考分支存在差异的Git分支");
        System.out.println("\n用法：java GitBranchDiffFinder [选项]");
        System.out.println("\n选项：");
        System.out.println("  -r, --repo <路径>       Git仓库路径 (默认: 当前目录)");
        System.out.println("  -b, --branches <分支>   参考分支名称，多个用逗号分隔 (默认: prod)");
        System.out.println("  -k, --keywords <关键字> 分支关键字，支持正则表达式，多个用逗号分隔 (默认: .*)");
        System.out.println("  -o, --output <目录>     输出目录 (默认: 当前目录)");
        System.out.println("  -s, --sort <排序方式>   排序方式：diff(差异大小)，created(创建时间)，name(名称) (默认: diff)");
        System.out.println("  --local-only            仅检查本地分支，不包含远程分支");
        System.out.println("  --remote <名称>         远程仓库名称 (默认: origin)");
        System.out.println("  -v, --verbose           显示详细日志");
        System.out.println("  -h, --help              显示此帮助信息");
        System.out.println("\n示例：");
        System.out.println("  java GitBranchDiffFinder -b main -k feature,hotfix -s created -v");
        System.out.println("  java GitBranchDiffFinder --repo /path/to/repo --local-only");
    }

    /**
     * 设置日志
     */
    private static void setupLogging(String outputDir, boolean verbose) {
        try {
            // 清除现有处理器
            for (java.util.logging.Handler handler : LOGGER.getHandlers()) {
                LOGGER.removeHandler(handler);
            }

            // 设置日志级别
            LOGGER.setLevel(verbose ? Level.FINE : Level.INFO);
            LOGGER.setUseParentHandlers(false);

            // 添加控制台处理器
            ConsoleHandler consoleHandler = new ConsoleHandler();
            consoleHandler.setLevel(verbose ? Level.FINE : Level.INFO);
            LOGGER.addHandler(consoleHandler);

            // 添加文件处理器
            String timestamp = new SimpleDateFormat("yyyyMMdd-HHmmss").format(new Date());
            Path logPath = Paths.get(outputDir, "git_diff_finder_" + timestamp + ".log");
            Files.createDirectories(logPath.getParent());

            FileHandler fileHandler = new FileHandler(logPath.toString());
            fileHandler.setFormatter(new SimpleFormatter());
            fileHandler.setLevel(Level.FINE); // 文件记录详细日志
            LOGGER.addHandler(fileHandler);

        } catch (Exception e) {
            System.err.println("无法设置日志: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 查找分支差异
     */
    public void findDifferences() {
        LOGGER.info("开始查找分支差异...");
        LOGGER.info("Git仓库路径: " + repoPath);
        LOGGER.info("参考分支: " + String.join(", ", refBranches));
        LOGGER.info("关键字模式: " + keywordPatterns.stream().map(Pattern::pattern).collect(Collectors.toList()));
        LOGGER.info("包含远程分支: " + includeRemote);
        if (includeRemote) {
            LOGGER.info("远程仓库名称: " + remoteName);
        }
        LOGGER.info("输出文件: " + outputFilePath);
        LOGGER.info("排序方式: " + sortType);

        try {
            // 检查路径是否是Git仓库
            verifyGitRepository();

            // 更新远程分支信息
            if (includeRemote) {
                fetchRemoteInfo();
            }

            // 验证参考分支是否存在
            verifyReferenceBranchesExist();

            // 获取所有分支
            List<BranchInfo> allBranches = getAllBranches();

            // 筛选匹配关键字的分支
            List<BranchInfo> matchingBranches = filterBranchesByKeywords(allBranches);
            LOGGER.info("找到 " + matchingBranches.size() + " 个匹配关键字的分支");

            if (matchingBranches.isEmpty()) {
                LOGGER.warning("未找到匹配关键字的分支，程序结束");
                writeReportHeader(0);
                return;
            }

            // 查找有差异的分支
            List<BranchDiff> branchDiffs = findBranchesWithDifferences(matchingBranches);

            // 排序结果
            sortBranchDiffs(branchDiffs);

            // 生成报告
            generateReport(branchDiffs);

            LOGGER.info("分析完成！结果已保存到: " + outputFilePath);

        } catch (Exception e) {
            LOGGER.log(Level.SEVERE, "查找分支差异时发生错误", e);
            try {
                Files.write(outputFilePath,
                        ("执行错误: " + e.getMessage() + "\n").getBytes(StandardCharsets.UTF_8),
                        StandardOpenOption.CREATE, StandardOpenOption.APPEND);
            } catch (IOException ioe) {
                LOGGER.log(Level.SEVERE, "无法写入错误信息到输出文件", ioe);
            }
        }
    }

    /**
     * 验证Git命令是否可用
     */
    private void verifyGitAvailability() throws IOException, InterruptedException {
        LOGGER.fine("验证Git命令是否可用...");

        ProcessBuilder pb = new ProcessBuilder("git", "--version");
        pb.directory(new File(repoPath));

        Process process = pb.start();
        int exitCode = process.waitFor();

        if (exitCode != 0) {
            throw new IOException("Git命令不可用，请确保Git已安装并且可以在命令行中执行");
        }

        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        String version = reader.readLine();
        LOGGER.info("Git版本: " + version);
    }

    /**
     * 验证当前目录是否为Git仓库
     */
    private void verifyGitRepository() throws IOException, InterruptedException {
        // 首先验证Git命令是否可用
        verifyGitAvailability();

        LOGGER.fine("验证路径是否为Git仓库: " + repoPath);

        // 验证是否是一个Git仓库
        ProcessBuilder pb = new ProcessBuilder("git", "rev-parse", "--is-inside-work-tree");
        pb.directory(new File(repoPath));
        pb.redirectErrorStream(true); // 将错误输出合并到标准输出

        Process process = pb.start();
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

        StringBuilder output = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            output.append(line).append("\n");
        }

        int exitCode = process.waitFor();

        if (exitCode != 0 || !output.toString().trim().equals("true")) {
            LOGGER.severe("目录不是有效的Git仓库: " + repoPath);
            LOGGER.severe("命令输出: " + output.toString().trim());
            throw new IOException("指定路径不是一个有效的Git仓库: " + repoPath + "。请确保您在Git仓库目录中执行此命令。");
        }

        LOGGER.fine("验证成功：路径是有效的Git仓库");
    }

    /**
     * 获取远程仓库信息
     */
    private void fetchRemoteInfo() throws IOException, InterruptedException {
        LOGGER.info("正在获取远程仓库信息...");

        // 确保远程仓库配置正确
        ProcessBuilder pbRemote = new ProcessBuilder("git", "remote", "-v");
        pbRemote.directory(new File(repoPath));
        pbRemote.redirectErrorStream(true);

        Process processRemote = pbRemote.start();
        BufferedReader readerRemote = new BufferedReader(new InputStreamReader(processRemote.getInputStream()));

        StringBuilder remoteOutput = new StringBuilder();
        String line;
        boolean remoteFound = false;

        while ((line = readerRemote.readLine()) != null) {
            remoteOutput.append(line).append("\n");
            if (line.startsWith(remoteName + "\t")) {
                remoteFound = true;
            }
        }

        int exitCode = processRemote.waitFor();

        if (exitCode != 0) {
            LOGGER.warning("获取远程仓库信息失败: " + remoteOutput);
        } else if (!remoteFound) {
            LOGGER.warning("找不到指定的远程仓库 '" + remoteName + "'。远程仓库列表: \n" + remoteOutput);
            throw new IOException("找不到指定的远程仓库 '" + remoteName + "'。请检查远程仓库配置。");
        }

        // 获取最新的远程仓库信息
        LOGGER.info("正在获取最新的远程分支信息...");
        ProcessBuilder pbFetch = new ProcessBuilder("git", "fetch", remoteName, "--prune");
        pbFetch.directory(new File(repoPath));
        pbFetch.redirectErrorStream(true);

        Process processFetch = pbFetch.start();
        BufferedReader readerFetch = new BufferedReader(new InputStreamReader(processFetch.getInputStream()));

        StringBuilder fetchOutput = new StringBuilder();
        while ((line = readerFetch.readLine()) != null) {
            fetchOutput.append(line).append("\n");
            LOGGER.fine("Fetch: " + line);
        }

        exitCode = processFetch.waitFor();

        if (exitCode != 0) {
            LOGGER.warning("获取远程分支信息失败: " + fetchOutput);
            throw new IOException("无法获取远程分支信息。详细信息: " + fetchOutput);
        }

        LOGGER.info("成功获取远程分支信息");
    }

    /**
     * 验证参考分支是否存在
     */
    private void verifyReferenceBranchesExist() throws IOException, InterruptedException {
        LOGGER.fine("验证参考分支是否存在...");

        List<BranchInfo> existingBranches = getAllBranches();
        Set<String> branchNames = existingBranches.stream()
                .map(BranchInfo::getName)
                .collect(Collectors.toSet());

        for (String refBranch : refBranches) {
            if (!branchNames.contains(refBranch)) {
                // 检查是否有同名远程分支可以拉取
                String remoteBranch = remoteName + "/" + refBranch;
                if (branchNames.contains(remoteBranch)) {
                    LOGGER.info("参考分支 '" + refBranch + "' 在本地不存在，但在远程存在，尝试创建本地跟踪分支...");
                    createTrackingBranch(refBranch, remoteBranch);
                } else {
                    LOGGER.severe("参考分支不存在: " + refBranch);
                    throw new IOException("参考分支 '" + refBranch + "' 不存在于本地或远程。请检查分支名称是否正确。");
                }
            } else {
                LOGGER.fine("验证成功：参考分支 '" + refBranch + "' 存在");
            }
        }
    }

    /**
     * 创建跟踪远程分支的本地分支
     */
    private void createTrackingBranch(String localBranch, String remoteBranch) throws IOException, InterruptedException {
        LOGGER.fine("创建跟踪分支: " + localBranch + " <- " + remoteBranch);

        ProcessBuilder pb = new ProcessBuilder("git", "branch", localBranch, "--track", remoteBranch);
        pb.directory(new File(repoPath));
        pb.redirectErrorStream(true);

        Process process = pb.start();
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

        StringBuilder output = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            output.append(line).append("\n");
        }

        int exitCode = process.waitFor();

        if (exitCode != 0) {
            LOGGER.severe("创建跟踪分支失败: " + output);
            throw new IOException("无法创建跟踪分支 '" + localBranch + "'。详细信息: " + output);
        }

        LOGGER.info("成功创建跟踪分支: " + localBranch + " <- " + remoteBranch);
    }

    /**
     * 获取所有Git分支（包括本地和远程分支）
     */
    private List<BranchInfo> getAllBranches() throws IOException, InterruptedException {
        LOGGER.fine("获取所有Git分支...");

        List<BranchInfo> branches = new ArrayList<>();

        // 获取本地分支
        branches.addAll(getLocalBranches());

        // 如果需要，获取远程分支
        if (includeRemote) {
            branches.addAll(getRemoteBranches());
        }

        LOGGER.fine("找到 " + branches.size() + " 个Git分支");
        return branches;
    }

    /**
     * 获取本地分支
     */
    private List<BranchInfo> getLocalBranches() throws IOException, InterruptedException {
        ProcessBuilder pb = new ProcessBuilder("git", "branch");
        pb.directory(new File(repoPath));
        pb.redirectErrorStream(true);

        Process process = pb.start();
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

        List<BranchInfo> branches = new ArrayList<>();
        String line;

        while ((line = reader.readLine()) != null) {
            // 去除分支名称前的星号和空格
            String branch = line.replaceAll("^\\*?\\s*", "").trim();
            if (!branch.isEmpty()) {
                branches.add(new BranchInfo(branch, false));
            }
        }

        int exitCode = process.waitFor();
        if (exitCode != 0) {
            LOGGER.warning("获取本地分支失败");
            return new ArrayList<>();
        }

        LOGGER.fine("找到 " + branches.size() + " 个本地分支");
        return branches;
    }

    /**
     * 获取远程分支
     */
    private List<BranchInfo> getRemoteBranches() throws IOException, InterruptedException {
        ProcessBuilder pb = new ProcessBuilder("git", "branch", "-r");
        pb.directory(new File(repoPath));
        pb.redirectErrorStream(true);

        Process process = pb.start();
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

        List<BranchInfo> branches = new ArrayList<>();
        String line;

        while ((line = reader.readLine()) != null) {
            line = line.trim();
            if (line.startsWith(remoteName + "/") && !line.contains("HEAD ->")) {
                String branch = line.trim();
                branches.add(new BranchInfo(branch, true));
            }
        }

        int exitCode = process.waitFor();
        if (exitCode != 0) {
            LOGGER.warning("获取远程分支失败");
            return new ArrayList<>();
        }

        LOGGER.fine("找到 " + branches.size() + " 个远程分支");
        return branches;
    }

    /**
     * 根据关键字筛选分支
     */
    private List<BranchInfo> filterBranchesByKeywords(List<BranchInfo> branches) {
        LOGGER.fine("根据关键字筛选分支...");

        List<BranchInfo> matchingBranches = new ArrayList<>();

        for (BranchInfo branch : branches) {
            // 忽略参考分支
            boolean isRefBranch = false;
            for (String refBranch : refBranches) {
                if (branch.getName().equals(refBranch) ||
                        branch.getName().equals(remoteName + "/" + refBranch)) {
                    isRefBranch = true;
                    break;
                }
            }

            if (isRefBranch) {
                continue;
            }

            // 检查是否匹配任一关键字模式 - 使用find()实现模糊匹配
            for (Pattern pattern : keywordPatterns) {
                if (pattern.matcher(branch.getName()).find()) {  // 模糊匹配
                    matchingBranches.add(branch);
                    LOGGER.fine("分支匹配关键字: " + branch.getName() + " (匹配模式: " + pattern.pattern() + ")");
                    break;
                }
            }
        }

        return matchingBranches;
    }

    /**
     * 查找与参考分支有差异的分支
     */
    private List<BranchDiff> findBranchesWithDifferences(List<BranchInfo> branches) throws IOException, InterruptedException {
        LOGGER.fine("查找与参考分支有差异的分支...");

        List<BranchDiff> branchDiffs = new ArrayList<>();

        for (BranchInfo branch : branches) {
            LOGGER.fine("检查分支: " + branch.getName());

            // 对每个参考分支进行检查
            for (String refBranch : refBranches) {
                try {
                    // 直接使用简化的检查方式
                    if (branchHasDifferences(refBranch, branch.getName())) {
                        BranchDiff diff = getBranchDiff(branch, refBranch);
                        branchDiffs.add(diff);
                        LOGGER.info("找到差异分支: " + branch.getName() + " (与 " + refBranch + ")");
                        break; // 一旦发现有差异，就不再与其他参考分支比较
                    } else {
                        LOGGER.fine("分支没有差异: " + branch.getName() + " (与 " + refBranch + ")");
                    }
                } catch (Exception e) {
                    LOGGER.warning("检查分支 " + branch.getName() + " 与 " + refBranch + " 的差异时发生错误: " + e.getMessage());
                    // 继续检查下一个分支，不中断整个过程
                }
            }
        }

        LOGGER.info("共找到 " + branchDiffs.size() + " 个有差异的分支");
        return branchDiffs;
    }

    /**
     * 直接检查分支是否有差异
     */
    private boolean branchHasDifferences(String refBranch, String branch) throws IOException, InterruptedException {
        LOGGER.fine("直接检查分支差异: " + refBranch + " <-> " + branch);

        // 处理远程分支名称
        String branchName = branch;
        if (branch.startsWith(remoteName + "/")) {
            branchName = branch; // 使用完整的远程分支名称
        }

        // 使用 git diff 检查有无差异
        ProcessBuilder pb = new ProcessBuilder("git", "diff", "--quiet", refBranch, branchName);
        pb.directory(new File(repoPath));
        pb.redirectErrorStream(true);

        Process process = pb.start();
        int exitCode = process.waitFor();

        // exitCode = 0 表示没有差异，exitCode = 1 表示有差异
        boolean hasDifference = (exitCode == 1);

        LOGGER.fine("分支差异检查结果: " + (hasDifference ? "有差异" : "无差异"));
        return hasDifference;
    }

    /**
     * 获取分支差异信息
     */
    private BranchDiff getBranchDiff(BranchInfo branch, String refBranch) throws IOException, InterruptedException {
        BranchDiff branchDiff = new BranchDiff(branch.getName(), refBranch, branch.isRemote());

        try {
            // 获取分支创建时间
            branchDiff.setCreationDate(getBranchCreationDate(branch.getName()));

            // 获取分支最后提交信息
            setLastCommitInfo(branchDiff);

            // 获取分支差异
            int aheadCount = getCommitDiffCount(refBranch, branch.getName());
            branchDiff.setAheadCount(aheadCount);

            // 获取差异文件
            List<String> changedFiles = getDiffFiles(refBranch, branch.getName());
            branchDiff.setChangedFiles(changedFiles);

            // 获取具体变更内容
            if (!changedFiles.isEmpty()) {
                String diffContent = getDiffContent(refBranch, branch.getName());
                branchDiff.setDiffContent(diffContent);
            }
        } catch (Exception e) {
            LOGGER.warning("获取分支 " + branch.getName() + " 信息时发生错误: " + e.getMessage());
            // 设置基本信息，即使发生错误也不会影响结果
        }

        return branchDiff;
    }

    /**
     * 获取分支创建时间
     */
    private String getBranchCreationDate(String branch) throws IOException, InterruptedException {
        ProcessBuilder pb = new ProcessBuilder(
                "git", "log", "--date=iso", "--format=%ad", branch, "--max-count=1");
        pb.directory(new File(repoPath));
        pb.redirectErrorStream(true);

        Process process = pb.start();
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        String date = reader.readLine();

        process.waitFor();
        return date != null ? date : "未知";
    }

    /**
     * 设置最后提交信息
     */
    private void setLastCommitInfo(BranchDiff branchDiff) throws IOException, InterruptedException {
        ProcessBuilder pb = new ProcessBuilder(
                "git", "log", "--format=%H|%an|%ad|%s", branchDiff.getBranch(), "--max-count=1", "--date=iso");
        pb.directory(new File(repoPath));
        pb.redirectErrorStream(true);

        Process process = pb.start();
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        String commitInfo = reader.readLine();

        process.waitFor();

        if (commitInfo != null) {
            String[] parts = commitInfo.split("\\|", 4);
            if (parts.length >= 4) {
                branchDiff.setLastCommitHash(parts[0]);
                branchDiff.setLastCommitAuthor(parts[1]);
                branchDiff.setLastCommitDate(parts[2]);
                branchDiff.setLastCommitMessage(parts[3]);
            }
        }
    }

    /**
     * 获取提交差异数量
     */
    private int getCommitDiffCount(String fromBranch, String toBranch) throws IOException, InterruptedException {
        LOGGER.fine("获取提交差异数量: " + fromBranch + ".." + toBranch);

        ProcessBuilder pb = new ProcessBuilder(
                "git", "rev-list", "--count", fromBranch + ".." + toBranch);
        pb.directory(new File(repoPath));
        pb.redirectErrorStream(true);

        Process process = pb.start();
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));
        String countStr = reader.readLine();
        StringBuilder output = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            output.append(line).append("\n");
        }

        int exitCode = process.waitFor();
        if (exitCode != 0) {
            LOGGER.warning("获取提交差异数量时出错: " + output.toString());
            return 0;
        }

        int count = countStr != null ? Integer.parseInt(countStr) : 0;
        LOGGER.fine("提交差异数量: " + count);
        return count;
    }

    /**
     * 获取差异文件列表
     */
    private List<String> getDiffFiles(String fromBranch, String toBranch) throws IOException, InterruptedException {
        LOGGER.fine("获取差异文件列表: " + fromBranch + " -> " + toBranch);

        ProcessBuilder pb = new ProcessBuilder(
                "git", "diff", "--name-only", fromBranch, toBranch);
        pb.directory(new File(repoPath));
        pb.redirectErrorStream(true);

        Process process = pb.start();
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

        List<String> files = new ArrayList<>();
        String line;
        while ((line = reader.readLine()) != null) {
            if (!line.trim().isEmpty()) {
                files.add(line);
            }
        }

        int exitCode = process.waitFor();
        if (exitCode != 0) {
            LOGGER.warning("获取差异文件列表时出错，返回空列表");
            return new ArrayList<>();
        }

        LOGGER.fine("找到 " + files.size() + " 个差异文件");
        return files;
    }

    /**
     * 获取差异内容
     */
    private String getDiffContent(String fromBranch, String toBranch) throws IOException, InterruptedException {
        LOGGER.fine("获取差异内容: " + fromBranch + " -> " + toBranch);

        ProcessBuilder pb = new ProcessBuilder(
                "git", "diff", fromBranch, toBranch);
        pb.directory(new File(repoPath));
        pb.redirectErrorStream(true);

        Process process = pb.start();
        BufferedReader reader = new BufferedReader(new InputStreamReader(process.getInputStream()));

        StringBuilder content = new StringBuilder();
        String line;
        while ((line = reader.readLine()) != null) {
            content.append(line).append("\n");
        }

        process.waitFor();
        return content.toString();
    }

    /**
     * 根据配置的排序方式对分支差异进行排序
     */
    private void sortBranchDiffs(List<BranchDiff> branchDiffs) {
        LOGGER.fine("对分支差异结果进行排序，方式: " + sortType);

        switch (sortType) {
            case DIFF:
                // 按差异大小排序（提交数量 + 文件数量）
                Collections.sort(branchDiffs, Comparator
                        .<BranchDiff>comparingInt(d -> d.getAheadCount() + d.getChangedFiles().size())
                        .reversed());
                break;
            case CREATED:
                // 按创建时间排序（需要解析日期字符串）
                Collections.sort(branchDiffs, Comparator.comparing(BranchDiff::getCreationDate));
                break;
            case NAME:
                // 按分支名称排序
                Collections.sort(branchDiffs, Comparator.comparing(BranchDiff::getBranch));
                break;
        }
    }

    /**
     * 生成报告
     */
    private void generateReport(List<BranchDiff> branchDiffs) throws IOException {
        LOGGER.fine("生成分支差异报告...");

        // 写入报告头部
        writeReportHeader(branchDiffs.size());

        // 写入分支差异详情
        for (int i = 0; i < branchDiffs.size(); i++) {
            BranchDiff diff = branchDiffs.get(i);

            String report = String.format(
                    "\n分支 #%d: %s %s\n" +
                            "-------------------------------------------\n" +
                            "参考分支: %s\n" +
                            "创建时间: %s\n" +
                            "最后提交: %s (%s)\n" +
                            "提交作者: %s\n" +
                            "提交信息: %s\n" +
                            "提交差异数: %d\n" +
                            "修改文件数: %d\n",
                    i + 1, diff.getBranch(), diff.isRemote() ? "[远程]" : "[本地]",
                    diff.getRefBranch(),
                    diff.getCreationDate(),
                    diff.getLastCommitHash(), diff.getLastCommitDate(),
                    diff.getLastCommitAuthor(),
                    diff.getLastCommitMessage(),
                    diff.getAheadCount(),
                    diff.getChangedFiles().size()
            );

            Files.write(outputFilePath, report.getBytes(StandardCharsets.UTF_8), StandardOpenOption.APPEND);

            // 写入修改的文件列表
            if (!diff.getChangedFiles().isEmpty()) {
                Files.write(outputFilePath,
                        "\n修改的文件:\n".getBytes(StandardCharsets.UTF_8),
                        StandardOpenOption.APPEND);

                for (String file : diff.getChangedFiles()) {
                    Files.write(outputFilePath,
                            ("- " + file + "\n").getBytes(StandardCharsets.UTF_8),
                            StandardOpenOption.APPEND);
                }
            }

            // 写入差异内容（可能很长，仅在详细模式下包含）
            if (verbose && !diff.getDiffContent().isEmpty()) {
                Files.write(outputFilePath,
                        "\n差异内容:\n".getBytes(StandardCharsets.UTF_8),
                        StandardOpenOption.APPEND);
                Files.write(outputFilePath,
                        diff.getDiffContent().getBytes(StandardCharsets.UTF_8),
                        StandardOpenOption.APPEND);
            }

            Files.write(outputFilePath,
                    "\n===========================================\n".getBytes(StandardCharsets.UTF_8),
                    StandardOpenOption.APPEND);
        }

        // 打印报告位置
        System.out.println("\n报告已生成: " + outputFilePath);

        // 显示报告摘要
        if (!branchDiffs.isEmpty()) {
            System.out.println("\n找到 " + branchDiffs.size() + " 个与参考分支存在差异的分支:");
            for (int i = 0; i < branchDiffs.size(); i++) {
                BranchDiff diff = branchDiffs.get(i);
                System.out.printf("%d. %s %s (差异: %d提交, %d文件)\n",
                        i + 1, diff.getBranch(), diff.isRemote() ? "[远程]" : "[本地]",
                        diff.getAheadCount(), diff.getChangedFiles().size());
            }
        } else {
            System.out.println("\n未找到与参考分支存在差异的分支。");
        }
    }

    /**
     * 写入报告头部
     */
    private void writeReportHeader(int diffBranchCount) throws IOException {
        String header = String.format(
                "Git分支差异报告\n" +
                        "===========================================\n" +
                        "生成时间: %s\n" +
                        "仓库路径: %s\n" +
                        "参考分支: %s\n" +
                        "关键字: %s\n" +
                        "包含远程分支: %s\n" +
                        "远程仓库名称: %s\n" +
                        "排序方式: %s\n" +
                        "找到的差异分支数: %d\n" +
                        "===========================================\n",
                new Date(),
                repoPath,
                String.join(", ", refBranches),
                keywordPatterns.stream().map(Pattern::pattern).collect(Collectors.joining(", ")),
                includeRemote ? "是" : "否",
                remoteName,
                sortType,
                diffBranchCount
        );

        Files.write(outputFilePath, header.getBytes(StandardCharsets.UTF_8),
                StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING);
    }

    /**
     * 分支信息类
     */
    private static class BranchInfo {
        private final String name;
        private final boolean remote;

        public BranchInfo(String name, boolean remote) {
            this.name = name;
            this.remote = remote;
        }

        public String getName() {
            return name;
        }

        public boolean isRemote() {
            return remote;
        }
    }

    /**
     * 分支差异信息类
     */
    private static class BranchDiff {
        private final String branch;
        private final String refBranch;
        private final boolean remote;
        private String creationDate = "未知";
        private String lastCommitHash = "";
        private String lastCommitAuthor = "";
        private String lastCommitDate = "";
        private String lastCommitMessage = "";
        private int aheadCount = 0;
        private List<String> changedFiles = new ArrayList<>();
        private String diffContent = "";

        public BranchDiff(String branch, String refBranch, boolean remote) {
            this.branch = branch;
            this.refBranch = refBranch;
            this.remote = remote;
        }

        // Getters and setters
        public String getBranch() { return branch; }
        public String getRefBranch() { return refBranch; }
        public boolean isRemote() { return remote; }

        public String getCreationDate() { return creationDate; }
        public void setCreationDate(String creationDate) { this.creationDate = creationDate; }

        public String getLastCommitHash() { return lastCommitHash; }
        public void setLastCommitHash(String lastCommitHash) { this.lastCommitHash = lastCommitHash; }

        public String getLastCommitAuthor() { return lastCommitAuthor; }
        public void setLastCommitAuthor(String lastCommitAuthor) { this.lastCommitAuthor = lastCommitAuthor; }

        public String getLastCommitDate() { return lastCommitDate; }
        public void setLastCommitDate(String lastCommitDate) { this.lastCommitDate = lastCommitDate; }

        public String getLastCommitMessage() { return lastCommitMessage; }
        public void setLastCommitMessage(String lastCommitMessage) { this.lastCommitMessage = lastCommitMessage; }

        public int getAheadCount() { return aheadCount; }
        public void setAheadCount(int aheadCount) { this.aheadCount = aheadCount; }

        public List<String> getChangedFiles() { return changedFiles; }
        public void setChangedFiles(List<String> changedFiles) { this.changedFiles = changedFiles; }

        public String getDiffContent() { return diffContent; }
        public void setDiffContent(String diffContent) { this.diffContent = diffContent; }
    }
}