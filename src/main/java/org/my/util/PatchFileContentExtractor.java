package org.my.util;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.util.HashSet;
import java.util.Set;

/**
 * 读取 patch 文件，并根据在代码中定义的项目根目录，
 * 查找并提取已修改和已删除文件的完整原始内容。
 * (已修复路径解析逻辑，能正确处理带 revision信息的行)
 */
public class PatchFileContentExtractor {

    /**
     * 从本地项目中提取文件内容。
     * @param projectBasePath 用户指定的项目根目录路径。
     * @param patchFilePath patch 文件的路径。
     * @param outputFilePath 输出 TXT 文件的路径。
     * @throws IOException 如果读写文件时发生错误。
     */
    public void extractFilesFromLocalProject(Path projectBasePath, Path patchFilePath, Path outputFilePath) throws IOException {
        Set<String> relativePaths = new HashSet<>();

        // 第一步：解析 patch 文件，收集所有文件的相对路径
        try (BufferedReader reader = Files.newBufferedReader(patchFilePath, StandardCharsets.UTF_8)) {
            String currentLine;
            while ((currentLine = reader.readLine()) != null) {
                if (currentLine.startsWith("--- a/")) {
                    // =================== 这是修复的关键部分 ===================
                    // 获取 "--- a/" 之后的所有内容
                    String fullPathLine = currentLine.substring(6);
                    String relativePath;

                    // 查找制表符 '\t'，它通常分隔文件路径和元数据
                    int tabIndex = fullPathLine.indexOf('\t');

                    if (tabIndex != -1) {
                        // 如果找到制表符，路径就是它之前的部分
                        relativePath = fullPathLine.substring(0, tabIndex);
                    } else {
                        // 如果没有制表符，整行（减去前缀）都是路径
                        relativePath = fullPathLine;
                    }

                    // 添加清理过的路径，移除可能存在的前后空格
                    relativePaths.add(relativePath.trim());
                    // =========================================================
                }
            }
        }
        System.out.println("在 patch 文件中找到 " + relativePaths.size() + " 个不重复的文件路径。");

        // 第二步：根据相对路径读取本地文件并写入输出文件
        try (BufferedWriter writer = Files.newBufferedWriter(outputFilePath, StandardCharsets.UTF_8,
                StandardOpenOption.CREATE, StandardOpenOption.TRUNCATE_EXISTING)) {

            for (String relativePath : relativePaths) {
                // 将项目根目录和文件相对路径拼接成绝对路径
                Path sourceFilePath = projectBasePath.resolve(relativePath);

                writer.write("====================================================================");
                writer.newLine();
                writer.write("### START OF FILE: " + relativePath);
                writer.newLine();
                writer.write("====================================================================");
                writer.newLine();
                writer.newLine();

                if (Files.exists(sourceFilePath)) {
                    String fileContent = new String(Files.readAllBytes(sourceFilePath), StandardCharsets.UTF_8);
                    writer.write(fileContent);
                    System.out.println("成功提取: " + relativePath);
                } else {
                    String warningMessage = "!!! 警告: 在指定项目目录中未找到文件。该文件可能已被删除。路径: " + sourceFilePath.toAbsolutePath();
                    writer.write(warningMessage);
                    System.out.println(warningMessage);
                }

                writer.newLine();
                writer.newLine();
                writer.write("====================================================================");
                writer.newLine();
                writer.write("### END OF FILE: " + relativePath);
                writer.newLine();
                writer.write("====================================================================");
                writer.newLine();
                writer.newLine();
            }
        }
        System.out.println("提取完成！所有内容已写入到 " + outputFilePath.toAbsolutePath());
    }

    public static void main(String[] args) {
        // ==================================================================
        // ===                      用户配置区域                           ===
        // ===   请在下方修改为您本地的实际路径                             ===
        // ==================================================================

        // 1. 请将这里替换为您项目的根目录的绝对路径
        //    示例 (Windows): "C:\\Users\\<USER>\\Projects\\MyProject"
        //    示例 (Linux/macOS): "/home/<USER>/projects/my-project"
        String projectRootPath = "E:\\project\\pam";

        // 2. 请将这里替换为您 .patch 文件的绝对路径
        String patchFilePath = "D:\\Users\\ex_wuyh42.CN\\Desktop\\fix(ctc)__修复里程碑设计计划变更记录检查逻辑，移除模组类型详设的跳过逻辑以支持上级模组状态检查_feat(ctc)__在物料调整查询结果中新增父级物料描述设置逻辑，完善层.patch";

        // 3. 输出文件名 (通常无需修改)
        String outputFileName = "full_original_content.txt";

        // ==================================================================
        // ===                      配置结束                              ===
        // ==================================================================


        // --- 执行区域 ---
        try {
            // 检查用户是否已修改默认路径
            if (projectRootPath.contains("C:\\path\\to\\your\\project") || patchFilePath.contains("C:\\path\\to\\your\\")) {
                System.err.println("错误：请先修改 main 方法中的 'projectRootPath' 和 'patchFilePath' 变量，指向您的实际路径。");
                return;
            }

            Path projectRoot = Paths.get(projectRootPath);
            Path patchFile = Paths.get(patchFilePath);
            Path outputFile = Paths.get(outputFileName);

            // 验证路径有效性
            if (!Files.isDirectory(projectRoot)) {
                System.err.println("错误：提供的项目根目录不是一个有效的目录！");
                System.err.println("无效路径: " + projectRoot.toAbsolutePath());
                return;
            }
            if (!Files.exists(patchFile)) {
                System.err.println("错误：找不到指定的 patch 文件！");
                System.err.println("无效路径: " + patchFile.toAbsolutePath());
                return;
            }

            System.out.println("项目根目录设置为: " + projectRoot.toAbsolutePath());
            System.out.println("正在读取 Patch 文件: " + patchFile.toAbsolutePath());
            System.out.println("输出文件将保存为: " + outputFile.toAbsolutePath());
            System.out.println("---");

            PatchFileContentExtractor extractor = new PatchFileContentExtractor();
            extractor.extractFilesFromLocalProject(projectRoot, patchFile, outputFile);

        } catch (Exception e) { // 捕获更广泛的异常，因为 InvalidPathException 可能在 Paths.get 中发生
            System.err.println("处理文件时发生严重错误：");
            e.printStackTrace();
        }
    }
}