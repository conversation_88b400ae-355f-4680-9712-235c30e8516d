package org.my.util;
import java.io.*;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.*;

/**
 * SQL转换工具类
 * 用于将柔性系统相关的SQL语句转换为瑞仕格医疗系统的SQL语句
 */
public class SqlTransformTool {

    // 基准时间：2025年6月25日10:50
    private static long currentTimestamp = 1751000000000L;

    // 用于存储旧ID到新ID的映射关系
    private static final Map<String, String> roleIdMapping = new HashMap<>();

    // 源文件编码
    private static final Charset SOURCE_CHARSET = Charset.forName("GB18030");

    // 输出文件编码
    private static final Charset OUTPUT_CHARSET = StandardCharsets.UTF_8;

    public static void main(String[] args) {
        try {
            System.out.println("====== SQL转换工具 ======");
            System.out.println("源文件编码: GB18030");
            System.out.println("输出文件编码: UTF-8");
            System.out.println("========================\n");

            // 检查是否有参数指定测试模式
            if (args.length > 0 && "test".equals(args[0])) {
                System.out.println("运行测试模式：只处理role_menu，使用手动映射");
                testRoleMenuTransformOnly();
                return;
            }

            // 先测试role_menu文件格式
            System.out.println("预检查：测试role_menu文件格式...");
            testRoleMenuFile();
            System.out.println();

            // 第一步：处理role表，建立ID映射
            System.out.println("第一步：处理role表...");
            processRoleFile();

            // 显示部分ID映射关系用于验证
            System.out.println("\n建立的ID映射关系（显示前5条）：");
            int count = 0;
            for (Map.Entry<String, String> entry : roleIdMapping.entrySet()) {
                System.out.println("  [" + entry.getKey() + "] => [" + entry.getValue() + "]");
                if (++count >= 5) {
                    System.out.println("  ... 共 " + roleIdMapping.size() + " 条映射");
                    break;
                }
            }

            // 检查映射是否为空
            if (roleIdMapping.isEmpty()) {
                System.out.println("警告：roleIdMapping为空！role表可能没有正确处理！");
                System.out.println("建议运行测试模式：java SqlTransformTool test");
                return; // 如果没有映射关系，就不继续处理
            }

            // 第二步：使用映射关系处理role_menu表
            System.out.println("\n第二步：处理role_menu表...");
            processRoleMenuFile();

            System.out.println("\n====== 转换完成！======");
            System.out.println("输出文件：");
            System.out.println("  1. role_transformed.sql");
            System.out.println("  2. role_menu_transformed.sql");

        } catch (Exception e) {
            System.err.println("错误：" + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 处理role表文件 - 按分号分割完整SQL记录
     */
    private static void processRoleFile() throws IOException {
        System.out.println("  开始读取role文件...");

        // 读取整个文件内容
        StringBuilder fileContent = new StringBuilder();
        BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream("D:\\Users\\ex_wuyh42.CN\\Desktop\\role.txt"), SOURCE_CHARSET)
        );

        String line;
        while ((line = reader.readLine()) != null) {
            fileContent.append(line).append("\n");
        }
        reader.close();

        // 按分号分割SQL语句
        String[] sqlStatements = fileContent.toString().split(";");
        System.out.println("  找到 " + sqlStatements.length + " 个SQL语句");

        BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream("D:\\Users\\ex_wuyh42.CN\\Desktop\\role_transformed.sql"), OUTPUT_CHARSET)
        );

        int processedCount = 0;

        for (int i = 0; i < sqlStatements.length; i++) {
            String sqlStatement = sqlStatements[i].trim();

            // 跳过空语句
            if (sqlStatement.isEmpty()) {
                continue;
            }

            // 显示前几个SQL语句用于调试
            if (i < 2) {
                System.out.println("  SQL语句 #" + (i + 1) + ": " + sqlStatement.replaceAll("\\s+", " ").substring(0, Math.min(100, sqlStatement.length())) + "...");
            }

            if (sqlStatement.contains("INSERT INTO pam_basedata.light_role")) {
                System.out.println("  处理role SQL #" + (processedCount + 1));

                // 转换SQL
                String transformedSql = transformRoleSqlComplete(sqlStatement);
                writer.write(transformedSql);
                writer.write(";");
                writer.newLine();
                processedCount++;
            } else {
                // 非role的SQL语句，直接写入
                writer.write(sqlStatement);
                if (!sqlStatement.isEmpty()) {
                    writer.write(";");
                }
                writer.newLine();
            }
        }

        writer.close();
        System.out.println("  处理了 " + processedCount + " 条role记录");
    }

    /**
     * 转换role表的SQL语句
     */
    private static String transformRoleSql(String sql) {
        // 提取VALUES内容
        int valuesStart = sql.indexOf("VALUES(");
        int valuesEnd = sql.lastIndexOf(");");

        if (valuesStart == -1 || valuesEnd == -1 || valuesEnd <= valuesStart) {
            return sql;
        }

        String beforeValues = sql.substring(0, valuesStart + 7);
        String valuesContent = sql.substring(valuesStart + 7, valuesEnd);
        String afterValues = ");";

        // 智能解析字段
        List<String> fields = smartParseFields(valuesContent);

        if (fields.size() >= 10) {
            // 提取旧ID（去除所有空格）
            String oldId = fields.get(0).replaceAll("\\s+", "");

            // 生成新ID
            String newId = generateId();

            // 保存映射关系
            roleIdMapping.put(oldId, newId);
            System.out.println("    映射: " + oldId + " => " + newId);

            // 替换字段值
            fields.set(0, newId); // ID
            fields.set(1, transformText(fields.get(1))); // name
            // fields.get(2) 是 code，保持不变
            fields.set(3, transformText(fields.get(3))); // description
            fields.set(4, "'上海瑞仕格医疗科技有限公司'"); // unit_name
            // 其他字段保持不变

            // 重建SQL
            return beforeValues + String.join(", ", fields) + afterValues;
        }

        return sql;
    }

    /**
     * 转换完整的role表SQL语句（处理多行SQL）
     */
    private static String transformRoleSqlComplete(String sqlStatement) {
        System.out.println("    开始转换完整role SQL语句...");

        // 将多行SQL合并为单行，便于处理
        String cleanSql = sqlStatement.replaceAll("\\s+", " ").trim();

        // 提取VALUES内容
        int valuesStart = cleanSql.indexOf("VALUES(");
        int valuesEnd = cleanSql.lastIndexOf(")");

        if (valuesStart == -1 || valuesEnd == -1) {
            System.out.println("    警告：无法找到VALUES子句");
            return sqlStatement;
        }

        String beforeValues = cleanSql.substring(0, valuesStart + 7);
        String valuesContent = cleanSql.substring(valuesStart + 7, valuesEnd);

        // 智能解析字段
        List<String> fields = smartParseFields(valuesContent);

        if (fields.size() >= 10) {
            // 提取旧ID（去除所有空格）
            String oldId = fields.get(0).replaceAll("\\s+", "");

            // 生成新ID
            String newId = generateId();

            // 保存映射关系
            roleIdMapping.put(oldId, newId);
            System.out.println("    映射: " + oldId + " => " + newId);

            // 替换字段值
            fields.set(0, newId); // ID
            fields.set(1, transformText(fields.get(1))); // name
            // fields.get(2) 是 code，保持不变
            fields.set(3, transformText(fields.get(3))); // description
            fields.set(4, "'上海瑞仕格医疗科技有限公司'"); // unit_name
            // 其他字段保持不变

            // 重建SQL - 保持原有的多行格式
            String newSql = "INSERT INTO pam_basedata.light_role\n" +
                           "(id, name, code, description, unit_name, create_by, create_at, update_by, update_at, delete_flag)\n" +
                           "VALUES(" + String.join(", ", fields) + ")";

            return newSql;
        }

        return sqlStatement;
    }

    /**
     * 处理role_menu表文件 - 按分号分割完整SQL记录
     */
    private static void processRoleMenuFile() throws IOException {
        System.out.println("  开始读取role_menu文件...");

        // 读取整个文件内容
        StringBuilder fileContent = new StringBuilder();
        BufferedReader reader = new BufferedReader(
                new InputStreamReader(new FileInputStream("D:\\Users\\ex_wuyh42.CN\\Desktop\\role_menu.txt"), SOURCE_CHARSET)
        );

        String line;
        while ((line = reader.readLine()) != null) {
            fileContent.append(line).append("\n");
        }
        reader.close();

        // 按分号分割SQL语句
        String[] sqlStatements = fileContent.toString().split(";");
        System.out.println("  找到 " + sqlStatements.length + " 个SQL语句");

        BufferedWriter writer = new BufferedWriter(
                new OutputStreamWriter(new FileOutputStream("D:\\Users\\ex_wuyh42.CN\\Desktop\\role_menu_transformed.sql"), OUTPUT_CHARSET)
        );

        int processedCount = 0;
        int replacedCount = 0;

        for (int i = 0; i < sqlStatements.length; i++) {
            String sqlStatement = sqlStatements[i].trim();

            // 跳过空语句
            if (sqlStatement.isEmpty()) {
                continue;
            }

            // 显示前几个SQL语句用于调试
            if (i < 3) {
                System.out.println("  SQL语句 #" + (i + 1) + ": " + sqlStatement.replaceAll("\\s+", " "));
            }

            if (sqlStatement.contains("INSERT INTO pam_basedata.light_role_menu")) {
                System.out.println("  处理role_menu SQL #" + (processedCount + 1));

                // 转换SQL
                RoleMenuTransformResult result = transformRoleMenuSqlComplete(sqlStatement);
                writer.write(result.sql);
                writer.write(";");
                writer.newLine();

                if (result.roleIdReplaced) {
                    replacedCount++;
                }
                processedCount++;
            } else {
                // 非role_menu的SQL语句，直接写入
                writer.write(sqlStatement);
                if (!sqlStatement.isEmpty()) {
                    writer.write(";");
                }
                writer.newLine();
            }
        }

        writer.close();

        System.out.println("  处理了 " + processedCount + " 条role_menu记录");
        System.out.println("  成功替换了 " + replacedCount + " 个role_id");
    }

    /**
     * 转换role_menu表的SQL（返回转换结果和是否成功替换role_id）
     */
    private static RoleMenuTransformResult transformRoleMenuSql(String sql) {
        System.out.println("    开始转换SQL: " + sql.substring(0, Math.min(150, sql.length())) + "...");

        // 提取VALUES内容
        int valuesStart = sql.indexOf("VALUES(");
        int valuesEnd = sql.indexOf(");");

        if (valuesStart == -1 || valuesEnd == -1) {
            System.out.println("    警告：无法找到VALUES子句: " + sql);
            return new RoleMenuTransformResult(sql, false);
        }

        String beforeValues = sql.substring(0, valuesStart + 7);
        String valuesContent = sql.substring(valuesStart + 7, valuesEnd);
        String afterValues = ");";

        // 直接用逗号分割（role_menu的字段都是数字，不包含逗号）
        String[] parts = valuesContent.split(",");

        if (parts.length >= 3) {
            // 清理字段值（去除空格）
            String oldPrimaryId = parts[0].trim();  // 修复：这是主键ID，不是menu_id
            String oldRoleId = parts[1].trim();
            String menuId = parts[2].trim();

            System.out.println("    解析字段 - 旧主键ID: [" + oldPrimaryId + "], 旧role_id: [" + oldRoleId + "], menu_id: [" + menuId + "]");

            // 生成新的主键ID（修复：变量名从newMenuId改为newPrimaryId）
            String newPrimaryId = generateId();
            System.out.println("    生成新主键ID: " + newPrimaryId);

            // 查找role_id的映射
            System.out.println("    在映射表中查找role_id: [" + oldRoleId + "]");
            System.out.println("    当前映射表内容: " + roleIdMapping);

            String newRoleId = roleIdMapping.get(oldRoleId);
            boolean roleIdReplaced = false;

            if (newRoleId != null) {
                roleIdReplaced = true;
                System.out.println("    成功替换role_id: " + oldRoleId + " => " + newRoleId);
            } else {
                newRoleId = oldRoleId; // 如果找不到映射，保持原值
                System.out.println("    警告：未找到role_id [" + oldRoleId + "] 的映射，保持原值！");
            }

            // 重建SQL（修复：使用newPrimaryId而不是newMenuId）
            String newValues = newPrimaryId + ", " + newRoleId + ", " + menuId;
            String newSql = beforeValues + newValues + afterValues;

            System.out.println("    生成新SQL: " + newSql);
            return new RoleMenuTransformResult(newSql, roleIdReplaced);
        } else {
            System.out.println("    警告：字段数量不足，期望3个字段，实际: " + parts.length);
            for (int i = 0; i < parts.length; i++) {
                System.out.println("      字段[" + i + "]: [" + parts[i].trim() + "]");
            }
        }

        return new RoleMenuTransformResult(sql, false);
    }

    /**
     * 转换完整的role_menu表SQL语句（处理多行SQL）
     */
    private static RoleMenuTransformResult transformRoleMenuSqlComplete(String sqlStatement) {
        System.out.println("    开始转换完整SQL语句...");

        // 将多行SQL合并为单行，便于处理
        String cleanSql = sqlStatement.replaceAll("\\s+", " ").trim();
        System.out.println("    清理后的SQL: " + cleanSql);

        // 提取VALUES内容
        int valuesStart = cleanSql.indexOf("VALUES(");
        int valuesEnd = cleanSql.lastIndexOf(")");

        if (valuesStart == -1 || valuesEnd == -1) {
            System.out.println("    警告：无法找到VALUES子句");
            return new RoleMenuTransformResult(sqlStatement, false);
        }

        String beforeValues = cleanSql.substring(0, valuesStart + 7);
        String valuesContent = cleanSql.substring(valuesStart + 7, valuesEnd);

        System.out.println("    解析VALUES内容: [" + valuesContent + "]");

        // 直接用逗号分割（role_menu的字段都是数字，不包含逗号）
        String[] parts = valuesContent.split(",");

        if (parts.length >= 3) {
            // 清理字段值（去除空格）
            String oldPrimaryId = parts[0].trim();
            String oldRoleId = parts[1].trim();
            String menuId = parts[2].trim();

            System.out.println("    解析字段 - 旧主键ID: [" + oldPrimaryId + "], 旧role_id: [" + oldRoleId + "], menu_id: [" + menuId + "]");

            // 生成新的主键ID
            String newPrimaryId = generateId();
            System.out.println("    生成新主键ID: " + newPrimaryId);

            // 查找role_id的映射
            System.out.println("    在映射表中查找role_id: [" + oldRoleId + "]");

            String newRoleId = roleIdMapping.get(oldRoleId);
            boolean roleIdReplaced = false;

            if (newRoleId != null) {
                roleIdReplaced = true;
                System.out.println("    成功替换role_id: " + oldRoleId + " => " + newRoleId);
            } else {
                newRoleId = oldRoleId; // 如果找不到映射，保持原值
                System.out.println("    警告：未找到role_id [" + oldRoleId + "] 的映射，保持原值！");
            }

            // 重建SQL - 保持原有的多行格式
            String newSql = "INSERT INTO pam_basedata.light_role_menu\n" +
                           "(id, role_id, menu_id)\n" +
                           "VALUES(" + newPrimaryId + ", " + newRoleId + ", " + menuId + ")";

            System.out.println("    生成新SQL: " + newSql.replaceAll("\\n", " "));
            return new RoleMenuTransformResult(newSql, roleIdReplaced);
        } else {
            System.out.println("    警告：字段数量不足，期望3个字段，实际: " + parts.length);
            for (int i = 0; i < parts.length; i++) {
                System.out.println("      字段[" + i + "]: [" + parts[i].trim() + "]");
            }
        }

        return new RoleMenuTransformResult(sqlStatement, false);
    }

    /**
     * 智能解析字段（处理包含逗号的字符串）
     */
    private static List<String> smartParseFields(String valuesContent) {
        List<String> fields = new ArrayList<>();
        StringBuilder currentField = new StringBuilder();
        boolean inQuotes = false;

        for (int i = 0; i < valuesContent.length(); i++) {
            char c = valuesContent.charAt(i);

            // 检测引号
            if (c == '\'' && (i == 0 || valuesContent.charAt(i-1) != '\\')) {
                inQuotes = !inQuotes;
            }

            // 在引号外遇到逗号时分割
            if (c == ',' && !inQuotes) {
                fields.add(currentField.toString().trim());
                currentField = new StringBuilder();
                continue;
            }

            currentField.append(c);
        }

        // 添加最后一个字段
        if (currentField.length() > 0) {
            fields.add(currentField.toString().trim());
        }

        return fields;
    }

    /**
     * 转换文本（替换柔性为瑞仕格医疗）
     */
    private static String transformText(String text) {
        if (text == null || text.equals("NULL")) {
            return text;
        }

        String result = text;

        // 如果有引号，先去除再处理
        boolean hasQuotes = false;
        if (result.startsWith("'") && result.endsWith("'")) {
            result = result.substring(1, result.length() - 1);
            hasQuotes = true;
        }

        // 执行替换
        result = result.replace("(柔性)", "(瑞仕格医疗)");
        result = result.replace("（柔性）", "（瑞仕格医疗）");
        result = result.replace("柔性物流", "瑞仕格医疗");
        result = result.replace("柔性项目", "瑞仕格医疗项目");
        result = result.replace("库卡柔性", "瑞仕格医疗");

        // 恢复引号
        if (hasQuotes) {
            result = "'" + result + "'";
        }

        return result;
    }

    /**
     * 生成16位ID
     */
    private static String generateId() {
        currentTimestamp += 1000;
        String idStr = String.valueOf(currentTimestamp);

        // 确保是16位
        while (idStr.length() < 16) {
            idStr = idStr + "0";
        }

        return idStr.substring(0, 16);
    }

    /**
     * role_menu转换结果
     */
    private static class RoleMenuTransformResult {
        final String sql;
        final boolean roleIdReplaced;

        RoleMenuTransformResult(String sql, boolean roleIdReplaced) {
            this.sql = sql;
            this.roleIdReplaced = roleIdReplaced;
        }
    }

    /**
     * 测试方法：检查role_menu文件的内容格式
     */
    public static void testRoleMenuFile() {
        try {
            System.out.println("====== 测试role_menu文件格式 ======");
            BufferedReader reader = new BufferedReader(
                    new InputStreamReader(new FileInputStream("D:\\Users\\ex_wuyh42.CN\\Desktop\\role_menu.txt"), SOURCE_CHARSET)
            );

            String line;
            int lineCount = 0;
            int insertLineCount = 0;

            while ((line = reader.readLine()) != null && lineCount < 10) {
                lineCount++;
                System.out.println("第" + lineCount + "行: " + line);

                if (line.contains("INSERT INTO")) {
                    insertLineCount++;
                    System.out.println("  -> 这是INSERT语句");

                    // 检查是否包含role_menu
                    if (line.contains("role_menu")) {
                        System.out.println("  -> 包含role_menu");
                    }

                    // 检查VALUES位置
                    int valuesPos = line.indexOf("VALUES(");
                    if (valuesPos != -1) {
                        System.out.println("  -> VALUES位置: " + valuesPos);
                        String valuesContent = line.substring(valuesPos + 7, line.indexOf(");"));
                        System.out.println("  -> VALUES内容: [" + valuesContent + "]");

                        String[] parts = valuesContent.split(",");
                        System.out.println("  -> 字段数量: " + parts.length);
                        for (int i = 0; i < parts.length; i++) {
                            System.out.println("    字段[" + i + "]: [" + parts[i].trim() + "]");
                        }
                    }
                }
            }

            reader.close();
            System.out.println("总共检查了 " + lineCount + " 行，其中 " + insertLineCount + " 行是INSERT语句");

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 简化的测试方法：只处理role_menu，手动添加一些测试映射
     */
    public static void testRoleMenuTransformOnly() {
        try {
            System.out.println("====== 测试role_menu转换（手动映射） ======");

            // 手动添加一些测试映射关系
            roleIdMapping.put("1014922322026430464", "1751000001000000");
            roleIdMapping.put("1016087142964658176", "1751000002000000");
            roleIdMapping.put("1014924157168648192", "1751000003000000");
            roleIdMapping.put("1014925942243459072", "1751000004000000");
            roleIdMapping.put("1014929939297730560", "1751000005000000");
            roleIdMapping.put("1014930148933238784", "1751000006000000");
            roleIdMapping.put("1014930421818851328", "1751000007000000");

            System.out.println("添加了 " + roleIdMapping.size() + " 个测试映射关系");

            // 处理role_menu文件
            processRoleMenuFile();

            System.out.println("====== 测试完成 ======");

        } catch (Exception e) {
            System.err.println("测试失败: " + e.getMessage());
            e.printStackTrace();
        }
    }
}