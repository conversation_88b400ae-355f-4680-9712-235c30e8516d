package org.my.util;
/**
 * 一个工具类，用于在控制台打印两类特定的特殊字符。
 * 警告：此版本将直接输出原始的控制字符，这很可能会导致控制台显示混乱、
 * 格式错乱，甚至触发系统提示音。
 *
 * 1. XML 1.0 规范中非法的字符。
 * 2. 从 utf8mb4 写入 utf8 数据库字段时会导致错误的增补平面字符（以示例形式）。
 */
public final class SpecialCharacterPrinter {

    public static void main(String[] args) {
        printXmlInvalidChars();
        System.out.println("\n================================================================================\n");
        printSupplementaryChars();
    }

    /**
     * 打印第一类：XML 1.0 规范中定义的非法字符。
     */
    public static void printXmlInvalidChars() {
        System.out.println("--- 场景一：XML 1.0 校验中不能通过的特殊字符 ---");
        System.out.println(">>> 警告：正在直接输出原始控制字符，预期输出会非常混乱。");
        System.out.println("--------------------------------------------------------------------------------");
        System.out.printf("%-6s | %-10s | %-35s | %s%n", "字符", "十六进制", "Unicode 名称", "描述");
        System.out.println("--------------------------------------------------------------------------------");

        // 1. C0 控制字符 (0x00-0x1F)，排除合法的 0x09, 0x0A, 0x0D
        for (int codePoint = 0x00; codePoint <= 0x1F; codePoint++) {
            if (codePoint == 0x09 || codePoint == 0x0A || codePoint == 0x0D) {
                continue; // 跳过合法的 Tab, LF, CR
            }
            printRow(codePoint, "C0 Control Character");
        }

        // 2. DEL 字符和 C1 控制字符 (0x7F-0x9F)
        for (int codePoint = 0x7F; codePoint <= 0x9F; codePoint++) {
            printRow(codePoint, "DEL & C1 Control Character");
        }

        // 3. Unicode 非字符
        printRow(0xFFFE, "Unicode Non-character");
        printRow(0xFFFF, "Unicode Non-character");
    }

    /**
     * 打印第二类：Unicode增补平面中的字符示例。
     */
    public static void printSupplementaryChars() {
        System.out.println("--- 场景二：写入 utf8 字段错误的特殊字符 (4字节UTF-8字符示例) ---");
        System.out.println(">>> 实际范围是 U+10000 到 U+10FFFF 的所有字符，以下仅为常见示例。");
        System.out.println("--------------------------------------------------------------------------------");
        System.out.printf("%-6s | %-10s | %-35s | %s%n", "字符", "十六进制", "Unicode 名称", "描述");
        System.out.println("--------------------------------------------------------------------------------");

        int[] exampleCodePoints = {
                0x1F600, // 😀 GRINNING FACE
                0x1F602, // 😂 FACE WITH TEARS OF JOY
                0x1F44D, // 👍 THUMBS UP
        };

        for (int codePoint : exampleCodePoints) {
            printRow(codePoint, "Supplementary Plane Character");
        }
    }

    /**
     * 统一打印一行的信息
     * @param codePoint Unicode码点
     * @param description 描述
     */
    private static void printRow(int codePoint, String description) {
        String charRepresentation = getRawCharacterRepresentation(codePoint);
        String hex = String.format(codePoint < 0x10000 ? "U+%04X" : "U+%05X", codePoint);
        String name = Character.getName(codePoint);
        if (name == null) {
            name = "<control>";
        }
        System.out.printf("%-6s | %-10s | %-35s | %s%n", charRepresentation, hex, name, description);
    }

    /**
     * 获取一个码点的原始字符串表示。
     * @param codePoint Unicode码点
     * @return 字符串表示
     */
    private static String getRawCharacterRepresentation(int codePoint) {
        // 对于非字符，它们没有字形，仍然使用占位符
        if (codePoint == 0xFFFE || codePoint == 0xFFFF) {
            return "[非字符]";
        }
        // 对于所有其他码点，包括控制字符和增补平面字符，
        // 都直接转换为String。这是本次的核心改动。
        return new String(Character.toChars(codePoint));
    }
}