package org.my.util;

import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 产品税收编码配置SQL生成器
 * 基于已知的Excel数据生成INSERT语句
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since 2025-06-23
 */
public class ProductTaxSqlGenerator {

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * 生成产品税收编码配置的INSERT语句
     */
    public static void generateProductTaxSettingInserts() throws IOException {
        String outputPath = "E:\\output\\product_tax_setting_insert.sql";
        String tableName = "pam_ctc.product_tax_setting";
        
        // 基于Excel数据手动构建数据
        List<ProductTaxData> dataList = buildProductTaxData();
        
        // 生成SQL语句
        List<String> sqlStatements = generateInsertStatements(dataList, tableName);
        
        // 保存到文件
        saveSqlToFile(sqlStatements, outputPath);
        
        System.out.println("SQL文件已生成: " + outputPath);
        System.out.println("共生成 " + sqlStatements.size() + " 条INSERT语句");
    }

    /**
     * 构建产品税收数据
     */
    private static List<ProductTaxData> buildProductTaxData() {
        List<ProductTaxData> dataList = new ArrayList<>();

        // 基于Excel数据添加所有记录
        dataList.add(new ProductTaxData("202506210001", "101009", "1060101060000000000", "再生纸浆",
            "2022-09-06 00:00:00", null, null, "907289759775195136", "2022-09-06 10:12:20",
            null, "梁庆垒", null, "0", null));

        dataList.add(new ProductTaxData("202506210002", "101009", "1090110040000000000", "焊接设备用零件",
            "2022-09-06 00:00:00", null, null, "907289759775195136", "2022-09-06 10:12:48",
            null, "梁庆垒", null, "0", null));

        dataList.add(new ProductTaxData("202506210003", "101009", "1090110040000000000", "焊接设备用零件（零税率）",
            "2022-09-06 00:00:00", null, null, "907289759775195136", "2022-09-06 10:14:47",
            null, "梁庆垒", null, "0", null));

        dataList.add(new ProductTaxData("202506210004", "101009", "1090110030000000000", "铆焊机械",
            "2022-09-06 00:00:00", null, null, "907289759775195136", "2022-09-06 10:15:09",
            null, "梁庆垒", null, "0", null));

        dataList.add(new ProductTaxData("202506210005", "101009", "1090110030000000000", "铆焊机械（零税率）",
            "2022-09-06 00:00:00", null, null, "907289759775195136", "2022-09-06 10:15:31",
            null, "梁庆垒", null, "0", null));

        dataList.add(new ProductTaxData("202506210006", "101009", "1090114010000000000", "输送机械（输送机和提升机）",
            "2022-09-06 00:00:00", null, null, "907289759775195136", "2022-09-06 10:15:56",
            null, "梁庆垒", null, "0", null));

        dataList.add(new ProductTaxData("202506210007", "101009", "1090114010000000000", "输送机械（输送机和提升机）（零税率）",
            "2022-09-06 00:00:00", null, null, "907289759775195136", "2022-09-06 10:16:14",
            null, "梁庆垒", null, "0", null));

        dataList.add(new ProductTaxData("202506210008", "101009", "1090121020000000000", "气动系统及机械",
            "2022-09-06 00:00:00", null, null, "907289759775195136", "2022-09-06 10:16:37",
            null, "梁庆垒", null, "0", null));

        dataList.add(new ProductTaxData("202506210009", "101009", "1090116010000000000", "立体（高架）仓库存储系统",
            "2022-09-06 00:00:00", null, null, "907289759775195136", "2022-09-06 10:16:38",
            null, "梁庆垒", null, "0", null));

        dataList.add(new ProductTaxData("202506210010", "101009", "1090116010000000000", "立体（高架）仓库存储系统（零税率）",
            "2022-09-06 00:00:00", null, null, "907289759775195136", "2022-09-06 10:17:08",
            null, "梁庆垒", null, "0", null));

        return dataList;
    }

    /**
     * 生成INSERT语句列表
     */
    private static List<String> generateInsertStatements(List<ProductTaxData> dataList, String tableName) {
        List<String> sqlStatements = new ArrayList<>();
        
        String[] fields = {
            "id", "ou_id", "tax_code", "tax_name", "spec_business_tag", 
            "start_time", "end_time", "remark", "create_by", "create_at", 
            "update_by", "create_user_name", "update_at", "deleted_flag"
        };
        
        String fieldsPart = String.join(", ", fields);
        String insertPrefix = "INSERT INTO " + tableName + " (" + fieldsPart + ") VALUES ";
        
        for (ProductTaxData data : dataList) {
            StringBuilder valuesPart = new StringBuilder("(");
            
            valuesPart.append("'").append(data.id).append("', ");
            valuesPart.append(data.ouId).append(", ");
            valuesPart.append("'").append(data.taxCode).append("', ");
            valuesPart.append("'").append(escapeSqlString(data.taxName)).append("', ");
            valuesPart.append(data.specBusinessTag != null ? data.specBusinessTag : "NULL").append(", ");
            valuesPart.append(data.startTime != null ? "'" + data.startTime + "'" : "NULL").append(", ");
            valuesPart.append(data.endTime != null ? "'" + data.endTime + "'" : "NULL").append(", ");
            valuesPart.append(data.remark != null ? "'" + escapeSqlString(data.remark) + "'" : "NULL").append(", ");
            valuesPart.append(data.createBy).append(", ");
            valuesPart.append(data.createAt != null ? "'" + data.createAt + "'" : "NULL").append(", ");
            valuesPart.append(data.updateBy != null ? data.updateBy : "NULL").append(", ");
            valuesPart.append(data.createUserName != null ? "'" + escapeSqlString(data.createUserName) + "'" : "NULL").append(", ");
            valuesPart.append(data.updateAt != null ? "'" + data.updateAt + "'" : "NULL").append(", ");
            valuesPart.append(data.deletedFlag);
            
            valuesPart.append(")");
            sqlStatements.add(insertPrefix + valuesPart.toString() + ";");
        }
        
        return sqlStatements;
    }

    /**
     * SQL字符串转义
     */
    private static String escapeSqlString(String value) {
        if (value == null) {
            return "";
        }
        return value.replace("'", "''");
    }

    /**
     * 将SQL语句保存到文件
     */
    private static void saveSqlToFile(List<String> sqlStatements, String outputPath) throws IOException {
        String parentDir = Paths.get(outputPath).getParent().toString();
        Files.createDirectories(Paths.get(parentDir));
        
        try (FileWriter writer = new FileWriter(outputPath, false)) {
            writer.write("-- =====================================================\n");
            writer.write("-- 产品税收编码配置数据INSERT语句\n");
            writer.write("-- =====================================================\n");
            writer.write("-- 生成时间: " + dateFormat.format(new Date()) + "\n");
            writer.write("-- 记录数量: " + sqlStatements.size() + "\n");
            writer.write("-- 生成工具: ProductTaxSqlGenerator v1.0\n");
            writer.write("-- =====================================================\n\n");
            
            writer.write("-- 开始事务\n");
            writer.write("START TRANSACTION;\n\n");
            
            for (int i = 0; i < sqlStatements.size(); i++) {
                if (i % 10 == 0) {
                    writer.write("-- Batch " + (i / 10 + 1) + "\n");
                }
                writer.write(sqlStatements.get(i) + "\n");
                if ((i + 1) % 10 == 0 && i < sqlStatements.size() - 1) {
                    writer.write("\n");
                }
            }
            
            writer.write("\n-- 提交事务\n");
            writer.write("COMMIT;\n\n");
            writer.write("-- 文件结束\n");
            
            writer.flush();
        }
    }

    /**
     * 产品税收数据类
     */
    static class ProductTaxData {
        String id;
        String ouId;
        String taxCode;
        String taxName;
        String specBusinessTag;
        String startTime;
        String endTime;
        String remark;
        String createBy;
        String createAt;
        String updateBy;
        String createUserName;
        String updateAt;
        String deletedFlag;

        public ProductTaxData(String id, String ouId, String taxCode, String taxName, 
                             String startTime, String endTime, String remark, String createBy, 
                             String createAt, String updateBy, String createUserName, 
                             String updateAt, String deletedFlag, String specBusinessTag) {
            this.id = id;
            this.ouId = ouId;
            this.taxCode = taxCode;
            this.taxName = taxName;
            this.startTime = startTime;
            this.endTime = endTime;
            this.remark = remark;
            this.createBy = createBy;
            this.createAt = createAt;
            this.updateBy = updateBy;
            this.createUserName = createUserName;
            this.updateAt = updateAt;
            this.deletedFlag = deletedFlag;
            this.specBusinessTag = specBusinessTag;
        }
    }

    /**
     * 主方法
     */
    public static void main(String[] args) {
        try {
            generateProductTaxSettingInserts();
        } catch (Exception e) {
            System.err.println("生成SQL过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
