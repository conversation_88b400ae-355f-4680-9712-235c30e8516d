package org.my.util;

import java.text.SimpleDateFormat;
import java.util.Calendar;

public class DateFormatTest {

    public static void main(String[] args) {
        // 使用Calendar获取一个日期实例
        Calendar calendar = Calendar.getInstance();
        // 假设日期是2023年12月31日
        calendar.set(2024, Calendar.UNDECIMBER, 29);

        // 使用YYYY格式化日期
        SimpleDateFormat formatWithYYYY = new SimpleDateFormat("YYYY-MM-dd");
        String dateWithYYYY = formatWithYYYY.format(calendar.getTime());
        System.out.println("Date with YYYY: " + dateWithYYYY);
        // 输出的是2024-12-31，因为12月31日属于第1周，而该周属于下一年

        // 使用yyyy格式化日期
        SimpleDateFormat formatWithyyyy = new SimpleDateFormat("yyMMdd");
        String dateWithyyyy = formatWithyyyy.format(calendar.getTime());
        System.out.println("Date with yyyy: " + dateWithyyyy);
        // 输出2023-12-31
    }

}
