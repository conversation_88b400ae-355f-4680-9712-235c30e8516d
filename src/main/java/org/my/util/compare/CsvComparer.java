package org.my.util.compare;

import com.opencsv.CSVReader;
import com.opencsv.CSVReaderBuilder;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileReader;
import java.util.*;

public class CsvComparer {
    private static final Logger log = LoggerFactory.getLogger(CsvComparer.class);

    private static final String[] HEADERS = {
            "project_id", "project_code", "purchase_order_id", "project_wbs_receipts_id",
            "num", "requirement_code", "pam_code", "erp_code", "order_num", "storage_count",
            "cancel_num", "discount_price", "po_total_amount", "po_type", "activity_code",
            "wbs_summary_code", "data_time", "vendor_name", "materiel_descr"
    };

    public static boolean compareCSVFiles(String file1Path, String file2Path) {
        log.info("开始比较CSV文件");
        log.info("文件1路径: {}", file1Path);
        log.info("文件2路径: {}", file2Path);

        try {
            log.info("开始读取文件1");
            Map<String, Map<String, String>> dataMap1 = readCSVToMap(file1Path);
            log.info("文件1读取完成，共读取 {} 条记录", dataMap1.size());

            log.info("开始读取文件2");
            Map<String, Map<String, String>> dataMap2 = readCSVToMap(file2Path);
            log.info("文件2读取完成，共读取 {} 条记录", dataMap2.size());

            // 比较记录数量
            if (dataMap1.size() != dataMap2.size()) {
                log.error("记录数量不一致: 文件1={}, 文件2={}", dataMap1.size(), dataMap2.size());
                return false;
            }
            log.info("记录数量一致，开始比较数据内容");

            // 比较两个Map
            return compareDataMaps(dataMap1, dataMap2);

        } catch (Exception e) {
            log.error("文件处理过程发生错误", e);
            return false;
        }
    }

    private static Map<String, Map<String, String>> readCSVToMap(String filePath) throws Exception {
        Map<String, Map<String, String>> dataMap = new HashMap<>();
        int lineCount = 0;

        log.debug("开始读取文件: {}", filePath);
        try (CSVReader reader = new CSVReaderBuilder(new FileReader(filePath))
                .withSkipLines(1) // 跳过头行
                .build()) {

            String[] line;
            while ((line = reader.readNext()) != null) {
                lineCount++;
                log.debug("正在处理第 {} 行数据", lineCount);

                Map<String, String> rowData = new HashMap<>();
                StringBuilder keyBuilder = new StringBuilder();

                // 读取每一列的数据
                for (int i = 0; i < HEADERS.length && i < line.length; i++) {
                    String value = line[i].trim();
                    // 处理数值类型，统一格式
                    if (isNumeric(value)) {
                        value = normalizeNumber(value);
                        log.trace("数值标准化: {} -> {}", line[i], value);
                    }
                    rowData.put(HEADERS[i], value);

                    // 构建组合key
                    if (keyBuilder.length() > 0) {
                        keyBuilder.append("|");
                    }
                    keyBuilder.append(value);
                }

                String compositeKey = keyBuilder.toString();
                if (dataMap.containsKey(compositeKey)) {
                    log.warn("发现重复记录，行号: {}", lineCount);
                }
                dataMap.put(compositeKey, rowData);
            }
        }
        log.info("文件读取完成，共处理 {} 行数据", lineCount);
        return dataMap;
    }

    private static boolean compareDataMaps(Map<String, Map<String, String>> dataMap1,
                                           Map<String, Map<String, String>> dataMap2) {
        log.info("开始比较数据内容");
        int processedCount = 0;

        for (String key : dataMap1.keySet()) {
            processedCount++;
            if (processedCount % 1000 == 0) {
                log.info("已比较 {} 条记录", processedCount);
            }

            if (!dataMap2.containsKey(key)) {
                log.error("在第二个文件中未找到匹配的记录");
                Map<String, String> record = dataMap1.get(key);
                log.error("未匹配的记录详情:");
                record.forEach((header, value) -> log.error("{}: {}", header, value));
                return false;
            }

            // 比较两条记录的所有字段值
            Map<String, String> record1 = dataMap1.get(key);
            Map<String, String> record2 = dataMap2.get(key);

            for (String header : HEADERS) {
                String value1 = record1.get(header);
                String value2 = record2.get(header);

                if (!Objects.equals(value1, value2)) {
                    log.error("发现不一致的字段: {}", header);
                    log.error("文件1的值: {}", value1);
                    log.error("文件2的值: {}", value2);
                    return false;
                }
            }
        }

        log.info("数据比较完成，全部记录一致");
        return true;
    }

    private static boolean isNumeric(String str) {
        if (str == null || str.trim().isEmpty()) {
            return false;
        }
        try {
            Double.parseDouble(str);
            return true;
        } catch (NumberFormatException e) {
            return false;
        }
    }

    private static String normalizeNumber(String number) {
        try {
            double value = Double.parseDouble(number);
            // 处理整数
            if (value == (long) value) {
                return String.format("%.0f", value);
            }
            // 处理小数，保留6位小数
            return String.format("%.6f", value);
        } catch (NumberFormatException e) {
            return number;
        }
    }

    public static void main(String[] args) {
        log.info("程序开始执行");
        String file1Path = "E:/beforce.csv";
        String file2Path = "E:/after.csv";

        boolean areEqual = compareCSVFiles(file1Path, file2Path);
        if (areEqual) {
            log.info("比较结果: 两个CSV文件内容完全一致");
        } else {
            log.error("比较结果: 两个CSV文件内容不一致");
        }
        log.info("程序执行完成");
    }
}