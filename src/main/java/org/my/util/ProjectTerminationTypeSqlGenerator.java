package org.my.util;

import java.io.FileWriter;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 项目终止分类配置SQL生成器
 * 基于已知的Excel数据生成INSERT语句
 * 
 * <AUTHOR> Team
 * @version 1.0
 * @since 2025-06-23
 */
public class ProjectTerminationTypeSqlGenerator {

    /**
     * 生成项目终止分类配置的INSERT语句
     */
    public static void generateProjectTerminationTypeInserts() throws IOException {
        String outputPath = "E:\\output\\project_termination_type_insert.sql";
        String tableName = "pam_ctc.project_termination_type";
        
        // 基于Excel数据手动构建数据
        List<ProjectTerminationTypeData> dataList = buildProjectTerminationTypeData();
        
        // 生成SQL语句
        List<String> sqlStatements = generateInsertStatements(dataList, tableName);
        
        // 保存到文件
        saveSqlToFile(sqlStatements, outputPath);
        
        System.out.println("SQL文件已生成: " + outputPath);
        System.out.println("共生成 " + sqlStatements.size() + " 条INSERT语句");
    }

    /**
     * 构建项目终止分类数据
     */
    private static List<ProjectTerminationTypeData> buildProjectTerminationTypeData() {
        List<ProjectTerminationTypeData> dataList = new ArrayList<>();
        
        // 基于Excel数据添加所有记录
        dataList.add(new ProjectTerminationTypeData(
            "202506210001", 
            "1384556177898926080", 
            "项目废弃", 
            "R03", 
            "项目废弃：项目没有成本发生，如及时发现项目重复/项目类型错误", 
            "-1", 
            "2020-09-02 09:27:14", 
            null, 
            null, 
            "0"
        ));
        
        dataList.add(new ProjectTerminationTypeData(
            "202506210002", 
            "1384556177898926080", 
            "项目终止", 
            "R02", 
            "项目终止：项目已发生成本，成本需要本项目承担或者调账至其他项目", 
            "-1", 
            "2020-06-12 22:27:14", 
            "-1", 
            "2020-12-18 18:05:42", 
            "0"
        ));
        
        return dataList;
    }

    /**
     * 生成INSERT语句列表
     */
    private static List<String> generateInsertStatements(List<ProjectTerminationTypeData> dataList, String tableName) {
        List<String> sqlStatements = new ArrayList<>();
        
        String[] fields = {
            "id", "unit_id", "termination_name", "termination_code", "warning_des", 
            "create_by", "create_at", "update_by", "update_at", "delete_flag"
        };
        
        String fieldsPart = String.join(", ", fields);
        String insertPrefix = "INSERT INTO " + tableName + " (" + fieldsPart + ") VALUES ";
        
        for (ProjectTerminationTypeData data : dataList) {
            StringBuilder valuesPart = new StringBuilder("(");
            
            valuesPart.append("'").append(data.id).append("', ");
            valuesPart.append(data.unitId).append(", ");
            valuesPart.append("'").append(escapeSqlString(data.terminationName)).append("', ");
            valuesPart.append("'").append(escapeSqlString(data.terminationCode)).append("', ");
            valuesPart.append("'").append(escapeSqlString(data.warningDes)).append("', ");
            valuesPart.append(data.createBy).append(", ");
            valuesPart.append(data.createAt != null ? "'" + data.createAt + "'" : "NULL").append(", ");
            valuesPart.append(data.updateBy != null ? data.updateBy : "NULL").append(", ");
            valuesPart.append(data.updateAt != null ? "'" + data.updateAt + "'" : "NULL").append(", ");
            valuesPart.append(data.deleteFlag);
            
            valuesPart.append(")");
            sqlStatements.add(insertPrefix + valuesPart.toString() + ";");
        }
        
        return sqlStatements;
    }

    /**
     * SQL字符串转义
     */
    private static String escapeSqlString(String value) {
        if (value == null) {
            return "";
        }
        return value
            .replace("\\", "\\\\")
            .replace("'", "''")
            .replace("\"", "\\\"")
            .replace("\n", "\\n")
            .replace("\r", "\\r")
            .replace("\t", "\\t");
    }

    /**
     * 将SQL语句保存到文件（无注释版本）
     */
    private static void saveSqlToFile(List<String> sqlStatements, String outputPath) throws IOException {
        String parentDir = Paths.get(outputPath).getParent().toString();
        Files.createDirectories(Paths.get(parentDir));

        try (java.io.OutputStreamWriter writer = new java.io.OutputStreamWriter(
                new java.io.FileOutputStream(outputPath), "UTF-8")) {
            for (String sql : sqlStatements) {
                writer.write(sql + "\n");
            }
            writer.flush();
        }
    }

    /**
     * 项目终止分类数据类
     */
    static class ProjectTerminationTypeData {
        String id;
        String unitId;
        String terminationName;
        String terminationCode;
        String warningDes;
        String createBy;
        String createAt;
        String updateBy;
        String updateAt;
        String deleteFlag;

        public ProjectTerminationTypeData(String id, String unitId, String terminationName, String terminationCode, 
                                        String warningDes, String createBy, String createAt, String updateBy, 
                                        String updateAt, String deleteFlag) {
            this.id = id;
            this.unitId = unitId;
            this.terminationName = terminationName;
            this.terminationCode = terminationCode;
            this.warningDes = warningDes;
            this.createBy = createBy;
            this.createAt = createAt;
            this.updateBy = updateBy;
            this.updateAt = updateAt;
            this.deleteFlag = deleteFlag;
        }
    }

    /**
     * 主方法
     */
    public static void main(String[] args) {
        try {
            generateProjectTerminationTypeInserts();
        } catch (Exception e) {
            System.err.println("生成SQL过程中发生错误: " + e.getMessage());
            e.printStackTrace();
        }
    }
}
