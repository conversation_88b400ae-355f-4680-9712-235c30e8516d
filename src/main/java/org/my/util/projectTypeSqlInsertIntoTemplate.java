package org.my.util;

import com.google.common.collect.Lists;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.concurrent.atomic.AtomicInteger;

public class projectTypeSqlInsertIntoTemplate {

    public static void main(String[] args) {
        String template = "INSERT INTO pam_ctc.project_type_check_rel" +
                " (ID, project_type_id, check_item_id, yes_or_no, CREATE_BY, CREATE_AT, UPDATE_BY, UPDATE_AT, VERSION)" +
                " VALUES(%s, %s, 1324380607388708864, 1, 605063861594226688, now(), 605063861594226688, now(), 0);";

        ArrayList<Long> projectTypeIdList = Lists.newArrayList(
                605039440783474688L,
                605045424591470592L,
                605045706410950656L,
                605046317999194112L,
                605046560618708992L,
                605047144012840960L,
                605047507075989504L,
                605047722822598656L,
                605050226478153728L,
                605051002017546240L,
                605051492524621824L,
                605051911795638272L,
                605052086547120128L,
                618017268428701696L,
                618017882747437056L,
                618018401989689344L,
                618018803388776448L,
                618019338787487744L,
                618020029195091968L,
                618020489972940800L,
                618804166369738752L,
                618805461604368384L,
                629760732287729664L,
                629766352067887104L,
                629766914532442112L,
                666307380941684736L,
                674648588181045248L,
                677877284476026880L,
                677878033247371264L,
                705504212829601792L,
                705504883670777856L,
                721131828219478016L,
                773843647723470848L,
                783382558992760832L,
                789522293704359936L,
                789525576737095680L,
                789525987573366784L,
                789526403350528000L,
                803556028716154880L,
                814201790982520832L,
                814205555764625408L,
                814206932712685568L,
                814208685482967040L,
                814209725796188160L,
                817089902272315392L,
                817091325261250560L,
                817095107080617984L,
                817095403777294336L,
                817096476298575872L,
                818777819038351360L,
                818781844613890048L,
                823599844193992704L,
                823601686256812032L,
                824576649474342912L,
                827205759296077824L,
                844633709410779136L,
                844634780279504896L,
                844945589132066816L,
                844946763004510208L,
                846330123123687424L,
                847395378058756096L,
                847422148870930432L,
                847422668373229568L,
                847423896868093952L,
                847424600760385536L,
                847425292220760064L,
                847425945756237824L,
                847426211121463296L,
                847426394735509504L,
                847427395752296448L,
                869188597457092608L,
                872805758150901760L,
                874603613379362816L,
                883076182625484800L,
                883077353176039424L,
                883079104251822080L,
                883080747252973568L,
                883082123190206464L,
                883344684544425984L,
                883348571091369984L,
                883350383605317632L,
                885158008437342208L,
                885177671351271424L,
                885179016707833856L,
                885180370255872000L,
                885187457321009152L,
                885189081800114176L,
                885194317092618240L,
                885196181993422848L,
                885197494403727360L,
                885198720855965696L,
                885202609432952832L,
                885445187386474496L,
                899948775806599168L,
                920498754295955456L,
                967718732623249408L,
                978759794745671680L,
                994710143847366656L,
                1012747525314052096L,
                1012751245489733632L,
                1012752691169853440L,
                1012753424652959744L,
                1012755413017296896L,
                1012763417963921408L,
                1017861700281434112L,
                1037322764911902720L,
                1041778616368627712L,
                1077590255047016448L,
                1089846077101703168L,
                1110616955989000192L,
                1110889016279957504L,
                1110957075564855296L,
                1110958778351616000L,
                1110960133476712448L,
                1110995944859500544L,
                1128266349471072256L,
                1136622825407250432L,
                1172129166251159552L,
                1197103048275673088L,
                1240726105557000192L,
                1283065170184355840L,
                1296150994405040128L,
                1296152959205777408L,
                1296153479471157248L,
                1296154027574697984L,
                1296156756848955392L,
                1296157464261451776L,
                1302997113005260800L,
                1303018367124881408L
        );
        StringBuilder sb = new StringBuilder();
        for (Long projectTypeId : projectTypeIdList) {
            sb.append(String.format(template, generateId(), projectTypeId));
            sb.append("\n");
        }
        System.out.println(sb);
    }

    private static final AtomicInteger counter = new AtomicInteger(0);
    private static String lastGeneratedId = "";
    private static final Object lock = new Object();

    public static String generateId() {
        synchronized (lock) {
            String newId;
            do {
                // 获取当前时间
                LocalDateTime now = LocalDateTime.now();

                // 格式化年月日时分秒
                String timeStr = now.format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));

                // 获取计数器值并递增（0-999循环）
                int count = counter.getAndUpdate(n -> (n + 1) % 1000);

                // 组合最终ID：时间戳 + 3位序号
                newId = timeStr + String.format("%03d", count);

            } while (newId.equals(lastGeneratedId));

            // 保存最后生成的ID
            lastGeneratedId = newId;
            return newId;
        }
    }

}
