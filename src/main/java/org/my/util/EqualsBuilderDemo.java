package org.my.util;

import org.apache.commons.lang3.builder.EqualsBuilder;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Objects;

/**
 * EqualsBuilder多值比较示例
 * 演示如何使用Apache Commons Lang的EqualsBuilder来比较对象的多个字段
 */
public class EqualsBuilderDemo {

    public static void main(String[] args) {

        BigDecimal number = new BigDecimal("123.456");
        String result = number.setScale(0, BigDecimal.ROUND_DOWN).toString();
        System.out.println(result);
        // 创建测试对象
        Person person1 = new Person("张三", 30, "开发工程师", "北京");
        Person person2 = new Person("张三", 30, "开发工程师", "北京");
        Person person3 = new Person("张三", 35, "产品经理", "广州");

        System.out.println("=== 方法一：使用append方法逐个比较字段 ===");

        // 比较name字段
        boolean nameEquals = new EqualsBuilder()
                .append(person1.getName(), person2.getName())
                .isEquals();
        System.out.println("person1与person2的name字段相等: " + nameEquals);

        // 比较age字段
        boolean ageEquals = new EqualsBuilder()
                .append(person1.getAge(), person2.getAge())
                .isEquals();
        System.out.println("person1与person2的age字段相等: " + ageEquals);

        // 比较job字段
        boolean jobEquals = new EqualsBuilder()
                .append(person1.getJob(), person2.getJob())
                .isEquals();
        System.out.println("person1与person2的job字段相等: " + jobEquals);

        // 多字段"或"比较（任一字段相等即为true）
        boolean anyFieldsEqual = nameEquals || ageEquals || jobEquals;
        System.out.println("person1与person2的任一字段相等: " + anyFieldsEqual);

        // 多字段"与"比较（使用链式调用，所有字段都相等才为true）
        boolean allFieldsEqual = new EqualsBuilder()
                .append(person1.getName(), person2.getName())
                .append(person1.getAge(), person2.getAge())
                .append(person1.getJob(), person2.getJob())
                .isEquals();
        System.out.println("person1与person2的所有比较字段都相等: " + allFieldsEqual);

        System.out.println("\n=== 方法二：使用reflectionEquals进行反射比较 ===");

        // 比较所有字段
        boolean allEquals = EqualsBuilder.reflectionEquals(person1, person2);
        System.out.println("person1与person2的所有字段相等: " + allEquals);

        // 比较除了指定字段外的所有字段
        boolean equalsExcludeAddress = EqualsBuilder.reflectionEquals(person1, person2, "address");
        System.out.println("person1与person2除address外的所有字段相等: " + equalsExcludeAddress);

        // 自定义方法：比较任意字段是否相等（OR条件）
        System.out.println("\n=== 方法三：自定义方法实现任意字段OR比较 ===");
        boolean anyFieldEqual = anyFieldEquals(person1, person3, "name", "age", "job");
        System.out.println("person1与person3的任一指定字段相等: " + anyFieldEqual);

        // 查找哪些字段相等
        System.out.println("\n=== 哪些字段相等 ===");
        checkEqualFields(person1, person3, "name", "age", "job", "address");
    }

    /**
     * 自定义方法：检查两个对象的指定字段中是否有任一字段相等
     * @param a 对象a
     * @param b 对象b
     * @param fields 要比较的字段名数组
     * @return 如果任一字段相等则返回true
     */
    public static boolean anyFieldEquals(Object a, Object b, String... fields) {
        if (a == null || b == null || a.getClass() != b.getClass()) {
            return false;
        }

        for (String field : fields) {
            try {
                java.lang.reflect.Field f = a.getClass().getDeclaredField(field);
                f.setAccessible(true);
                Object valueA = f.get(a);
                Object valueB = f.get(b);

                boolean equals = new EqualsBuilder().append(valueA, valueB).isEquals();
                if (equals) {
                    return true;
                }
            } catch (Exception e) {
                System.err.println("比较字段时出错: " + e.getMessage());
            }
        }
        return false;
    }

    /**
     * 检查并输出哪些字段相等
     * @param a 对象a
     * @param b 对象b
     * @param fields 要检查的字段
     */
    public static void checkEqualFields(Object a, Object b, String... fields) {
        if (a == null || b == null || a.getClass() != b.getClass()) {
            System.out.println("对象不可比较");
            return;
        }

        for (String field : fields) {
            try {
                java.lang.reflect.Field f = a.getClass().getDeclaredField(field);
                f.setAccessible(true);
                Object valueA = f.get(a);
                Object valueB = f.get(b);

                boolean equals = new EqualsBuilder().append(valueA, valueB).isEquals();
                System.out.println("字段 '" + field + "' 相等: " + equals +
                        " (值: " + valueA + " vs " + valueB + ")");
            } catch (Exception e) {
                System.err.println("检查字段时出错: " + e.getMessage());
            }
        }
    }
}

/**
 * 示例用的Person类
 */
class Person {
    private String name;
    private int age;
    private String job;
    private String address;

    public Person(String name, int age, String job, String address) {
        this.name = name;
        this.age = age;
        this.job = job;
        this.address = address;
    }

    // Getters和Setters
    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public String getJob() {
        return job;
    }

    public void setJob(String job) {
        this.job = job;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }
}