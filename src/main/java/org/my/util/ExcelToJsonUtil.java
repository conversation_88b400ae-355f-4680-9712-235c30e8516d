package org.my.util;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;

import java.io.FileInputStream;
import java.io.IOException;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.*;

public class ExcelToJsonUtil {

    private static final ObjectMapper objectMapper = new ObjectMapper();
    private static final DecimalFormat decimalFormat = new DecimalFormat("#.##########");
    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    public static void main(String[] args) throws IOException {
        String json = convertExcelToJson("D:\\Users\\ex_wuyh42\\Downloads\\核算中台A4接口表-关联交易接口20250527.xlsx");
        System.out.println(json);
    }

    /**
     * 读取Excel文件并转换为JSON字符串
     *
     * @param filePath Excel文件路径
     * @return JSON字符串，包含所有sheet的数据
     * @throws IOException 文件读取异常
     */
    public static String convertExcelToJson(String filePath) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath);
             XSSFWorkbook workbook = new XSSFWorkbook(fis)) {

            ObjectNode rootNode = objectMapper.createObjectNode();
            ArrayNode sheetsArray = objectMapper.createArrayNode();

            // 遍历所有sheet
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                ObjectNode sheetNode = convertSheetToJson(sheet);
                sheetsArray.add(sheetNode);
            }

            rootNode.set("sheets", sheetsArray);
            rootNode.put("totalSheets", workbook.getNumberOfSheets());

            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(rootNode);
        }
    }

    /**
     * 读取Excel文件并转换为Map对象
     *
     * @param filePath Excel文件路径
     * @return Map对象，key为sheet名称，value为该sheet的数据列表
     * @throws IOException 文件读取异常
     */
    public static Map<String, List<Map<String, Object>>> convertExcelToMap(String filePath) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath);
             XSSFWorkbook workbook = new XSSFWorkbook(fis)) {

            Map<String, List<Map<String, Object>>> result = new LinkedHashMap<>();

            // 遍历所有sheet
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                Sheet sheet = workbook.getSheetAt(i);
                List<Map<String, Object>> sheetData = convertSheetToMapList(sheet);
                result.put(sheet.getSheetName(), sheetData);
            }

            return result;
        }
    }

    /**
     * 读取指定sheet并转换为JSON字符串
     *
     * @param filePath Excel文件路径
     * @param sheetName sheet名称
     * @return JSON字符串
     * @throws IOException 文件读取异常
     */
    public static String convertSheetToJson(String filePath, String sheetName) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath);
             XSSFWorkbook workbook = new XSSFWorkbook(fis)) {

            Sheet sheet = workbook.getSheet(sheetName);
            if (sheet == null) {
                throw new IllegalArgumentException("Sheet '" + sheetName + "' not found");
            }

            ObjectNode sheetNode = convertSheetToJson(sheet);
            return objectMapper.writerWithDefaultPrettyPrinter().writeValueAsString(sheetNode);
        }
    }

    /**
     * 将Sheet转换为JSON对象节点
     *
     * @param sheet Excel Sheet对象
     * @return JSON对象节点
     */
    private static ObjectNode convertSheetToJson(Sheet sheet) {
        ObjectNode sheetNode = objectMapper.createObjectNode();
        ArrayNode dataArray = objectMapper.createArrayNode();

        List<String> headers = new ArrayList<>();
        Iterator<Row> rowIterator = sheet.iterator();

        // 读取表头
        if (rowIterator.hasNext()) {
            Row headerRow = rowIterator.next();
            for (Cell cell : headerRow) {
                String headerValue = getCellValueAsString(cell);
                headers.add(headerValue != null ? headerValue.trim() : "");
            }
        }

        // 读取数据行
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            ObjectNode rowNode = objectMapper.createObjectNode();
            boolean hasData = false;

            for (int i = 0; i < headers.size(); i++) {
                Cell cell = row.getCell(i);
                Object cellValue = getCellValue(cell);
                String header = headers.get(i);

                if (cellValue != null) {
                    hasData = true;
                    if (cellValue instanceof String) {
                        rowNode.put(header, (String) cellValue);
                    } else if (cellValue instanceof Number) {
                        rowNode.put(header, ((Number) cellValue).doubleValue());
                    } else if (cellValue instanceof Boolean) {
                        rowNode.put(header, (Boolean) cellValue);
                    } else if (cellValue instanceof Date) {
                        rowNode.put(header, dateFormat.format((Date) cellValue));
                    } else {
                        rowNode.put(header, cellValue.toString());
                    }
                } else {
                    rowNode.putNull(header);
                }
            }

            // 只添加非空行
            if (hasData) {
                dataArray.add(rowNode);
            }
        }

        sheetNode.put("sheetName", sheet.getSheetName());
        sheetNode.set("headers", objectMapper.valueToTree(headers));
        sheetNode.set("data", dataArray);
        sheetNode.put("rowCount", dataArray.size());

        return sheetNode;
    }

    /**
     * 将Sheet转换为Map列表
     *
     * @param sheet Excel Sheet对象
     * @return Map列表，每个Map代表一行数据
     */
    private static List<Map<String, Object>> convertSheetToMapList(Sheet sheet) {
        List<Map<String, Object>> result = new ArrayList<>();
        List<String> headers = new ArrayList<>();
        Iterator<Row> rowIterator = sheet.iterator();

        // 读取表头
        if (rowIterator.hasNext()) {
            Row headerRow = rowIterator.next();
            for (Cell cell : headerRow) {
                String headerValue = getCellValueAsString(cell);
                headers.add(headerValue != null ? headerValue.trim() : "");
            }
        }

        // 读取数据行
        while (rowIterator.hasNext()) {
            Row row = rowIterator.next();
            Map<String, Object> rowMap = new LinkedHashMap<>();
            boolean hasData = false;

            for (int i = 0; i < headers.size(); i++) {
                Cell cell = row.getCell(i);
                Object cellValue = getCellValue(cell);
                String header = headers.get(i);

                rowMap.put(header, cellValue);
                if (cellValue != null) {
                    hasData = true;
                }
            }

            // 只添加非空行
            if (hasData) {
                result.add(rowMap);
            }
        }

        return result;
    }

    /**
     * 获取单元格的值
     *
     * @param cell 单元格对象
     * @return 单元格值
     */
    private static Object getCellValue(Cell cell) {
        if (cell == null) {
            return null;
        }

        switch (cell.getCellType()) {
            case STRING:
                return cell.getStringCellValue().trim();
            case NUMERIC:
                if (DateUtil.isCellDateFormatted(cell)) {
                    return cell.getDateCellValue();
                } else {
                    double numericValue = cell.getNumericCellValue();
                    // 判断是否为整数
                    if (numericValue == Math.floor(numericValue)) {
                        return (long) numericValue;
                    } else {
                        return Double.parseDouble(decimalFormat.format(numericValue));
                    }
                }
            case BOOLEAN:
                return cell.getBooleanCellValue();
            case FORMULA:
                // 处理公式，获取计算后的值
                try {
                    switch (cell.getCachedFormulaResultType()) {
                        case STRING:
                            return cell.getStringCellValue().trim();
                        case NUMERIC:
                            if (DateUtil.isCellDateFormatted(cell)) {
                                return cell.getDateCellValue();
                            } else {
                                double numericValue = cell.getNumericCellValue();
                                if (numericValue == Math.floor(numericValue)) {
                                    return (long) numericValue;
                                } else {
                                    return Double.parseDouble(decimalFormat.format(numericValue));
                                }
                            }
                        case BOOLEAN:
                            return cell.getBooleanCellValue();
                        default:
                            return null;
                    }
                } catch (Exception e) {
                    return cell.getCellFormula();
                }
            case BLANK:
            case _NONE:
            default:
                return null;
        }
    }

    /**
     * 获取单元格的字符串值
     *
     * @param cell 单元格对象
     * @return 字符串值
     */
    private static String getCellValueAsString(Cell cell) {
        Object value = getCellValue(cell);
        return value != null ? value.toString() : null;
    }

    /**
     * 获取Excel文件中所有sheet的名称
     *
     * @param filePath Excel文件路径
     * @return sheet名称列表
     * @throws IOException 文件读取异常
     */
    public static List<String> getSheetNames(String filePath) throws IOException {
        try (FileInputStream fis = new FileInputStream(filePath);
             XSSFWorkbook workbook = new XSSFWorkbook(fis)) {

            List<String> sheetNames = new ArrayList<>();
            for (int i = 0; i < workbook.getNumberOfSheets(); i++) {
                sheetNames.add(workbook.getSheetAt(i).getSheetName());
            }
            return sheetNames;
        }
    }
}
