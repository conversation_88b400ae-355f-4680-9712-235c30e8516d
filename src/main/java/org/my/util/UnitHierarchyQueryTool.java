package org.my.util;

import com.zaxxer.hikari.HikariConfig;
import com.zaxxer.hikari.HikariDataSource;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileWriter;
import java.io.IOException;
import java.sql.*;
import java.util.*;

/**
 * 单位层级结构查询工具
 * 用于查询pam_basedata.unit表的层级结构数据并生成SQL
 */
public class UnitHierarchyQueryTool {

    private static final Logger logger = LoggerFactory.getLogger(UnitHierarchyQueryTool.class);

    // pam_basedata数据库连接配置
    private static final String JDBC_URL = "***********************************************************************************************************************************************";
    private static final String DB_USER = "pam_sit";
    private static final String DB_PASSWORD = "tqVhr1zM5";

    private final HikariDataSource dataSource;

    public UnitHierarchyQueryTool() {
        this.dataSource = setupDataSource();
    }

    /**
     * 设置并返回HikariCP数据源
     */
    private HikariDataSource setupDataSource() {
        HikariConfig config = new HikariConfig();
        config.setJdbcUrl(JDBC_URL);
        config.setUsername(DB_USER);
        config.setPassword(DB_PASSWORD);
        config.setMaximumPoolSize(10);
        config.setMinimumIdle(2);
        config.setIdleTimeout(30000);
        config.setConnectionTimeout(30000);
        config.setMaxLifetime(1800000);
        return new HikariDataSource(config);
    }

    /**
     * 单位数据实体类
     */
    public static class UnitData {
        private String id;
        private String parentId;
        private String unitName;
        private String unitCode;
        private String unitType;
        private String status;
        private String createBy;
        private String createAt;
        private String updateBy;
        private String updateAt;
        private String deletedFlag;
        private int level; // 层级深度

        // 构造函数
        public UnitData(String id, String parentId, String unitName, String unitCode, 
                       String unitType, String status, String createBy, String createAt,
                       String updateBy, String updateAt, String deletedFlag, int level) {
            this.id = id;
            this.parentId = parentId;
            this.unitName = unitName;
            this.unitCode = unitCode;
            this.unitType = unitType;
            this.status = status;
            this.createBy = createBy;
            this.createAt = createAt;
            this.updateBy = updateBy;
            this.updateAt = updateAt;
            this.deletedFlag = deletedFlag;
            this.level = level;
        }

        // Getters
        public String getId() { return id; }
        public String getParentId() { return parentId; }
        public String getUnitName() { return unitName; }
        public String getUnitCode() { return unitCode; }
        public String getUnitType() { return unitType; }
        public String getStatus() { return status; }
        public String getCreateBy() { return createBy; }
        public String getCreateAt() { return createAt; }
        public String getUpdateBy() { return updateBy; }
        public String getUpdateAt() { return updateAt; }
        public String getDeletedFlag() { return deletedFlag; }
        public int getLevel() { return level; }

        @Override
        public String toString() {
            return String.format("UnitData{id='%s', parentId='%s', unitName='%s', level=%d}", 
                               id, parentId, unitName, level);
        }
    }

    /**
     * 查询指定ID的所有下级单位
     */
    public List<UnitData> findAllSubUnits(String rootUnitId) {
        List<UnitData> allSubUnits = new ArrayList<>();
        
        try (Connection connection = dataSource.getConnection()) {
            // 首先查询根单位信息
            UnitData rootUnit = findUnitById(connection, rootUnitId);
            if (rootUnit == null) {
                logger.warn("未找到ID为 {} 的单位", rootUnitId);
                return allSubUnits;
            }
            
            logger.info("找到根单位: {}", rootUnit);
            allSubUnits.add(rootUnit);
            
            // 递归查询所有下级单位
            findSubUnitsRecursive(connection, rootUnitId, 1, allSubUnits);
            
            logger.info("总共找到 {} 个单位（包括根单位）", allSubUnits.size());
            
        } catch (SQLException e) {
            logger.error("查询单位层级结构时发生错误", e);
        }
        
        return allSubUnits;
    }

    /**
     * 根据ID查询单位信息
     */
    private UnitData findUnitById(Connection connection, String unitId) throws SQLException {
        String sql = "SELECT id, parent_id, unit_name, unit_code, unit_type, status, " +
                    "create_by, create_at, update_by, update_at, deleted_flag " +
                    "FROM unit WHERE id = ? AND deleted_flag = '0'";
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, unitId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                if (rs.next()) {
                    return new UnitData(
                        rs.getString("id"),
                        rs.getString("parent_id"),
                        rs.getString("unit_name"),
                        rs.getString("unit_code"),
                        rs.getString("unit_type"),
                        rs.getString("status"),
                        rs.getString("create_by"),
                        rs.getString("create_at"),
                        rs.getString("update_by"),
                        rs.getString("update_at"),
                        rs.getString("deleted_flag"),
                        0 // 根单位层级为0
                    );
                }
            }
        }
        
        return null;
    }

    /**
     * 递归查询下级单位
     */
    private void findSubUnitsRecursive(Connection connection, String parentId, int level, List<UnitData> result) throws SQLException {
        String sql = "SELECT id, parent_id, unit_name, unit_code, unit_type, status, " +
                    "create_by, create_at, update_by, update_at, deleted_flag " +
                    "FROM unit WHERE parent_id = ? AND deleted_flag = '0' ORDER BY unit_code";
        
        try (PreparedStatement stmt = connection.prepareStatement(sql)) {
            stmt.setString(1, parentId);
            
            try (ResultSet rs = stmt.executeQuery()) {
                while (rs.next()) {
                    UnitData subUnit = new UnitData(
                        rs.getString("id"),
                        rs.getString("parent_id"),
                        rs.getString("unit_name"),
                        rs.getString("unit_code"),
                        rs.getString("unit_type"),
                        rs.getString("status"),
                        rs.getString("create_by"),
                        rs.getString("create_at"),
                        rs.getString("update_by"),
                        rs.getString("update_at"),
                        rs.getString("deleted_flag"),
                        level
                    );
                    
                    result.add(subUnit);
                    logger.debug("找到{}级下级单位: {}", level, subUnit);
                    
                    // 递归查询当前单位的下级单位
                    findSubUnitsRecursive(connection, subUnit.getId(), level + 1, result);
                }
            }
        }
    }

    /**
     * 生成INSERT SQL语句
     */
    public List<String> generateInsertSql(List<UnitData> units, String tableName) {
        List<String> sqlStatements = new ArrayList<>();
        
        String[] fields = {
            "id", "parent_id", "unit_name", "unit_code", "unit_type", "status",
            "create_by", "create_at", "update_by", "update_at", "deleted_flag"
        };
        
        String fieldsPart = String.join(", ", fields);
        
        for (UnitData unit : units) {
            StringBuilder sql = new StringBuilder();
            sql.append("INSERT INTO ").append(tableName).append(" (").append(fieldsPart).append(") VALUES (");
            
            sql.append("'").append(unit.getId()).append("', ");
            sql.append(unit.getParentId() != null ? "'" + unit.getParentId() + "'" : "NULL").append(", ");
            sql.append("'").append(escapeSqlString(unit.getUnitName())).append("', ");
            sql.append("'").append(escapeSqlString(unit.getUnitCode())).append("', ");
            sql.append(unit.getUnitType() != null ? "'" + unit.getUnitType() + "'" : "NULL").append(", ");
            sql.append(unit.getStatus() != null ? "'" + unit.getStatus() + "'" : "NULL").append(", ");
            sql.append(unit.getCreateBy() != null ? "'" + unit.getCreateBy() + "'" : "NULL").append(", ");
            sql.append(unit.getCreateAt() != null ? "'" + unit.getCreateAt() + "'" : "NULL").append(", ");
            sql.append(unit.getUpdateBy() != null ? "'" + unit.getUpdateBy() + "'" : "NULL").append(", ");
            sql.append(unit.getUpdateAt() != null ? "'" + unit.getUpdateAt() + "'" : "NULL").append(", ");
            sql.append("'").append(unit.getDeletedFlag()).append("'");
            
            sql.append(")");
            
            sqlStatements.add(sql.toString());
        }
        
        return sqlStatements;
    }

    /**
     * 转义SQL字符串中的特殊字符
     */
    private String escapeSqlString(String str) {
        if (str == null) return "";
        return str.replace("'", "''").replace("\\", "\\\\");
    }

    /**
     * 保存SQL到文件
     */
    public void saveSqlToFile(List<String> sqlStatements, String filePath) throws IOException {
        try (FileWriter writer = new FileWriter(filePath, false)) {
            for (String sql : sqlStatements) {
                writer.write(sql);
                writer.write(";\n");
            }
        }
        logger.info("SQL文件已保存到: {}", filePath);
    }

    /**
     * 关闭数据源
     */
    public void close() {
        if (dataSource != null && !dataSource.isClosed()) {
            dataSource.close();
        }
    }

    /**
     * 主方法 - 查询指定ID的所有下级单位并生成SQL
     */
    public static void main(String[] args) {
        String targetUnitId = "1008730763140530176";
        String outputPath = "E:\\output\\unit_hierarchy_" + targetUnitId + ".sql";
        String tableName = "pam_basedata.unit";
        
        UnitHierarchyQueryTool tool = new UnitHierarchyQueryTool();
        
        try {
            logger.info("开始查询单位ID {} 的所有下级单位...", targetUnitId);
            
            // 查询所有下级单位
            List<UnitData> allUnits = tool.findAllSubUnits(targetUnitId);
            
            if (allUnits.isEmpty()) {
                logger.warn("未找到任何单位数据");
                return;
            }
            
            // 打印层级结构
//            logger.info("单位层级结构:");
//            for (UnitData unit : allUnits) {
//                String indent = "  ".repeat(unit.getLevel());
//                logger.info("{}[{}] {} (ID: {})", indent, unit.getLevel(), unit.getUnitName(), unit.getId());
//            }
            
            // 生成SQL
            List<String> sqlStatements = tool.generateInsertSql(allUnits, tableName);
            
            // 保存到文件
            tool.saveSqlToFile(sqlStatements, outputPath);
            
            logger.info("查询完成！共找到 {} 个单位，生成 {} 条SQL语句", allUnits.size(), sqlStatements.size());
            logger.info("SQL文件已保存到: {}", outputPath);
            
        } catch (Exception e) {
            logger.error("执行过程中发生错误", e);
        } finally {
            tool.close();
        }
    }
}
