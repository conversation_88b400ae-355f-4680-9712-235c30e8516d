<?xml version="1.0" encoding="UTF-8"?>
<configuration>

    <!-- 定义全局变量 -->
    <property name="LOG_DIR" value="logs" />
    <property name="LOG_PATTERN" value="%d [%thread] %-5level %logger{36} [%file : %line] - %msg%n" />

    <!-- 控制台输出配置 -->
    <appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender">

        <!-- 输出模式 -->
        <encoder>
            <pattern>${LOG_PATTERN}</pattern>
        </encoder>
    </appender>

    <!--  &lt;!&ndash; 文件输出配置（日志轮转） &ndash;&gt;
      <appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender">
          <file>${LOG_DIR}/application.log</file>
          <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
              &lt;!&ndash; 每天生成一个新的日志文件 &ndash;&gt;
              <fileNamePattern>${LOG_DIR}/application.%d{yyyy-MM-dd}.log</fileNamePattern>
              &lt;!&ndash; 保留30天的日志 &ndash;&gt;
              <maxHistory>30</maxHistory>
          </rollingPolicy>
          <encoder>
              <pattern>${LOG_PATTERN}</pattern>
          </encoder>
      </appender>-->

    <!-- 日志级别及输出配置 -->
    <logger name="com.zaxxer.hikari" level="WARN" />
    <logger name="org.hibernate" level="WARN" />
    <logger name="org.springframework" level="WARN" />

    <!-- 根日志记录器 -->
    <root level="INFO">
        <appender-ref ref="CONSOLE" />
        <!-- <appender-ref ref="FILE" />-->
    </root>

    <!-- 开启DEBUG级别日志（可选） -->
    <logger name="DatabaseSearcher" level="DEBUG" />
</configuration>
