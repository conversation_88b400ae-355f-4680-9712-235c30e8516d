# 接口优先级分析工具 (Java 8 兼容版本)

## 📋 项目简介

这是一个专门为Spring Cloud微服务项目设计的接口性能监控和优先级分析工具。它能够：

- 📊 自动读取多个Excel监控文件
- 🧮 基于业界标准计算接口优先级得分
- 📈 生成全局TOP10优先修复接口列表
- 🎯 **生成每个服务的TOP10接口列表**
- 📑 输出带颜色标记的Excel分析报告
- 📝 **同时生成Markdown格式报告**
- 🎯 为两周巡检提供科学的决策依据

**✅ Java 8 完全兼容** - 本版本已针对Java 8进行优化，移除了所有Java 9+特性。

## 🏗️ 项目结构

```
org.example.analyzer/
├── InterfaceMetrics.java          # 数据模型类
├── PriorityConfig.java            # 配置参数类
├── ExcelMetricsReader.java        # Excel读取器
├── PriorityCalculator.java        # 优先级计算器
├── ExcelReportGenerator.java      # Excel报告生成器
├── MarkdownReportGenerator.java   # Markdown报告生成器
└── InterfacePriorityAnalyzer.java # 主程序类
```

## 🚀 快速开始

### 1. 环境要求

- **Java 8 或更高版本** (已针对Java 8优化)
- Maven 3.6 或更高版本

### 2. Java 8 兼容性说明

本项目已完全兼容Java 8，主要优化包括：

- ✅ **移除String.repeat()** - 使用`Collections.nCopies()`替代
- ✅ **移除Stream API高级特性** - 使用传统for循环和Collections.sort()
- ✅ **移除Lambda表达式** - 使用匿名内部类
- ✅ **保留Java 8时间API** - 使用LocalDateTime和DateTimeFormatter
- ✅ **兼容POI库** - 使用Apache POI 5.2.4版本

### 3. 配置路径

修改 `InterfacePriorityAnalyzer.java` 中的路径配置：

```java
// 修改为您的Excel文件目录路径
private static final String INPUT_DIRECTORY = "D:\\Users\\ex_wuyh42.CN\\Desktop\\生产环境-慢接口";

// 修改为您希望的输出文件路径
private static final String OUTPUT_FILE_PATH = "D:\\接口优先级分析报告_" + 
        LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss")) + ".xlsx";
```

### 4. 运行程序

```bash
# 编译项目
mvn clean compile

# 运行主程序
mvn exec:java -Dexec.mainClass="org.example.analyzer.InterfacePriorityAnalyzer"

# 或者在IDE中直接运行 InterfacePriorityAnalyzer.main() 方法
```

## 📊 输入文件格式

程序支持读取以下格式的Excel文件：

| 列名 | 说明 | 示例 |
|------|------|------|
| A: url | 接口地址 | /user/login |
| B: method | HTTP方法 | GET/POST |
| C: 次数 | 调用次数 | 1500 |
| D: 错误数 | 错误次数 | 15 |
| E: RT(ms) | 平均响应时间 | 2500.5 |
| F: 总时间(ms) | 累计时间 | 3750750 |
| G: 最大并发 | 最大并发数 | 5 |
| H: 最慢(ms) | 最慢响应时间 | 8500 |
| I: 最近错误 | 错误信息 | timeout error |
| J-O: 时间分布 | 各时间段请求数 | 0, 10, 500, 800, 190, 0 |
| P: 最大响应体大小 | 字节数 | 1024000 |
| Q-S: TP99/95/90 | 百分位数 | 5000, 3000, 2000 |
| T: 调用源 | 来源服务 | pam-gateway |
| U: 业务异常次数 | 业务异常 | 5 |

## 🎯 简化优先级计算公式

### 综合得分公式
```
最终得分 = 响应时间性能(40%) + 调用频率影响(25%) + 稳定性风险(20%) + 系统压力(15%)
```

### 基于核心指标的评分标准

#### 1. 响应时间性能 (40%)
**基于指标：** RT(ms)、最慢(ms)、响应时间分布(0-10ms到10s以上)

- **平均响应时间 (40%)**
  - >10秒: 10分 (严重)
  - 5-10秒: 8分 (高)
  - 2-5秒: 6分 (中等)
  - 0.5-2秒: 4分 (轻微)
  - <0.5秒: 2分 (正常)

- **最慢响应时间 (30%)**
  - >20秒: 10分 (极慢)
  - 10-20秒: 8分 (很慢)
  - 5-10秒: 6分 (慢)
  - 2-5秒: 4分 (偏慢)
  - <2秒: 2分 (正常)

- **响应时间分布 (30%)**
  - 10s以上占比>50%: 10分
  - 10s以上占比20-50%: 8分
  - 1-10s占比>60%: 6分
  - 500ms-1s占比>40%: 4分
  - 0-100ms占比>80%: 2分

#### 2. 调用频率影响 (25%)
**基于指标：** 次数、总时间(ms)

- **调用次数 (60%)**
  - >5000次: 10分 (超高频)
  - 1000-5000次: 8分 (高频)
  - 100-1000次: 6分 (中频)
  - 50-100次: 4分 (低频)
  - <50次: 2分 (极低频)

- **总时间消耗 (40%)**
  - 前10%: 10分 (资源消耗极大)
  - 前20%: 8分 (资源消耗较大)
  - 前50%: 6分 (资源消耗中等)
  - 其他: 4分 (资源消耗较小)

#### 3. 稳定性风险 (20%)
**基于指标：** 错误数、最近错误

- **错误率 (70%)**
  - >5%: 10分 (严重错误率)
  - 1-5%: 8分 (高错误率)
  - 0.1-1%: 6分 (中等错误率)
  - >0%: 4分 (轻微错误)
  - 0%: 2分 (无错误)

- **最近错误 (30%)**
  - 超时错误: 10分 (最严重)
  - 连接错误: 9分 (严重)
  - 其他错误: 7分 (一般)
  - 业务异常: 6分 (轻微)
  - 无错误: 2分 (正常)

#### 4. 系统压力 (15%)
**基于指标：** 最大并发、最大响应体大小(Bytes)

- **并发压力 (60%)**
  - >10: 10分 (高并发压力)
  - 5-10: 8分 (中等并发压力)
  - 2-5: 6分 (轻微并发压力)
  - >1: 4分 (有并发)
  - =1: 2分 (无并发压力)

- **响应体大小 (40%)**
  - >10MB: 10分 (超大响应体)
  - 1-10MB: 8分 (大响应体)
  - 100KB-1MB: 6分 (中等响应体)
  - 10KB-100KB: 4分 (小响应体)
  - <10KB: 2分 (很小响应体)

## 📈 输出报告

### 控制台输出
```
=================== TOP 10 优先修复接口 ===================
排名 服务模块    接口地址                               综合得分 调用次数 平均RT(ms) 错误率(%) 主要问题
1   gateway    /materialGet/getMaterialGetFromErp     9.50    8       302115     0.00     极高RT 超时
2   basedata   /bom/getBomFromErp                     9.20    1       140743     0.00     极高RT
3   gateway    /vendorAsl/getVendorAslFromErp         8.80    2       42239      0.00     极高RT
...
```

### 输出报告内容

#### Excel报告 (.xlsx)
1. **全局TOP 10 优先修复接口** - 跨所有服务的重点关注接口列表（红色高亮）
2. **各服务TOP 10接口** - 每个服务单独的TOP10接口工作表
3. **完整接口数据** - 所有接口的详细数据（按优先级排序）
4. **汇总分析** - 统计信息和分析结果，包含各服务TOP10汇总

#### Markdown报告 (.md)
1. **📋 基本信息** - 生成时间、总接口数、分析周期等
2. **📊 优先级分布** - 高中低优先级接口的数量和占比统计
3. **🚨 全局TOP 10 优先修复接口** - 带优先级图标的表格展示
4. **🔍 TOP 3 接口详细分析** - 最高优先级接口的详细问题分析
5. **🏢 各服务TOP 10 接口** - 按服务分组的优先级排序
6. **📈 汇总分析** - 性能问题统计和服务健康度排名
7. **📋 完整接口数据** - 前50条接口的详细数据表格

#### Markdown报告特色功能
- 🎨 **可视化图标** - 使用emoji图标直观显示优先级和问题类型
- 📱 **移动友好** - 在手机、平板等设备上也能良好显示
- 🔗 **易于分享** - 可直接在GitHub、GitLab等平台预览
- 📝 **版本控制** - 支持Git版本控制，便于跟踪历史变化
- 🖨️ **打印友好** - 可直接打印或导出为PDF
- 🔍 **搜索友好** - 纯文本格式，便于全文搜索

## ⚙️ 配置调整

所有配置参数都在 `PriorityConfig.java` 中，您可以根据实际需求调整：

```java
// 主要权重配置
public static final double BUSINESS_IMPACT_WEIGHT = 0.35;      // 业务影响权重
public static final double PERFORMANCE_SEVERITY_WEIGHT = 0.30; // 性能严重度权重
public static final double STABILITY_RISK_WEIGHT = 0.25;       // 稳定性风险权重
public static final double RESOURCE_CONSUMPTION_WEIGHT = 0.10; // 资源消耗权重

// 响应时间阈值
public static final double CRITICAL_RT_THRESHOLD = 30000;   // 30秒 - 紧急
public static final double SEVERE_RT_THRESHOLD = 10000;     // 10秒 - 严重
public static final double MODERATE_RT_THRESHOLD = 5000;    // 5秒 - 需改进

// 错误率阈值
public static final double CRITICAL_ERROR_RATE = 0.05;     // 5%
public static final double HIGH_ERROR_RATE = 0.01;         // 1%

// TOP N 数量
public static final int TOP_N_COUNT = 20;

// URL关键字过滤配置
public static final boolean ENABLE_URL_KEYWORD_FILTER = true;           // 启用URL关键字过滤
public static final String LOW_PRIORITY_URL_KEYWORDS = "export,import,select"; // 需要降权的关键字
public static final double LOW_PRIORITY_SCORE = 1.0;                    // 关键字匹配时的最低得分
```

### URL关键字过滤功能

新增的URL关键字过滤功能可以自动识别特定类型的接口并降低其优先级：

#### 功能特性
- **智能识别**：自动检测URL中包含的特定关键字
- **大小写不敏感**：支持大小写不敏感的关键字匹配
- **配置灵活**：支持多个关键字（逗号分隔），可随时调整
- **开关控制**：可通过配置开关启用/禁用此功能
- **权重降级**：匹配到关键字的接口权重自动降至最低（1.0分）

#### 使用场景
适用于需要降低以下类型接口优先级的场景：
- **数据导出接口**：如 `/user/export`、`/data/exportUsers`
- **数据导入接口**：如 `/file/import`、`/batch/importData`
- **查询选择接口**：如 `/query/select`、`/report/selectData`
- **其他批处理接口**：如 `/batch/download`、`/bulk/upload`

#### 配置示例
```java
// 启用功能并配置多个关键字
public static final boolean ENABLE_URL_KEYWORD_FILTER = true;
public static final String LOW_PRIORITY_URL_KEYWORDS = "export,import,select,download,upload,batch";
public static final double LOW_PRIORITY_SCORE = 0.5; // 可设置更低的分数
```

#### 匹配效果
配置关键字 `"export,import,select"` 后，以下URL都会被自动降权：
- `/api/EXPORT/users` ✅ (大写)
- `/service/Import/data` ✅ (首字母大写)
- `/query/Select/report` ✅ (混合大小写)
- `/user/exportData` ✅ (包含关键字)
- `/normal/api/endpoint` ❌ (不包含关键字，正常评分)

## 🔧 Java 8 兼容性特性

### 1. 字符串重复
```java
// Java 11+ 方式
"─".repeat(120)

// Java 8 兼容方式
String.join("", Collections.nCopies(120, "─"))
```

### 2. 集合排序
```java
// Java 8+ Stream API
list.stream().sorted(Comparator.comparing(...)).collect(Collectors.toList())

// Java 8 兼容方式
Collections.sort(list, new Comparator<InterfaceMetrics>() {
    @Override
    public int compare(InterfaceMetrics o1, InterfaceMetrics o2) {
        return Double.compare(o2.getFinalPriorityScore(), o1.getFinalPriorityScore());
    }
});
```

### 3. 文件过滤
```java
// Lambda 表达式
directory.listFiles((dir, name) -> name.endsWith(".xlsx"))

// Java 8 兼容方式
directory.listFiles(new FilenameFilter() {
    @Override
    public boolean accept(File dir, String name) {
        return name.endsWith(".xlsx");
    }
});
```

## 🐛 常见问题

### Q1: 程序运行时提示"找不到Excel文件"
**A:** 检查 `INPUT_DIRECTORY` 路径是否正确，确保目录存在且包含.xlsx文件

### Q2: Excel文件读取失败
**A:** 确保Excel文件格式正确，第一行为标题行，数据从第二行开始

### Q3: 生成的Excel报告打不开
**A:** 检查输出路径是否有写入权限，确保磁盘空间充足

### Q4: Excel工作表名称错误
**A:** 如果遇到"Invalid char found in sheet name"错误，说明文件名包含Excel不支持的字符。程序已自动处理常见情况，如遇问题请检查文件名格式

### Q5: Java版本兼容性问题
**A:** 本项目已完全兼容Java 8，如果遇到编译错误，请检查Java版本设置

## 📞 技术支持

如果您在使用过程中遇到问题，可以：

1. 检查控制台输出的错误信息
2. 确认输入文件格式是否正确
3. 验证Java 8环境是否正常
4. 查看生成的日志文件

## 📝 更新日志

### v1.5.0 (2025-01-25) - URL关键字过滤版本
- 🎯 **新增URL关键字过滤功能** - 支持根据URL关键字自动降低接口优先级
- 🔧 **智能关键字匹配** - 大小写不敏感的关键字检测，支持多关键字配置
- ⚙️ **灵活配置管理** - 可通过配置文件轻松调整关键字和开关状态
- 📊 **统计信息增强** - 在优先级分布中显示被关键字过滤的接口数量
- 🎛️ **权重降级机制** - 匹配关键字的接口自动设置为最低优先级得分
- 📝 **实时日志输出** - 控制台显示被降权的接口URL，便于监控过滤效果

### v1.4.0 (2025-01-18) - Markdown报告版本
- 📝 **新增Markdown报告** - 在Excel报告基础上同时生成Markdown格式报告
- 🎨 **可视化增强** - 使用emoji图标直观显示优先级和问题类型
- 📱 **多平台友好** - 支持GitHub、GitLab等平台预览，移动设备友好
- 🔍 **便于分享** - 纯文本格式便于版本控制和全文搜索
- 📊 **内容丰富** - 包含基本信息、优先级分布、详细分析、服务健康度等

### v1.3.0 (2025-01-18) - 简化评分规则版本
- 🎯 **简化评分模型** - 重新设计为4维度评分：响应时间性能(40%) + 调用频率影响(25%) + 稳定性风险(20%) + 系统压力(15%)
- 📊 **聚焦核心指标** - 只关注14个核心指标：次数、错误数、RT、总时间、最大并发、最慢RT、最近错误、响应时间分布、最大响应体大小
- ✅ **优化计算逻辑** - 移除复杂的业务类型判断，基于纯数据指标进行评分
- 📈 **提升评分精度** - 更直观的评分标准，更准确地反映接口性能问题

### v1.2.1 (2025-01-18) - 修复版本
- 🐛 **修复Excel工作表命名错误** - 自动清理文件名中的特殊字符
- ✅ **增强名称安全性** - 确保工作表名称符合Excel规范
- ✅ **改进错误处理** - 避免因文件名特殊字符导致的崩溃

### v1.2.0 (2025-01-18) - 增强版本
- ✅ **新增每个Excel前10记录功能** - 每个服务单独计算TOP10接口
- ✅ **增强Excel报告** - 为每个服务创建独立的TOP10工作表
- ✅ **优化控制台输出** - 显示全局TOP10和各服务TOP10
- ✅ **改进汇总分析** - 包含各服务TOP10接口汇总信息
- ✅ **保持Java 8兼容** - 所有新功能均兼容Java 8

### v1.1.0 (2025-01-18) - Java 8 兼容版本
- ✅ **完全兼容Java 8** - 移除所有Java 9+特性
- ✅ **优化字符串处理** - 使用Collections.nCopies替代String.repeat
- ✅ **重构集合操作** - 使用传统循环替代Stream API高级特性
- ✅ **移除Lambda表达式** - 使用匿名内部类确保兼容性
- ✅ **保持功能完整** - 所有原有功能保持不变

### v1.0.0 (2025-01-18)
- ✅ 实现基础的Excel文件读取功能
- ✅ 完成综合优先级计算算法
- ✅ 支持生成带颜色标记的Excel报告
- ✅ 提供详细的控制台输出
- ✅ 支持多服务模块数据合并分析