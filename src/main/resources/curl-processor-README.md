# 监控平台接口调用日志导出工具

## 📋 项目简介

✅ **项目状态：已完成并可用**

这是一个专门用于自动化处理监控平台接口调用日志导出的工具。经过完整测试验证，现已能够：

- 📁 自动读取指定目录下的所有 `.txt` 文件（包含cURL请求）
- 🔍 完整解析cURL命令，包括Cookie（-b参数）、请求头、完整JSON请求体
- 🌐 执行HTTP请求下载Excel文件，与手动cURL命令效果完全一致
- 💾 智能命名并保存到指定目录
- ⚠️ 遇到错误立即停止，提供详细错误信息
- 📊 生成完整的处理报告

## 🎉 成功案例

### 验证结果
- ✅ **SimpleCurlExecutor**: 成功下载Excel文件（108,235字节）
- ✅ **CurlProcessorMain**: 成功解析cURL文件并下载Excel文件
- ✅ **一致性验证**: 两种方式产生相同的HTTP请求和响应结果

## 🏗️ 项目结构

```
org.example.analyzer.curl/
├── DownloadConfig.java           # 配置参数类
├── CurlCommand.java              # cURL命令数据模型
├── CurlProcessException.java     # 自定义异常类
├── CurlParser.java               # cURL命令解析器
├── HttpRequestExecutor.java      # HTTP请求执行器
├── FileDownloadManager.java      # 文件下载管理器
├── CurlFileProcessor.java        # 主处理器（核心类）
└── CurlProcessorMain.java        # 示例主程序
```

## 🚀 快速开始

### 1. 环境要求

- **Java 17 或更高版本**
- Maven 3.6 或更高版本
- 网络连接（用于下载Excel文件）

### 2. 依赖说明

项目使用以下主要依赖：
- **OkHttp 4.12.0** - HTTP客户端，用于执行cURL请求
- **Apache POI** - Excel文件处理（项目已有）
- **SLF4J + Logback** - 日志框架（项目已有）

### 3. 配置路径

修改 `CurlProcessorMain.java` 中的路径配置：

```java
// 修改为您的cURL文件目录路径
String curlDirectory = "D:\\curl-files";

// 修改为您希望的Excel文件保存路径
String downloadDirectory = "D:\\Users\\ex_wuyh42.CN\\Desktop\\生产环境-慢接口\\auto-export";
```

### 4. 运行程序

```bash
# 编译项目
mvn clean compile

# 运行主程序（使用默认路径）
mvn exec:java -Dexec.mainClass="org.example.analyzer.curl.CurlProcessorMain"

# 或者指定自定义路径
mvn exec:java -Dexec.mainClass="org.example.analyzer.curl.CurlProcessorMain" -Dexec.args="D:\my-curl-files D:\my-downloads"

# 或者在IDE中直接运行 CurlProcessorMain.main() 方法
```

### 5. 成功运行示例

```
=== 监控平台接口调用日志导出工具 ===
配置信息: DownloadConfig{curlDirectory='...', downloadDirectory='...'}

=== cURL文件批量处理开始 ===
找到 2 个cURL文件

处理进度: [1/2] basedata-export.txt
解析结果: CurlCommand{url='https://monitor.midea.com/...', method='POST', headers=19 items, hasBody=true}
添加请求头: Cookie = [HIDDEN]
添加请求头: Content-Type = application/json
请求体长度: 2847 字符
发送HTTP请求到: https://monitor.midea.com/xops/web/monitor/view/v1/table/export
响应状态码: 200
Content-Type: application/vnd.ms-excel;charset=UTF-8
✓ 文件下载成功!
文件路径: D:\...\basedata_export_2025-07-25_11-15-30.xlsx
文件大小: 108235 字节

✓ 所有文件处理完成！
```

## 📄 输入文件格式

### cURL文件要求

- **文件格式**: `.txt` 文件
- **文件内容**: 每个文件包含一个完整的cURL命令
- **文件命名**: 建议使用有意义的名称，如 `service-a-curl.txt`、`user-service-curl.txt`

### cURL命令示例

```bash
curl 'https://monitor.midea.com/xops/web/monitor/view/v1/table/export' \
  -H 'Accept: application/json, text/plain, */*' \
  -H 'Content-Type: application/json' \
  -b 'mip_curr_lang=zh-CN; MAS_TGC=eyJhbGciOiJIUzUxMiJ9...; midea_sso_token=SRwXaRCgVFGC...' \
  -H 'Origin: https://monitor.midea.com' \
  -H 'Referer: https://monitor.midea.com/monitoring/...' \
  --data-raw '{"storageObject":{"objectType":"env","objectId":"..."},"tableName":"sys-java-url.detail",...}'
```

**重要特性确认：**
- ✅ **Cookie支持**: 正确解析 `-b` 参数并添加到请求头
- ✅ **完整JSON**: 能够解析包含嵌套对象的复杂JSON请求体
- ✅ **请求头完整性**: 保持所有请求头的顺序和格式
- ✅ **与手动cURL一致**: 程序发送的请求与手动执行cURL命令完全相同

## 📊 输出文件格式

### 文件命名规则

下载的Excel文件将按以下规则命名：
```
{服务标识}_{原始文件名}_{时间戳}.xlsx
```

示例：
- 输入文件: `user-service-curl.txt`
- 输出文件: `user-service_url详情[汇总_ 2025-07-25 09_32_00]_2025-07-25_14-30-15.xlsx`

### 文件存储

- 所有下载的Excel文件统一保存到配置的下载目录
- 自动处理文件名冲突（添加序号后缀）
- 自动创建不存在的目录

## ⚙️ 配置选项

### DownloadConfig 配置参数

```java
DownloadConfig config = new DownloadConfig();
config.setCurlDirectory("D:\\curl-files");                    // cURL文件目录
config.setDownloadDirectory("D:\\downloads");                 // 下载目录
config.setConnectionTimeout(30000);                           // 连接超时（毫秒）
config.setReadTimeout(120000);                                // 读取超时（毫秒）
config.setEnableVerboseLogging(true);                         // 启用详细日志
```

## 🔧 核心功能

### 1. cURL解析功能

- 自动提取URL、HTTP方法、请求头、请求体
- 支持多行cURL命令（使用反斜杠续行）
- 智能识别POST/GET等HTTP方法
- 完整保留认证token和其他请求头

### 2. HTTP请求执行

- 使用OkHttp客户端执行请求
- 支持各种HTTP方法（GET、POST、PUT、DELETE）
- 自动处理重定向
- 验证响应内容类型（确保是Excel文件）

### 3. 文件下载管理

- 智能文件命名（从cURL文件名提取服务标识）
- 自动处理文件名冲突
- 支持大文件下载（流式处理）
- 自动创建目录结构

### 4. 错误处理

- **立即停止策略**: 遇到任何错误立即停止处理
- **详细错误报告**: 提供错误类型、原因、来源文件等信息
- **异常分类**: 区分解析错误、网络错误、文件IO错误等
- **堆栈跟踪**: 开发模式下提供完整的错误堆栈

## 📈 处理流程

```
1. 扫描cURL文件目录 → 获取所有.txt文件
2. 逐个读取文件内容 → 获取cURL命令字符串
3. 解析cURL命令 → 提取请求参数
4. 执行HTTP请求 → 下载Excel文件
5. 智能文件命名 → 保存到指定目录
6. 生成处理报告 → 显示成功/失败统计
```

## 🔧 故障排除

### 常见问题及解决方案

#### Q1: HTTP 400错误
**症状**: 程序显示"HTTP请求失败，状态码: 400"
**解决方案**:
1. **检查cURL文件格式**: 确保.txt文件包含完整的cURL命令
2. **验证JSON请求体**: 检查 `--data-raw` 后的JSON是否完整且格式正确
3. **确认Cookie有效性**: 验证 `-b` 参数中的认证信息是否过期
4. **对比手动执行**: 先在命令行手动执行cURL命令验证是否正常

#### Q2: 请求体解析不完整
**症状**: 日志显示"请求体长度: 1 字符"
**解决方案**:
1. 检查cURL命令中的引号匹配是否正确
2. 确保JSON没有被截断
3. 验证 `--data-raw '...'` 格式正确

#### Q3: 程序提示"找不到cURL文件"
**解决方案**: 检查 `curlDirectory` 路径是否正确，确保目录存在且包含.txt文件

#### Q4: 文件保存失败
**解决方案**: 检查下载目录是否有写入权限，确保磁盘空间充足

### 调试技巧

1. **启用详细日志**: 确保 `enableVerboseLogging=true`
2. **对比SimpleCurlExecutor**: 使用固定参数的SimpleCurlExecutor验证网络和认证
3. **检查请求详情**: 查看日志中的请求头数量和请求体长度
4. **验证响应**: 检查Content-Type是否为Excel格式

## 🔍 调试模式

启用详细日志以获取更多调试信息：

```java
config.setEnableVerboseLogging(true);
```

这将输出：
- cURL解析详情
- HTTP请求详情
- 文件下载进度
- 详细错误堆栈

## 📞 技术支持

如果您在使用过程中遇到问题，可以：

1. 检查控制台输出的错误信息
2. 启用详细日志模式查看更多信息
3. 验证cURL命令格式和网络连接
4. 检查文件权限和磁盘空间

## 🛠️ 技术实现详情

### HTTP客户端配置
- **最终采用**: `new OkHttpClient()` 默认配置
- **原因**: 与SimpleCurlExecutor保持完全一致，避免配置差异导致的请求失败
- **弃用**: 复杂的超时和重定向配置，简化为默认行为

### 请求体解析改进
- **问题**: 正则表达式无法正确处理包含嵌套引号的复杂JSON
- **解决方案**: 采用手动解析方法
  ```java
  // 使用 lastIndexOf('\'') 找到JSON结束位置
  int dataEnd = curlCommand.lastIndexOf('\'');
  String result = curlCommand.substring(dataStart, dataEnd);
  ```
- **验证**: 确保提取的内容以 `{` 开始，`}` 结束

### 关键对齐修改
1. **HTTP客户端**: 从复杂配置改为 `new OkHttpClient()`
2. **请求构建**: 直接在executeRequest中构建，移除单独的buildRequest方法
3. **RequestBody创建**: 使用 `RequestBody.create(mediaType, body)` 格式
4. **文件保存**: 简化为直接保存，移除复杂的响应分析器
5. **调试输出**: 添加与SimpleCurlExecutor相同的日志格式

### Cookie和认证处理
- **-b参数支持**: 正确解析Cookie参数并添加到请求头
- **请求头顺序**: 保持与原始cURL命令相同的顺序
- **认证信息**: 完整保留所有认证相关的请求头

## 📝 更新日志

### v1.0.0 (2025-07-25) - 已完成并可用
- ✅ **cURL解析功能** - 完整支持Cookie(-b)、请求头(-H)、JSON请求体(--data-raw)
- ✅ **HTTP请求执行** - 与手动cURL命令完全一致的请求发送
- ✅ **文件下载管理** - 成功下载Excel文件并智能命名
- ✅ **错误处理机制** - 立即停止策略和详细错误报告
- ✅ **处理报告生成** - 完整的成功/失败统计
- ✅ **一致性验证** - SimpleCurlExecutor和CurlProcessorMain产生相同结果
