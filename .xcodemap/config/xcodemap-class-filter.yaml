autoDetectedPackages:
- org.my.util
enableAutoDetect: true
funcDisplayConfig:
  skipConstructors: false
  skipFieldAccess: true
  skipFieldChange: true
  skipGetters: true
  skipNonProjectPackages: true
  skipPrivateMethods: false
  skipSetters: true
ignoreSameClassCall: null
ignoreSamePackageCall: null
includedPackagePrefixes: null
includedParentClasses: null
name: xcodemap-filter
recordMode: smart
sourceDisplayConfig:
  color: blue
startOnDebug: false
